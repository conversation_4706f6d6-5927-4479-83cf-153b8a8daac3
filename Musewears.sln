
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 25.0.1705.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Musewears.Server", "Musewears\Server\Musewears.Server.csproj", "{E9408EE6-EB62-4E7B-91D8-5ABB922BEEBC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Musewears.Client", "Musewears\Client\Musewears.Client.csproj", "{815EBA09-25C9-4947-8921-ED357EDF079E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "build", "build", "{D65D2405-8254-4EBD-8A1A-439FD725BD79}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Catalog.API", "Musewears\Catalog.API\Catalog.API.csproj", "{201D0E98-8FFB-4DC7-A261-EA4540CBB7EC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WebAppComponents", "Musewears\WebAppComponents\WebAppComponents.csproj", "{EE4B7774-86F0-423B-BD5B-14E55A6C198F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Basket.API", "Musewears\Basket.API\Basket.API.csproj", "{1B70ED27-2924-4BD0-BA78-615CD6A778BE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AppHost", "Musewears\AppHost\AppHost.csproj", "{7FE77718-CBA1-4489-8041-E1AAEC21A106}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ServiceDefaults", "Musewears\ServiceDefaults\ServiceDefaults.csproj", "{894ADA57-1772-410C-9018-569925C67962}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Admin", "Musewears\Admin\Admin.csproj", "{C5C1A0FC-F355-464B-AC84-8C6CC90999DA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ordering.API", "Musewears\Ordering\Ordering.API\Ordering.API.csproj", "{5159D35F-5FC2-4C52-AD99-DA291252516A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ordering.Domain", "Musewears\Ordering\Ordering.Domain\Ordering.Domain.csproj", "{587A9212-3E18-44B4-B16F-16C48D3F1D01}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ordering.Infrastructure", "Musewears\Ordering\Ordering.Infrastructure\Ordering.Infrastructure.csproj", "{7E81CC97-5CC5-4412-B264-EFD37742D41B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OrderProcessor", "Musewears\Ordering\OrderProcessor\OrderProcessor.csproj", "{E1C37DB8-5E9D-4CAD-8C70-E72E3DF7D97F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DomainEventLogEF", "Musewears\DomainEventLogEF\DomainEventLogEF.csproj", "{6DB6CA1D-6B0F-417C-819F-3952D44A168A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EventBus", "Musewears\EventBus\EventBus.csproj", "{A1C37178-2AF2-4878-9093-F0A3C6E84D96}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EventBusRabbitMQ", "Musewears\EventBusRabbitMQ\EventBusRabbitMQ.csproj", "{1FE698D4-C6C0-4C4A-AA1D-4129034B5C19}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DomainEventBus", "Musewears\DomainEventBus\DomainEventBus.csproj", "{2FE698D4-C6C0-4C4A-AA1D-4129034B5C20}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ordering.UnitTests", "Musewears\Ordering\Ordering.UnitTests\Ordering.UnitTests.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Server.UnitTests", "Musewears\Server.UnitTests\Server.UnitTests.csproj", "{B2C3D4E5-F6G7-8901-BCDE-F23456789012}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E9408EE6-EB62-4E7B-91D8-5ABB922BEEBC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E9408EE6-EB62-4E7B-91D8-5ABB922BEEBC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E9408EE6-EB62-4E7B-91D8-5ABB922BEEBC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E9408EE6-EB62-4E7B-91D8-5ABB922BEEBC}.Release|Any CPU.Build.0 = Release|Any CPU
		{815EBA09-25C9-4947-8921-ED357EDF079E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{815EBA09-25C9-4947-8921-ED357EDF079E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{815EBA09-25C9-4947-8921-ED357EDF079E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{815EBA09-25C9-4947-8921-ED357EDF079E}.Release|Any CPU.Build.0 = Release|Any CPU
		{274726C1-179F-409C-91A7-48C6BA185F37}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{274726C1-179F-409C-91A7-48C6BA185F37}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{274726C1-179F-409C-91A7-48C6BA185F37}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{274726C1-179F-409C-91A7-48C6BA185F37}.Release|Any CPU.Build.0 = Release|Any CPU
		{0E356690-8D11-4C71-BBC4-0F1D8C536B4E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0E356690-8D11-4C71-BBC4-0F1D8C536B4E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0E356690-8D11-4C71-BBC4-0F1D8C536B4E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0E356690-8D11-4C71-BBC4-0F1D8C536B4E}.Release|Any CPU.Build.0 = Release|Any CPU
		{201D0E98-8FFB-4DC7-A261-EA4540CBB7EC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{201D0E98-8FFB-4DC7-A261-EA4540CBB7EC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{201D0E98-8FFB-4DC7-A261-EA4540CBB7EC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{201D0E98-8FFB-4DC7-A261-EA4540CBB7EC}.Release|Any CPU.Build.0 = Release|Any CPU
		{EE4B7774-86F0-423B-BD5B-14E55A6C198F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EE4B7774-86F0-423B-BD5B-14E55A6C198F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EE4B7774-86F0-423B-BD5B-14E55A6C198F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EE4B7774-86F0-423B-BD5B-14E55A6C198F}.Release|Any CPU.Build.0 = Release|Any CPU
		{1B70ED27-2924-4BD0-BA78-615CD6A778BE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1B70ED27-2924-4BD0-BA78-615CD6A778BE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1B70ED27-2924-4BD0-BA78-615CD6A778BE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1B70ED27-2924-4BD0-BA78-615CD6A778BE}.Release|Any CPU.Build.0 = Release|Any CPU
		{7FE77718-CBA1-4489-8041-E1AAEC21A106}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7FE77718-CBA1-4489-8041-E1AAEC21A106}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7FE77718-CBA1-4489-8041-E1AAEC21A106}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7FE77718-CBA1-4489-8041-E1AAEC21A106}.Release|Any CPU.Build.0 = Release|Any CPU
		{894ADA57-1772-410C-9018-569925C67962}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{894ADA57-1772-410C-9018-569925C67962}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{894ADA57-1772-410C-9018-569925C67962}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{894ADA57-1772-410C-9018-569925C67962}.Release|Any CPU.Build.0 = Release|Any CPU
		{C5C1A0FC-F355-464B-AC84-8C6CC90999DA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C5C1A0FC-F355-464B-AC84-8C6CC90999DA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C5C1A0FC-F355-464B-AC84-8C6CC90999DA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C5C1A0FC-F355-464B-AC84-8C6CC90999DA}.Release|Any CPU.Build.0 = Release|Any CPU
		{5159D35F-5FC2-4C52-AD99-DA291252516A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5159D35F-5FC2-4C52-AD99-DA291252516A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5159D35F-5FC2-4C52-AD99-DA291252516A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5159D35F-5FC2-4C52-AD99-DA291252516A}.Release|Any CPU.Build.0 = Release|Any CPU
		{587A9212-3E18-44B4-B16F-16C48D3F1D01}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{587A9212-3E18-44B4-B16F-16C48D3F1D01}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{587A9212-3E18-44B4-B16F-16C48D3F1D01}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{587A9212-3E18-44B4-B16F-16C48D3F1D01}.Release|Any CPU.Build.0 = Release|Any CPU
		{7E81CC97-5CC5-4412-B264-EFD37742D41B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7E81CC97-5CC5-4412-B264-EFD37742D41B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7E81CC97-5CC5-4412-B264-EFD37742D41B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7E81CC97-5CC5-4412-B264-EFD37742D41B}.Release|Any CPU.Build.0 = Release|Any CPU
		{E1C37DB8-5E9D-4CAD-8C70-E72E3DF7D97F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E1C37DB8-5E9D-4CAD-8C70-E72E3DF7D97F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E1C37DB8-5E9D-4CAD-8C70-E72E3DF7D97F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E1C37DB8-5E9D-4CAD-8C70-E72E3DF7D97F}.Release|Any CPU.Build.0 = Release|Any CPU
		{6DB6CA1D-6B0F-417C-819F-3952D44A168A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6DB6CA1D-6B0F-417C-819F-3952D44A168A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6DB6CA1D-6B0F-417C-819F-3952D44A168A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6DB6CA1D-6B0F-417C-819F-3952D44A168A}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1C37178-2AF2-4878-9093-F0A3C6E84D96}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1C37178-2AF2-4878-9093-F0A3C6E84D96}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1C37178-2AF2-4878-9093-F0A3C6E84D96}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1C37178-2AF2-4878-9093-F0A3C6E84D96}.Release|Any CPU.Build.0 = Release|Any CPU
		{1FE698D4-C6C0-4C4A-AA1D-4129034B5C19}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1FE698D4-C6C0-4C4A-AA1D-4129034B5C19}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1FE698D4-C6C0-4C4A-AA1D-4129034B5C19}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1FE698D4-C6C0-4C4A-AA1D-4129034B5C19}.Release|Any CPU.Build.0 = Release|Any CPU
		{2FE698D4-C6C0-4C4A-AA1D-4129034B5C20}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2FE698D4-C6C0-4C4A-AA1D-4129034B5C20}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2FE698D4-C6C0-4C4A-AA1D-4129034B5C20}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2FE698D4-C6C0-4C4A-AA1D-4129034B5C20}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {EC06E7CC-1F9D-4DFF-B13C-F1FDF39A3904}
	EndGlobalSection
EndGlobal
