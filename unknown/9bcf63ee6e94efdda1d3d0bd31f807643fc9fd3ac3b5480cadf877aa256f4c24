using MediatR;
using Ordering.Domain.AggregatesModel.BuyerAggregate;
using Ordering.Domain.AggregatesModel.OrderAggregate;
using Ordering.Domain.AggregatesModel.TransactionAggregate;
using Ordering.Domain.Events;

namespace Ordering.API.Application.DomainEventHandlers;

/// <summary>
/// Domain event handler for TRANSACTION.CREATED events from Interswitch
/// Handles buyer creation, payment method setup, and order association
/// </summary>
public class TransactionCreatedDomainEventHandler(
    IOrderRepository orderRepository,
    IBuyerRepository buyerRepository,
    ITransactionRepository transactionRepository,
    ILogger<TransactionCreatedDomainEventHandler> logger)
    : INotificationHandler<TransactionCreatedDomainEvent>
{
    private readonly IOrderRepository _orderRepository = orderRepository ?? throw new ArgumentNullException(nameof(orderRepository));
    private readonly IBuyerRepository _buyerRepository = buyerRepository ?? throw new ArgumentNullException(nameof(buyerRepository));
    private readonly ITransactionRepository _transactionRepository = transactionRepository ?? throw new ArgumentNullException(nameof(transactionRepository));
    private readonly ILogger<TransactionCreatedDomainEventHandler> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    public async Task Handle(TransactionCreatedDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        var transaction = domainEvent.Transaction;
        
        _logger.LogInformation("Handling TransactionCreated event for payment ID {PaymentId}, merchant reference {MerchantReference}",
            transaction.PaymentId, transaction.MerchantReference);

        try
        {
            // Find the order by merchant reference (which contains the txn_ref)
            var order = await _orderRepository.GetByMerchantReferenceAsync(transaction.MerchantReference);
            
            if (order is not null)
            {
                // Associate transaction with order
                transaction.AssociateWithOrder(order.Id);

                // Update order with payment reference
                order.SetPaymentReference(transaction.PaymentReference);

                _logger.LogInformation("Associated transaction {PaymentId} with order {OrderId}",
                    transaction.PaymentId, order.Id);

                // Create or get buyer - try webhook customer info first, then fallback to order context
                if (!string.IsNullOrEmpty(transaction.MerchantCustomerId) &&
                    !string.IsNullOrEmpty(transaction.MerchantCustomerName))
                {
                    await CreateOrUpdateBuyerAsync(transaction, order);
                }
                else
                {
                    _logger.LogInformation("No customer information in webhook for transaction {PaymentId}. Creating buyer from order context.",
                        transaction.PaymentId);

                    // Create buyer and payment method using order context
                    await CreateBuyerFromOrderContextAsync(transaction, order);
                }
            }
            else
            {
                _logger.LogWarning("Order not found for merchant reference {MerchantReference}. Transaction {PaymentId} will be stored without order association.",
                    transaction.MerchantReference, transaction.PaymentId);
            }

            // Save changes
            await _transactionRepository.UnitOfWork.SaveEntitiesAsync(cancellationToken);
            
            _logger.LogInformation("Successfully processed TransactionCreated event for payment ID {PaymentId}",
                transaction.PaymentId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing TransactionCreated event for payment ID {PaymentId}",
                transaction.PaymentId);
            throw;
        }
    }

    private async Task CreateOrUpdateBuyerAsync(Transaction transaction, Order order)
    {
        var buyer = await _buyerRepository.FindAsync(transaction.MerchantCustomerId!);
        
        if (buyer is null)
        {
            buyer = new Buyer(transaction.MerchantCustomerId!, transaction.MerchantCustomerName!);
            _buyerRepository.Add(buyer);
            
            _logger.LogInformation("Created new buyer {BuyerId} for transaction {PaymentId}",
                transaction.MerchantCustomerId, transaction.PaymentId);
        }

        // Create payment method for this transaction
        var paymentMethod = buyer.VerifyOrAddInterSwitchPaymentMethod(
            alias: $"Interswitch-{transaction.PaymentId}",
            paymentReference: transaction.PaymentReference,
            cardHolderName: transaction.MerchantCustomerName!,
            orderId: order.Id);

        _logger.LogInformation("Created/verified payment method {PaymentMethodId} for buyer {BuyerId}",
            paymentMethod.Id, buyer.Id);

        await _orderRepository.UnitOfWork.SaveEntitiesAsync();
    }

    private async Task CreateBuyerFromOrderContextAsync(Transaction transaction, Order order)
    {
        // Generate a buyer ID from the order context
        // Since we don't have customer info from webhook, we'll use a default approach
        var buyerId = $"order-{order.Id}";
        var buyerName = $"Customer for Order {order.Id}";

        var buyer = await _buyerRepository.FindAsync(buyerId);

        if (buyer is null)
        {
            buyer = new Buyer(buyerId, buyerName);
            _buyerRepository.Add(buyer);

            _logger.LogInformation("Created new buyer {BuyerId} from order context for transaction {PaymentId}",
                buyerId, transaction.PaymentId);
        }

        // Create payment method for this transaction
        var paymentMethod = buyer.VerifyOrAddInterSwitchPaymentMethod(
            alias: $"Interswitch-{transaction.PaymentId}",
            paymentReference: transaction.PaymentReference,
            cardHolderName: buyerName,
            orderId: order.Id);

        _logger.LogInformation("Created/verified payment method {PaymentMethodId} for buyer {BuyerId} from order context",
            paymentMethod.Id, buyer.Id);

        await _orderRepository.UnitOfWork.SaveEntitiesAsync();
    }
}
