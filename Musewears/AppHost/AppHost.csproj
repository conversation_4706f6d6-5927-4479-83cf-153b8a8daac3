<Project Sdk="Microsoft.NET.Sdk">

    <Sdk Name="Aspire.AppHost.Sdk" Version="9.3.1"/>

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <UserSecretsId>93e7745e-c52e-4923-98fc-8e69f839629b</UserSecretsId>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Aspire.Hosting.AppHost" Version="9.3.1"/>
        <PackageReference Include="Aspire.Hosting.PostgreSQL" Version="9.3.1" />
        <PackageReference Include="Aspire.Hosting.Redis" Version="9.3.1" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Admin\Admin.csproj" />
      <ProjectReference Include="..\Server\Musewears.Server.csproj" />
    </ItemGroup>

</Project>
