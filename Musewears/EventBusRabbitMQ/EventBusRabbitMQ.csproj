<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <Compile Include="..\Shared\ActivityExtensions.cs" Link="ActivityExtensions.cs" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Aspire.RabbitMQ.Client" Version="9.3.0" />
        <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.5" />
        <PackageReference Include="Polly.Core" Version="8.5.2" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\EventBus\EventBus.csproj" />
        <ProjectReference Include="..\Shared\Shared.csproj" />
    </ItemGroup>
</Project>
