using Microsoft.AspNetCore.Components.Authorization;

namespace Admin.Middleware;

public class BlazorAuthenticationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<BlazorAuthenticationMiddleware> _logger;

    public BlazorAuthenticationMiddleware(RequestDelegate next, ILogger<BlazorAuthenticationMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in BlazorAuthenticationMiddleware");
            throw;
        }

        // Handle 401 responses for Blazor pages
        if (context.Response.StatusCode == 401 && !context.Response.HasStarted)
        {
            var path = context.Request.Path.Value?.ToLower();
            
            // If it's a Blazor page request (not API), redirect to login
            if (!string.IsNullOrEmpty(path) && 
                !path.StartsWith("/api") && 
                !path.StartsWith("/_blazor") &&
                !path.StartsWith("/login") &&
                !context.Request.Headers.ContainsKey("X-Requested-With"))
            {
                _logger.LogInformation("Redirecting unauthorized request from {Path} to login", path);
                context.Response.Redirect("/login");
            }
        }
    }
}
