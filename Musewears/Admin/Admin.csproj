<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Blazored.LocalStorage" Version="4.5.0" />
      <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6" />
      <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.12.1" />
      <PackageReference Include="Asp.Versioning.Http.Client" Version="8.1.0" />
      <PackageReference Include="Refit" Version="7.2.22" />
      <PackageReference Include="Refit.HttpClientFactory" Version="7.2.22" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\ServiceDefaults\ServiceDefaults.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Components\Shared\" />
      <Folder Include="Controllers\" />
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="Handlers\ApiVersionHandler.cs" />
      <Compile Remove="Authentication\CustomAuthenticationHandler.cs" />
    </ItemGroup>

    <ItemGroup>
      <Content Remove="Components\Pages\Test.razor" />
      <Content Remove="Components\Pages\OrderList.razor" />
      <Content Remove="Components\Pages\ProductList.razor" />
      <Content Remove="Components\Pages\UserList.razor" />
    </ItemGroup>

</Project>
