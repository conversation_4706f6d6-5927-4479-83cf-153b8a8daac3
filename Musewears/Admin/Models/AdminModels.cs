using System.ComponentModel.DataAnnotations;

namespace Admin.Models;

// Authentication Models
public class AdminLoginRequest
{
    public string Email { get; set; } = "";
    public string Password { get; set; } = "";
    public bool RememberMe { get; set; }
}

public class LoginResponse
{
    public bool Success { get; set; }
    public string Token { get; set; } = "";
    public string Message { get; set; } = "";
    public UserInfo? User { get; set; }
}

public class UserInfo
{
    public string Id { get; set; } = "";
    public string Email { get; set; } = "";
    public string UserName { get; set; } = "";
    public List<string> Roles { get; set; } = new();
}

public class RefreshTokenRequest
{
    public string Token { get; set; } = "";
}

// User Management Models
public class UserViewModel
{
    public string Id { get; set; } = "";
    public string? Email { get; set; }
    public string? UserName { get; set; }
    public string? Fullname { get; set; }
    public bool EmailConfirmed { get; set; }
    public bool LockoutEnabled { get; set; }
    public DateTimeOffset? LockoutEnd { get; set; }
    public List<string> Roles { get; set; } = new();
}

public class UserDetailViewModel
{
    public string Id { get; set; } = "";
    public string? Email { get; set; }
    public string? UserName { get; set; }
    public string? Fullname { get; set; }
    public string? PhoneNumber { get; set; }
    public bool EmailConfirmed { get; set; }
    public bool PhoneNumberConfirmed { get; set; }
    public bool TwoFactorEnabled { get; set; }
    public bool LockoutEnabled { get; set; }
    public DateTimeOffset? LockoutEnd { get; set; }
    public int AccessFailedCount { get; set; }
    public List<string> Roles { get; set; } = new();
}

public class CreateUserRequest
{
    [Required]
    [EmailAddress]
    public string Email { get; set; } = "";

    [Required]
    public string Fullname { get; set; } = "";

    public string? PhoneNumber { get; set; }

    [Required]
    [MinLength(6)]
    public string Password { get; set; } = "";

    public bool EmailConfirmed { get; set; } = false;

    public List<string> Roles { get; set; } = new();
}

public class UpdateUserRequest
{
    [Required]
    [EmailAddress]
    public string Email { get; set; } = "";

    [Required]
    public string Fullname { get; set; } = "";

    public string? PhoneNumber { get; set; }
    public bool EmailConfirmed { get; set; }
    public bool PhoneNumberConfirmed { get; set; }
    public bool TwoFactorEnabled { get; set; }
    public bool LockoutEnabled { get; set; }
}

public class AssignRolesRequest
{
    public List<string> Roles { get; set; } = new();
}

public class LockoutUserRequest
{
    public DateTimeOffset? LockoutEnd { get; set; }
    public string? Reason { get; set; }
}

public class ResetPasswordRequest
{
    [Required]
    [MinLength(6)]
    public string NewPassword { get; set; } = "";
}

public class UserOperationResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = "";
    public List<string> Errors { get; set; } = new();
}

public class RoleViewModel
{
    public string Id { get; set; } = "";
    public string Name { get; set; } = "";
    public int UserCount { get; set; }
}

// Pagination Models
public class PaginationRequest
{
    public int PageIndex { get; set; } = 0;
    public int PageSize { get; set; } = 10;
}

public class PaginatedItems<T>(int pageIndex, int pageSize, long count, List<T> data)
{
    public int PageIndex { get; set; } = pageIndex;
    public int PageSize { get; set; } = pageSize;
    public long Count { get; set; } = count;
    public List<T> Data { get; set; } = data;
}

// Catalog Models
public class CatalogItem
{
    public int Id { get; set; }
    public string VendorId { get; set; } = "";
    public string Name { get; set; } = "";
    public string Description { get; set; } = "";
    public decimal Price { get; set; }
    public string? PictureFileName { get; set; }
    public string? PictureUri { get; set; }
    public int CatalogTypeId { get; set; }
    public int CatalogBrandId { get; set; }
    public int AvailableStock { get; set; }
    public int RestockThreshold { get; set; }
    public int MaxStockThreshold { get; set; }
    public bool OnReorder { get; set; }
}

public class CreateCatalogItemRequest
{
    public string VendorId { get; set; } = "";
    public string Name { get; set; } = "";
    public string Description { get; set; } = "";
    public decimal Price { get; set; }
    public string? PictureFileName { get; set; }
    public int CatalogTypeId { get; set; }
    public int CatalogBrandId { get; set; }
    public int AvailableStock { get; set; }
    public int RestockThreshold { get; set; }
    public int MaxStockThreshold { get; set; }
}

public class CatalogAnalytics
{
    public int TotalItems { get; set; }
    public int TotalBrands { get; set; }
    public int TotalTypes { get; set; }
    public int LowStockItems { get; set; }
}

// Order Models
public class OrderViewModel
{
    public int Id { get; set; }
    public string Status { get; set; } = "";
    public string Description { get; set; } = "";
    public string Street { get; set; } = "";
    public string City { get; set; } = "";
    public string State { get; set; } = "";
    public string Zipcode { get; set; } = "";
    public string Country { get; set; } = "";
    public List<OrderItemViewModel> OrderItems { get; set; } = new();
    public decimal Total { get; set; }
    public DateTime Date { get; set; }
}

public class OrderItemViewModel
{
    public string ProductName { get; set; } = "";
    public int Units { get; set; }
    public decimal UnitPrice { get; set; }
    public string PictureUrl { get; set; } = "";
}

public class OrderSummary
{
    public int OrderNumber { get; set; }
    public DateTime Date { get; set; }
    public string Status { get; set; } = "";
    public decimal Total { get; set; }
}
