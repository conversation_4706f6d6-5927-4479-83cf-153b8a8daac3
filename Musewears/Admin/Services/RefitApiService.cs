using Admin.Api;
using Admin.Models;
using Blazored.LocalStorage;
using System.Net.Http.Headers;

namespace Admin.Services;

public class RefitApiService(
    IAdminAuthApi authApi,
    IUsersApi usersApi,
    ICatalogApi catalogApi,
    IOrdersApi ordersApi,
    ILocalStorageService localStorage)
{
    // Authentication Methods
    public async Task<LoginResponse> LoginAsync(AdminLoginRequest request)
    {
        return await authApi.LoginAsync(request);
    }

    public async Task<LoginResponse> RefreshTokenAsync(RefreshTokenRequest request)
    {
        return await authApi.RefreshTokenAsync(request);
    }

    // User Management Methods
    public async Task<PaginatedItems<UserViewModel>> GetUsersAsync(int pageIndex = 0, int pageSize = 10, string? search = null)
    {
        await SetAuthorizationAsync();
        return await usersApi.GetUsersAsync(pageIndex, pageSize, search);
    }

    public async Task<UserDetailViewModel> GetUserByIdAsync(string id)
    {
        await SetAuthorizationAsync();
        return await usersApi.GetUserByIdAsync(id);
    }

    public async Task<UserOperationResult> CreateUserAsync(CreateUserRequest request)
    {
        await SetAuthorizationAsync();
        return await usersApi.CreateUserAsync(request);
    }

    public async Task<UserOperationResult> UpdateUserAsync(string id, UpdateUserRequest request)
    {
        await SetAuthorizationAsync();
        return await usersApi.UpdateUserAsync(id, request);
    }

    public async Task<UserOperationResult> DeleteUserAsync(string id)
    {
        await SetAuthorizationAsync();
        return await usersApi.DeleteUserAsync(id);
    }

    public async Task<UserOperationResult> AssignRolesAsync(string id, AssignRolesRequest request)
    {
        await SetAuthorizationAsync();
        return await usersApi.AssignRolesAsync(id, request);
    }

    public async Task<UserOperationResult> LockoutUserAsync(string id, LockoutUserRequest request)
    {
        await SetAuthorizationAsync();
        return await usersApi.LockoutUserAsync(id, request);
    }

    public async Task<UserOperationResult> UnlockUserAsync(string id)
    {
        await SetAuthorizationAsync();
        return await usersApi.UnlockUserAsync(id);
    }

    public async Task<UserOperationResult> ResetPasswordAsync(string id, ResetPasswordRequest request)
    {
        await SetAuthorizationAsync();
        return await usersApi.ResetPasswordAsync(id, request);
    }

    public async Task<List<RoleViewModel>> GetRolesAsync()
    {
        await SetAuthorizationAsync();
        return await usersApi.GetRolesAsync();
    }

    // Catalog Methods
    public async Task<PaginatedItems<CatalogItem>> GetCatalogItemsAsync(int pageIndex = 0, int pageSize = 10, string? name = null, int? type = null, int? brand = null)
    {
        await SetAuthorizationAsync();
        return await catalogApi.GetItemsAsync(pageIndex, pageSize, name, type, brand);
    }

    public async Task CreateCatalogItemAsync(CreateCatalogItemRequest request)
    {
        await SetAuthorizationAsync();
        await catalogApi.CreateItemAsync(request);
    }

    public async Task UpdateCatalogItemAsync(int id, CatalogItem item)
    {
        await SetAuthorizationAsync();
        await catalogApi.UpdateItemAsync(id, item);
    }

    public async Task DeleteCatalogItemAsync(int id)
    {
        await SetAuthorizationAsync();
        await catalogApi.DeleteItemAsync(id);
    }

    public async Task<CatalogAnalytics> GetCatalogAnalyticsAsync()
    {
        await SetAuthorizationAsync();
        return await catalogApi.GetAnalyticsAsync();
    }

    // Order Methods
    public async Task<PaginatedItems<OrderSummary>> GetOrdersAsync(int pageIndex = 0, int pageSize = 10, string? status = null, DateTime? fromDate = null, DateTime? toDate = null)
    {
        await SetAuthorizationAsync();
        return await ordersApi.GetOrdersAsync(pageIndex, pageSize, status, fromDate, toDate);
    }

    public async Task<OrderViewModel> GetOrderByIdAsync(int id)
    {
        await SetAuthorizationAsync();
        return await ordersApi.GetOrderByIdAsync(id);
    }

    public async Task UpdateOrderStatusAsync(int id, object statusRequest)
    {
        await SetAuthorizationAsync();
        await ordersApi.UpdateOrderStatusAsync(id, statusRequest);
    }

    // Helper method to set authorization header
    private async Task SetAuthorizationAsync()
    {
        var token = await localStorage.GetItemAsync<string>("authToken");
        if (!string.IsNullOrEmpty(token))
        {
            // Note: Refit handles authorization headers differently
            // This would need to be implemented using a DelegatingHandler
            // For now, we'll implement this in the next step
        }
    }
}
