using Admin.Models;
using Refit;

namespace Admin.Api;

public interface IUsersApi
{
    [Get("/api/admin/users")]
    Task<PaginatedItems<UserViewModel>> GetUsersAsync(
        [Query] int pageIndex = 0,
        [Query] int pageSize = 10,
        [Query] string? search = null);

    [Get("/api/admin/users/{id}")]
    Task<UserDetailViewModel> GetUserByIdAsync(string id);

    [Post("/api/admin/users")]
    Task<UserOperationResult> CreateUserAsync([Body] CreateUserRequest request);

    [Put("/api/admin/users/{id}")]
    Task<UserOperationResult> UpdateUserAsync(string id, [Body] UpdateUserRequest request);

    [Delete("/api/admin/users/{id}")]
    Task<UserOperationResult> DeleteUserAsync(string id);

    [Post("/api/admin/users/{id}/roles")]
    Task<UserOperationResult> AssignRolesAsync(string id, [Body] AssignRolesRequest request);

    [Post("/api/admin/users/{id}/lockout")]
    Task<UserOperationResult> LockoutUserAsync(string id, [Body] LockoutUserRequest request);

    [Post("/api/admin/users/{id}/unlock")]
    Task<UserOperationResult> UnlockUserAsync(string id);

    [Post("/api/admin/users/{id}/reset-password")]
    Task<UserOperationResult> ResetPasswordAsync(string id, [Body] ResetPasswordRequest request);

    [Get("/api/admin/roles")]
    Task<List<RoleViewModel>> GetRolesAsync();
}
