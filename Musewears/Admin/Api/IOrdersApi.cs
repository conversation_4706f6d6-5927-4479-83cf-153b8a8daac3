using Admin.Models;
using Refit;

namespace Admin.Api;

public interface IOrdersApi
{
    [Get("/api/admin/orders")]
    Task<PaginatedItems<OrderSummary>> GetOrdersAsync(
        [Query] int pageIndex = 0,
        [Query] int pageSize = 10,
        [Query] string? status = null,
        [Query] DateTime? fromDate = null,
        [Query] DateTime? toDate = null);

    [Get("/api/admin/orders/{id}")]
    Task<OrderViewModel> GetOrderByIdAsync(int id);

    [Put("/api/admin/orders/{id}/status")]
    Task UpdateOrderStatusAsync(int id, [Body] object statusRequest);
}
