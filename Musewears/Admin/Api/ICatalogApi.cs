using Admin.Models;
using Refit;

namespace Admin.Api;

public interface ICatalogApi
{
    [Get("/api/admin/catalog/items")]
    Task<PaginatedItems<CatalogItem>> GetItemsAsync(
        [Query] int pageIndex = 0,
        [Query] int pageSize = 10,
        [Query] string? name = null,
        [Query] int? type = null,
        [Query] int? brand = null);

    [Post("/api/admin/catalog/items")]
    Task CreateItemAsync([Body] CreateCatalogItemRequest request);

    [Put("/api/admin/catalog/items/{id}")]
    Task UpdateItemAsync(int id, [Body] CatalogItem item);

    [Delete("/api/admin/catalog/items/{id}")]
    Task DeleteItemAsync(int id);

    [Get("/api/admin/catalog/analytics")]
    Task<CatalogAnalytics> GetAnalyticsAsync();
}
