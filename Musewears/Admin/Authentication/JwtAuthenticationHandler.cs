using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using System.Text.Encodings.Web;

namespace Admin.Authentication;

public class JwtAuthenticationHandler(
    IOptionsMonitor<AuthenticationSchemeOptions> options,
    ILoggerFactory logger,
    UrlEncoder encoder,
    AuthenticationStateProvider authenticationStateProvider)
    : AuthenticationHandler<AuthenticationSchemeOptions>(options, logger, encoder)
{
    protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        try
        {
            // Get the authentication state from our custom provider
            var authState = await authenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;

            if (user.Identity?.IsAuthenticated != true) return AuthenticateResult.NoResult();
            
            // Create authentication ticket with the user from our state provider
            var ticket = new AuthenticationTicket(user, Scheme.Name);
            return AuthenticateResult.Success(ticket);

        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error in JWT authentication handler");
            return AuthenticateResult.Fail("Authentication failed");
        }
    }

    protected override Task HandleChallengeAsync(AuthenticationProperties properties)
    {
        // For Blazor Server, redirect to login page
        var loginUrl = "/login";

        // If this is an AJAX request or API call, return 401
        if (Request.Headers.ContainsKey("X-Requested-With") ||
            Request.Path.StartsWithSegments("/api") ||
            Request.Headers.Accept.Any(h => h.Contains("application/json")))
        {
            Response.StatusCode = 401;
            return Task.CompletedTask;
        }

        // For regular page requests, redirect to login
        Response.Redirect(loginUrl);
        return Task.CompletedTask;
    }
}