namespace Admin.Authentication;

using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components.Authorization;
using Blazored.LocalStorage;
using System.IdentityModel.Tokens.Jwt;
using Microsoft.JSInterop;
using System.Collections.Concurrent;

public class AdminAuthenticationStateProvider(ILocalStorageService localStorage, HttpClient httpClient, IHttpContextAccessor httpContextAccessor)
    : AuthenticationStateProvider
{
    private readonly HttpClient _httpClient = httpClient;
    private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
    private AuthenticationState? _cachedAuthenticationState;
    private bool _isInitialized = false;
    private readonly object _lock = new object();
    private readonly string _instanceId = Guid.NewGuid().ToString("N")[..8];
    private string? _currentToken;

    // Static cache to persist authentication state across scoped instances
    private static readonly ConcurrentDictionary<string, AuthenticationState> _globalAuthCache = new();
    private static readonly ConcurrentDictionary<string, string> _globalTokenCache = new();
    private static readonly object _globalLock = new object();

    private string GetSessionKey()
    {
        // Use a combination of connection ID and user agent for session identification
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext != null)
        {
            var connectionId = httpContext.Connection.Id;
            var userAgent = httpContext.Request.Headers.UserAgent.ToString();
            return $"{connectionId}_{userAgent.GetHashCode()}";
        }
        return "default_session";
    }

    public override Task<AuthenticationState> GetAuthenticationStateAsync()
    {
        lock (_lock)
        {
            // First check local cache
            if (_cachedAuthenticationState != null)
            {
                Console.WriteLine($"AdminAuthenticationStateProvider[{_instanceId}]: Returning local cached authentication state - Authenticated: {_cachedAuthenticationState.User.Identity?.IsAuthenticated}");
                return Task.FromResult(_cachedAuthenticationState);
            }

            // Check global cache for this session
            var sessionKey = GetSessionKey();
            if (_globalAuthCache.TryGetValue(sessionKey, out var globalState))
            {
                Console.WriteLine($"AdminAuthenticationStateProvider[{_instanceId}]: Found global cached state for session {sessionKey} - Authenticated: {globalState.User.Identity?.IsAuthenticated}");
                _cachedAuthenticationState = globalState;
                return Task.FromResult(globalState);
            }

            // Always return anonymous user until explicitly authenticated
            // This avoids JavaScript interop issues during prerendering
            Console.WriteLine($"AdminAuthenticationStateProvider[{_instanceId}]: Returning anonymous user (no cached state for session {sessionKey})");
            var anonymousState = new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
            return Task.FromResult(anonymousState);
        }
    }

    public void NotifyUserAuthentication(string token)
    {
        AuthenticationState authState;
        lock (_lock)
        {
            var claims = ParseClaimsFromJwt(token);
            var identity = new ClaimsIdentity(claims, "jwt");
            var user = new ClaimsPrincipal(identity);

            Console.WriteLine($"NotifyUserAuthentication[{_instanceId}]: User authenticated: {user.Identity.IsAuthenticated}");
            Console.WriteLine($"NotifyUserAuthentication[{_instanceId}]: User roles: {string.Join(", ", user.FindAll("http://schemas.microsoft.com/ws/2008/06/identity/claims/role").Select(c => c.Value))}");

            // Store token for API calls
            _currentToken = token;

            // Update cached state and mark as initialized
            _cachedAuthenticationState = new AuthenticationState(user);
            _isInitialized = true;
            authState = _cachedAuthenticationState;

            // Store in global cache for session persistence
            var sessionKey = GetSessionKey();
            _globalAuthCache[sessionKey] = authState;
            _globalTokenCache[sessionKey] = token;

            Console.WriteLine($"NotifyUserAuthentication[{_instanceId}]: Cached state set for session {sessionKey} - Authenticated: {_cachedAuthenticationState.User.Identity?.IsAuthenticated}");
        }

        // Notify outside the lock to avoid potential deadlocks
        NotifyAuthenticationStateChanged(Task.FromResult(authState));
    }

    /// <summary>
    /// Initializes authentication state from localStorage after component has rendered.
    /// This method should be called from OnAfterRenderAsync to avoid JavaScript interop issues during prerendering.
    /// </summary>
    public async Task InitializeAuthenticationAsync()
    {
        bool shouldInitialize;
        lock (_lock)
        {
            shouldInitialize = !_isInitialized;
        }

        if (!shouldInitialize)
        {
            Console.WriteLine("InitializeAuthenticationAsync: Already initialized, skipping");
            return;
        }

        try
        {
            Console.WriteLine("InitializeAuthenticationAsync: Initializing authentication from localStorage");

            var token = await localStorage.GetItemAsync<string>("authToken");

            AuthenticationState newState;
            lock (_lock)
            {
                _isInitialized = true;

                if (string.IsNullOrEmpty(token))
                {
                    Console.WriteLine("InitializeAuthenticationAsync: No token found - user not authenticated");
                    _cachedAuthenticationState = new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
                }
                else
                {
                    var claims = ParseClaimsFromJwt(token);
                    var identity = new ClaimsIdentity(claims, "jwt");
                    var user = new ClaimsPrincipal(identity);

                    Console.WriteLine($"InitializeAuthenticationAsync: User authenticated: {user.Identity.IsAuthenticated}");
                    Console.WriteLine($"InitializeAuthenticationAsync: User roles: {string.Join(", ", user.FindAll("http://schemas.microsoft.com/ws/2008/06/identity/claims/role").Select(c => c.Value))}");

                    // Store token for API calls
                    _currentToken = token;

                    _cachedAuthenticationState = new AuthenticationState(user);

                    // Store in global cache
                    var sessionKey = GetSessionKey();
                    _globalAuthCache[sessionKey] = _cachedAuthenticationState;
                    _globalTokenCache[sessionKey] = token;
                }

                newState = _cachedAuthenticationState;
            }

            // Notify that authentication state has changed
            NotifyAuthenticationStateChanged(Task.FromResult(newState));
        }
        catch (Exception ex)
        {
            Console.WriteLine($"InitializeAuthenticationAsync: Error: {ex.Message}");

            AuthenticationState errorState;
            lock (_lock)
            {
                _cachedAuthenticationState = new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
                _isInitialized = true;
                errorState = _cachedAuthenticationState;
            }

            NotifyAuthenticationStateChanged(Task.FromResult(errorState));
        }
    }

    public async Task RefreshAuthenticationStateAsync()
    {
        Console.WriteLine("RefreshAuthenticationStateAsync: Refreshing authentication state");

        lock (_lock)
        {
            // Clear cached state and re-initialize
            _cachedAuthenticationState = null;
            _isInitialized = false;
        }

        await InitializeAuthenticationAsync();
    }

    public void NotifyUserLogout()
    {
        AuthenticationState logoutState;
        lock (_lock)
        {
            var anonymousUser = new ClaimsPrincipal(new ClaimsIdentity());
            // Clear cached state but keep initialized flag
            _cachedAuthenticationState = new AuthenticationState(anonymousUser);
            _currentToken = null;
            logoutState = _cachedAuthenticationState;

            // Clear from global cache
            var sessionKey = GetSessionKey();
            _globalAuthCache.TryRemove(sessionKey, out _);
            _globalTokenCache.TryRemove(sessionKey, out _);
        }

        Console.WriteLine($"NotifyUserLogout[{_instanceId}]: User logged out");
        NotifyAuthenticationStateChanged(Task.FromResult(logoutState));
    }

    /// <summary>
    /// Gets the current JWT token for API calls without requiring JavaScript interop.
    /// This method is safe to call during server-side rendering.
    /// </summary>
    public string? GetCurrentToken()
    {
        lock (_lock)
        {
            // First check local token
            if (!string.IsNullOrEmpty(_currentToken))
            {
                return _currentToken;
            }

            // Check global cache
            var sessionKey = GetSessionKey();
            if (_globalTokenCache.TryGetValue(sessionKey, out var cachedToken))
            {
                _currentToken = cachedToken;
                return _currentToken;
            }

            return null;
        }
    }



    private IEnumerable<Claim> ParseClaimsFromJwt(string jwt)
    {
        var handler = new JwtSecurityTokenHandler();
        var token = handler.ReadJwtToken(jwt);
        return token.Claims;
    }
}