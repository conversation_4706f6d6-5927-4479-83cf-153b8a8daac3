using System.Text;
using Admin.Api;
using Admin.Authentication;
using Admin.Components;
using Admin.Handlers;
using Admin.Middleware;
using Admin.Services;
using Blazored.LocalStorage;
using Microsoft.AspNetCore.Components.Authorization;
using Refit;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

// Add HTTP Context Accessor for authentication
builder.Services.AddHttpContextAccessor();

builder.Services.AddBlazoredLocalStorage();

// Register AdminAuthenticationStateProvider as scoped (required due to ILocalStorageService dependency)
builder.Services.AddScoped<AdminAuthenticationStateProvider>();
builder.Services.AddScoped<AuthenticationStateProvider>(provider => provider.GetRequiredService<AdminAuthenticationStateProvider>());

builder.Services.AddScoped<JwtAuthenticationHandler>();

// Register handlers
builder.Services.AddTransient<AuthorizationHandler>();

// Configure Refit APIs
var baseUrl = builder.Configuration["ApiSettings:BaseUrl"] ?? "https://localhost:7135";

// Configure HttpClientHandler to bypass SSL validation in development
var httpClientHandler = new HttpClientHandler();
if (builder.Environment.IsDevelopment())
{
    httpClientHandler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true;
}

// Auth API doesn't need authorization handler (for login)
builder.Services.AddRefitClient<IAdminAuthApi>()
    .ConfigureHttpClient(c =>
    {
        c.BaseAddress = new Uri(baseUrl);
        c.DefaultRequestHeaders.Add("Accept", "application/json");
    })
    .AddApiVersion(2.0)
    .ConfigurePrimaryHttpMessageHandler(() => httpClientHandler);

// Other APIs need authorization handler
builder.Services.AddRefitClient<IUsersApi>()
    .ConfigureHttpClient(c =>
    {
        c.BaseAddress = new Uri(baseUrl);
        c.DefaultRequestHeaders.Add("Accept", "application/json");
    })
    .AddApiVersion(2.0)
    .AddHttpMessageHandler<AuthorizationHandler>()
    .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
    {
        ServerCertificateCustomValidationCallback = builder.Environment.IsDevelopment()
            ? (message, cert, chain, errors) => true
            : null
    });

builder.Services.AddRefitClient<ICatalogApi>()
    .ConfigureHttpClient(c =>
    {
        c.BaseAddress = new Uri(baseUrl);
        c.DefaultRequestHeaders.Add("Accept", "application/json");
    })
    .AddApiVersion(2.0)
    .AddHttpMessageHandler<AuthorizationHandler>()
    .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
    {
        ServerCertificateCustomValidationCallback = builder.Environment.IsDevelopment()
            ? (message, cert, chain, errors) => true
            : null
    });

builder.Services.AddRefitClient<IOrdersApi>()
    .ConfigureHttpClient(c =>
    {
        c.BaseAddress = new Uri(baseUrl);
        c.DefaultRequestHeaders.Add("Accept", "application/json");
    })
    .AddApiVersion(2.0)
    .AddHttpMessageHandler<AuthorizationHandler>()
    .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
    {
        ServerCertificateCustomValidationCallback = builder.Environment.IsDevelopment()
            ? (message, cert, chain, errors) => true
            : null
    });

// Register the new Refit-based API service
builder.Services.AddScoped<RefitApiService>();

// Keep ApiService for backward compatibility during transition
builder.Services.AddHttpClient<ApiService>(client =>
{
    client.BaseAddress = new Uri(baseUrl);
    client.DefaultRequestHeaders.Add("Accept", "application/json");
})
.AddApiVersion(2.0)
.ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
{
    ServerCertificateCustomValidationCallback = builder.Environment.IsDevelopment()
        ? (message, cert, chain, errors) => true
        : null
});

// Services
builder.Services.AddScoped<ApiService>();

// Authentication Setup for Blazor Server with JWT
// Configure authentication to work with JWT tokens from AdminAuthenticationStateProvider
builder.Services.AddAuthentication(options =>
{
    options.DefaultScheme = "jwt";
    options.DefaultChallengeScheme = "jwt";
    options.DefaultAuthenticateScheme = "jwt";
})
.AddScheme<Microsoft.AspNetCore.Authentication.AuthenticationSchemeOptions, JwtAuthenticationHandler>("jwt", options => { });

// Authorization Policies
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("AdminOnly", policy =>
    {
        policy.RequireRole("Admin");
        policy.RequireAuthenticatedUser();
        policy.AddAuthenticationSchemes("jwt");
    });

    options.AddPolicy("ManagerOrAdmin", policy =>
    {
        policy.RequireRole("Admin", "Manager");
        policy.RequireAuthenticatedUser();
        policy.AddAuthenticationSchemes("jwt");
    });

    // Set default policy to require authentication
    options.DefaultPolicy = options.GetPolicy("AdminOnly")!;
});


var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseAuthentication();
app.UseAuthorization();

// Add custom middleware to handle Blazor authentication redirects
app.UseMiddleware<BlazorAuthenticationMiddleware>();

app.UseAntiforgery();

app.MapStaticAssets();
app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

app.Run();