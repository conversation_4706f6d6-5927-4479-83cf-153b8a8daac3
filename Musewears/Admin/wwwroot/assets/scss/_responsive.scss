
@media (max-width: 1900px) {
    .all-gallery-wrap .left .wrap-gallery-item {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1440px) and (max-width: 1900px) {
    .wg-goal {
        flex-wrap: wrap;
    }
}

@media (max-width: 1440px) {
    .tf-main-product {
        flex-direction: column;
        > div {
            width: 100%;
        }
        .tf-product-info-list {
            padding-left: 0;
            padding-top: 50px;
        }
      
    }
    .wg-chart-default,
    .wg-box {
        padding: 24px 15px;
    }
    .tf-section-4 {
        > div {
            grid-column: span 2 / span 2 !important;
        }
    }
    .tf-section-3 {
        grid-template-columns: repeat(4,minmax(0,1fr));
        > div {
            &:nth-child(2),
            &:nth-child(1) {
                grid-column: span 2 / span 2 !important;
            }
            &:nth-child(3) {
                grid-column: span 4 / span 4 !important;
            }
        }
    }
    .tf-section-5 {
        > div {
            &:nth-child(1) {
                grid-column: span 8 / span 8 !important;
            }
            &:nth-child(2) {
                grid-column: span 4 / span 4 !important;
            }
            &:nth-child(3) {
                grid-column: span 4 / span 4 !important;
            }
        }
    }
    .all-gallery-wrap {
        flex-wrap: wrap;
        .right {
            max-width: unset;
            .image {
                text-align: center;
            }
        }
    }
}

@media (max-width: 1200px) {
    .tf-section-5 {
        > div {
            &:nth-child(2),
            &:nth-child(3) {
                grid-column: span 8 / span 8 !important;
            }
        }
    }
    .tf-section-8,
    .tf-section-3 {
        > div {
            &:nth-child(2),
            &:nth-child(1) {
                grid-column: span 4 / span 4 !important;
            }
        }
    }
    .tf-section-2 {
        > div {
            grid-column: span 2 / span 2 !important;
        }
    }
    .tf-section-7 {
        > div {
            &:nth-child(2),
            &:nth-child(1) {
                grid-column: span 3 / span 3 !important;
            }
        }
    }
    .tf-section-6 {
        > div {
            &:nth-child(2),
            &:nth-child(1) {
                grid-column: span 8 / span 8 !important;
            }
        }
    }
    .all-gallery-wrap {
        .left {
            .wrap-gallery-item {
                @include grid(3,1fr,24px,14px);
            }
        }
    }
    .form-style-2 { 
        .left {
            max-width: 150px !important;
        }
    }
    .header-user {
        width: unset;
        > div {
            &:last-child {
                display: none;
            }
        }
    }
    .new-page-wrap {
        flex-wrap: wrap;
        .right {
            max-width: unset;
        }
    }
    .upload-image {
        flex-wrap: wrap;
        .item {
            width: 48%;
        }
    }
}

@media (max-width: 1024px) {
    .wg-goal {
        .left {
            min-width: unset;
        }
    }   
}

@media (max-width: 991px) {
    .layout-wrap {
        &.full-width {
            .section-menu-left {
                left: 0;
                .button-show-hide {
                    transform: rotate(0deg);
                }
                .box-logo {
                    left: 0;
                }
            }
            .section-content-right {
                .header-dashboard {
                    padding-left: 15px !important;
                }
            }
        }
        .section-menu-left {
            left: -100%;
            .button-show-hide {
                transform: rotate(180deg);
            }
            .box-logo {
                background-color: #fff;
                border-color: #fff;
                left: -100%;
            }
        }
        .section-content-right {
            .main-content {
                padding-left: 0 !important;
                .main-content-inner {
                    padding-left: 15px;
                    padding-right: 15px;
                }
            }
            .header-dashboard {
                width: 100% !important;
                padding-left: 15px !important;
                left: 0 !important;
                .wrap {
                    .button-show-hide {
                        display: block;
                    }
                    .form-search {
                        display: none;
                    }
                    .header-left {
                        > a {
                            display: block;
                        }
                    }
                    .header-grid {
                        > .divider,
                        > .user,
                        > .apps,
                        > .button-dark-light,
                        > .button-zoom-maximize,
                        > .message,
                        > .noti,
                        > .country {
                            display: none;
                        }
                    }
                }
            }
        }
    }
    .tf-section-6 {
        > div {
            &:nth-child(1) {
                grid-column: span 8 / span 8 !important;
            }
            &:nth-child(2) {
                grid-column: span 8 / span 8 !important;
            }
        }
    }
    #line-chart-9 {
        margin-bottom: 0;
    }
    .swiper-button-next, 
    .swiper-button-prev {
        display: none;
    }
}

@media (max-width: 767px) {
    .form-style-1 {
        > * {
            flex-wrap: wrap;
            > * {
                &:last-child {
                    width: 100%;
                }
            }
        }
    }
    .form-style-2 {
        > * {
            flex-wrap: wrap;
        }
    }
    .wg-order-detail {
        flex-wrap: wrap;
        .right {
            max-width: unset;
        }
    }
    .wg-filter,
    .order-track {
        flex-wrap: wrap;
    }
    .all-gallery-wrap {
        .left {
            .wrap-gallery-item {
                @include grid(1,1fr,24px,14px);
            }
        }
    }
    .form-style-2 { 
        .left {
            max-width: unset !important;
        }
    } 
    .tf-product-info-delivery-return {
        flex-wrap: wrap;
    }
    .tf-product-btn-wishlist {
        order: 1;
    }
    .thumbs-slider {
        flex-direction: column;
        .tf-product-media-thumbs {
            order: 1;
            width: 100%;
            .swiper-slide {
                width: 130px;
            }
        }
        .tf-product-media-main {
            width: 100%;
            .item {
                height: 700px;
            }
        }
    }
}

@media (max-width: 600px) {
    .tf-section-4 {
        > div {
            grid-column: span 4 / span 4 !important;
        }
    }
    .w-half {
        width: 100% !important;
    }
    form {
        .cols {
            flex-wrap: wrap;
        }
    }
    .flex-wrap-mobile {
        flex-wrap: wrap;
    }
    .road-map {
        flex-wrap: wrap;
        gap: 30px;
        .road-map-item {
            &::before {
                display: none;
            }
        }
    }
    .mobile-wrap {
        flex-wrap: wrap;
    }
    .wrap-login-page {
        padding-top: 30px;
        padding-left: 15px;
        padding-right: 15px;
    }
    .upload-image {
        .item {
            width: 100%;
        }
    }
}