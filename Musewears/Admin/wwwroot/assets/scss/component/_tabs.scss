.widget-tabs {
    .widget-menu-tab {
        @include d-flex;
        gap: 26px;
        padding-bottom: 14px;
        border-bottom: 1px solid #EDF1F5;
        margin-bottom: 24px;
        li {
            cursor: pointer;
            position: relative;
            * {
                color: var(--Note);
            }
            &.active {
                * {
                    color: var(--Main);
                }
                &::after {
                    position: absolute;
                    content: '';
                    width: 100%;
                    height: 1px;
                    left: 0;
                    bottom: -15px;
                    background-color: var(--Main);
                    
                }
            }
        }
        &.style-1 {
            padding: 8px;
            border-radius: 14px;
            gap: 10px;
            background-color: #ECF0F4;
            border: 0;
            .item-title {
                border-radius: 14px;
                padding: 12px 20px;
                width: 100%;
                text-align: center;
                &::after {
                    display: none;
                }
                &.active {
                    background-color: #fff;
                    .body-title {
                        color: #2377FC;
                    }
                }
                .body-title {
                    color: #111111;
                }
            }
        }
    }
}