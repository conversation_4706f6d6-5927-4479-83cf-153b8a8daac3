.header {
    position: absolute;
    z-index: 10;
    top: 30px;
    width: 100%;
    .header-inner {

        .header-inner-wrap {
            @include flex(center,space-between);
            .header-left {
                padding-left: 5px;
            }
            .header-right {
                @include flex(center,center);
                gap: 20px;
                .header-news {
                    color: var(--Body-Text);
                    font-size: 16px;
                    font-weight: 800;
                    line-height: 21px;
                    text-transform: capitalize;
                }
                .header-button {
                    @include flex(center,center);
                    gap: 8px;
                }
            }
        }
    }
}

.header-dashboard {
    position: fixed;
    top: 0;
    right: 0;
    width: calc(100% - 280px);
    height: 80px;
    padding: 15px 13px 15px 30px;
    background: var(--White);
    box-shadow: 0px 4px 24px 0px rgba(20, 25, 38, 0.05);
    z-index: 19;
    @include transition3;
    .wrap {
        @include flex(center,space-between);
        gap: 15px;
        height: 100%;
        .header-left {
            width: 100%;
            max-width: 780px;
            position: relative;
            @include d-flex;
            align-items: center;
            gap: 15px;
            > a {
                display: none;
            }
            .button-show-hide {
                font-size: 24px;
                color: #94A3B8;
                cursor: pointer;
                @include transition3;
                transform: rotate(180deg);
                display: none;
            }
            .box-content-search {
                position: absolute;
                top: 50px;
                left: 0;
                right: 0;
                border-radius: 14px;
                padding: 16px;
                background-color: var(--White);
                box-shadow: 0px 4px 24px 2px rgba(20, 25, 38, 0.05);
                height: 538px;
                overflow-y: scroll;
                opacity: 0;
                visibility: hidden;

                &.active {
                    top: 55px;
                    opacity: 1;
                    visibility: visible;
                }

                &::-webkit-scrollbar {
                    width: 3px;
                }
                .product-item {
                    .name {
                        a {
                            color: var(--Heading);
                        }
                    }
                }
            }
        }
        .header-grid {
            @include d-flex;
            gap: 20px;
            > .divider {
                width: 1px;
                background: #ECF0F4;
                height: unset;
            }
            > .setting {
                width: 24px;
                @include flex(center,center);
                font-size: 24px;
                i {
                    animation-name: spin;
                    animation-duration: 3s;
                    animation-iteration-count: infinite;
                    animation-timing-function: linear;
                }
            }
        }
        .header-item {
            @include flex(center,center);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(203, 213, 225, 0.30);
            i {
                font-size: 20px;
                color: var(--Heading);
            }
            &.country {
                > .dropdown {
                    > .dropdown-menu.show {
                        margin-top: 19px !important;
                    }
                }
            }
        }
    }
}