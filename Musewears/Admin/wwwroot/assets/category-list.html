<!DOCTYPE html>
<!--[if IE 8 ]><html class="ie" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en-US" lang="en-US"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!-->
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en-US" lang="en-US">
<!--<![endif]-->

<head>
    <!-- Basic Page Needs -->
    <meta charset="utf-8">
    <!--[if IE]><meta http-equiv='X-UA-Compatible' content='IE=edge,chrome=1'><![endif]-->
    <title>Remos eCommerce Admin Dashboard HTML Template</title>

    <meta name="author" content="themesflat.com">

    <!-- Mobile Specific Metas -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">

    <!-- Theme Style -->
    <link rel="stylesheet" type="text/css" href="css/animate.min.css">
    <link rel="stylesheet" type="text/css" href="css/animation.css">
    <link rel="stylesheet" type="text/css" href="css/bootstrap.css">
    <link rel="stylesheet" type="text/css" href="css/bootstrap-select.min.css">
    <link rel="stylesheet" type="text/css" href="css/style.css">



    <!-- Font -->
    <link rel="stylesheet" href="font/fonts.css">

    <!-- Icon -->
    <link rel="stylesheet" href="icon/style.css">

    <!-- Favicon and Touch Icons  -->
    <link rel="shortcut icon" href="images/favicon.png">
    <link rel="apple-touch-icon-precomposed" href="images/favicon.png">

</head>

<body class="body">

    <!-- #wrapper -->
    <div id="wrapper">
        <!-- #page -->
        <div id="page" class="">
            <!-- layout-wrap -->
           <div class="layout-wrap">
                <!-- preload -->
                <div id="preload" class="preload-container">
                    <div class="preloading">
                        <span></span>
                    </div>
                </div>
                <!-- /preload -->
                <!-- section-menu-left -->
                <div class="section-menu-left">
                    <div class="box-logo">
                        <a href="index.html" id="site-logo-inner">
                            <img class="" id="logo_header" alt="" src="images/logo/logo.png" data-light="images/logo/logo.png" data-dark="images/logo/logo-dark.png" >
                        </a>
                        <div class="button-show-hide">
                            <i class="icon-menu-left"></i>
                        </div>
                    </div>
                    <div class="section-menu-left-wrap">
                        <div class="center">
                            <div class="center-item">
                                <div class="center-heading">Main Home</div>
                                <ul class="menu-list">
                                    <li class="menu-item has-children">
                                        <a href="javascript:void(0);" class="menu-item-button">
                                            <div class="icon"><i class="icon-grid"></i></div>
                                            <div class="text">Dashboard</div>
                                        </a>
                                        <ul class="sub-menu">
                                            <li class="sub-menu-item">
                                                <a href="index.html" class="">
                                                    <div class="text">Home 01</div>
                                                </a>
                                            </li>
                                            <li class="sub-menu-item">
                                                <a href="home-2.html" class="">
                                                    <div class="text">Home 02</div>
                                                </a>
                                            </li>
                                            <li class="sub-menu-item">
                                                <a href="home-3.html" class="">
                                                    <div class="text">Home 03</div>
                                                </a>
                                            </li>
                                            <li class="sub-menu-item">
                                                <a href="home-4.html" class="">
                                                    <div class="text">Home 04</div>
                                                </a>
                                            </li>
                                            <li class="sub-menu-item">
                                                <a href="home-boxed.html" class="">
                                                    <div class="text">Home Boxed</div>
                                                </a>
                                            </li>
                                            <li class="sub-menu-item">
                                                <a href="home-menu-icon-hover.html" class="">
                                                    <div class="text">Home Menu Icon Hover</div>
                                                </a>
                                            </li>
                                            <li class="sub-menu-item">
                                                <a href="home-menu-icon-default.html" class="">
                                                    <div class="text">Home Menu Icon Default</div>
                                                </a>
                                            </li>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                            <div class="center-item">
                                <div class="center-heading">All page</div>
                                <ul class="menu-list">
                                    <li class="menu-item has-children">
                                        <a href="javascript:void(0);" class="menu-item-button">
                                            <div class="icon"><i class="icon-shopping-cart"></i></div>
                                            <div class="text">Ecommerce</div>
                                        </a>
                                        <ul class="sub-menu">
                                            <li class="sub-menu-item">
                                                <a href="add-product.html" class="">
                                                    <div class="text">Add Product</div>
                                                </a>
                                            </li>
                                            <li class="sub-menu-item">
                                                <a href="product-list.html" class="">
                                                    <div class="text">Product List</div>
                                                </a>
                                            </li>
                                            <li class="sub-menu-item">
                                                <a href="product-detail-1.html" class="">
                                                    <div class="text">Product Detail 1</div>
                                                </a>
                                            </li>
                                            <li class="sub-menu-item">
                                                <a href="product-detail-2.html" class="">
                                                    <div class="text">Product Detail 2</div>
                                                </a>
                                            </li>
                                            <li class="sub-menu-item">
                                                <a href="product-detail-3.html" class="">
                                                    <div class="text">Product Detail 3</div>
                                                </a>
                                            </li>
                                        </ul>
                                    </li>
                                    <li class="menu-item has-children active">
                                        <a href="javascript:void(0);" class="menu-item-button">
                                            <div class="icon"><i class="icon-layers"></i></div>
                                            <div class="text">Category</div>
                                        </a>
                                        <ul class="sub-menu" style="display: block;">
                                            <li class="sub-menu-item">
                                                <a href="category-list.html" class="active">
                                                    <div class="text">Category list</div>
                                                </a>
                                            </li>
                                            <li class="sub-menu-item">
                                                <a href="new-category.html" class="">
                                                    <div class="text">New category</div>
                                                </a>
                                            </li>
                                        </ul>
                                    </li>
                                    <li class="menu-item has-children">
                                        <a href="javascript:void(0);" class="menu-item-button">
                                            <div class="icon"><i class="icon-box"></i></div>
                                            <div class="text">Attributes</div>
                                        </a>
                                        <ul class="sub-menu">
                                            <li class="sub-menu-item">
                                                <a href="attributes.html" class="">
                                                    <div class="text">Attributes</div>
                                                </a>
                                            </li>
                                            <li class="sub-menu-item">
                                                <a href="add-attributes.html" class="">
                                                    <div class="text">Add attributes</div>
                                                </a>
                                            </li>
                                        </ul>
                                    </li>
                                    <li class="menu-item has-children">
                                        <a href="javascript:void(0);" class="menu-item-button">
                                            <div class="icon"><i class="icon-file-plus"></i></div>
                                            <div class="text">Order</div>
                                        </a>
                                        <ul class="sub-menu">
                                            <li class="sub-menu-item">
                                                <a href="oder-list.html" class="">
                                                    <div class="text">Order list</div>
                                                </a>
                                            </li>
                                            <li class="sub-menu-item">
                                                <a href="oder-detail.html" class="">
                                                    <div class="text">Order detail</div>
                                                </a>
                                            </li>
                                            <li class="sub-menu-item">
                                                <a href="oder-tracking.html" class="">
                                                    <div class="text">Order tracking</div>
                                                </a>
                                            </li>
                                        </ul>
                                    </li>
                                    <li class="menu-item has-children">
                                        <a href="javascript:void(0);" class="menu-item-button">
                                            <div class="icon"><i class="icon-user"></i></div>
                                            <div class="text">User</div>
                                        </a>
                                        <ul class="sub-menu">
                                            <li class="sub-menu-item">
                                                <a href="all-user.html" class="">
                                                    <div class="text">All user</div>
                                                </a>
                                            </li>
                                            <li class="sub-menu-item">
                                                <a href="add-new-user.html" class="">
                                                    <div class="text">Add new user</div>
                                                </a>
                                            </li>
                                            <li class="sub-menu-item">
                                                <a href="login.html" class="">
                                                    <div class="text">Login</div>
                                                </a>
                                            </li>
                                            <li class="sub-menu-item">
                                                <a href="sign-up.html" class="">
                                                    <div class="text">Sign up</div>
                                                </a>
                                            </li>
                                        </ul>
                                    </li>
                                    <li class="menu-item has-children">
                                        <a href="javascript:void(0);" class="menu-item-button">
                                            <div class="icon"><i class="icon-user-plus"></i></div>
                                            <div class="text">Roles</div>
                                        </a>
                                        <ul class="sub-menu">
                                            <li class="sub-menu-item">
                                                <a href="all-roles.html" class="">
                                                    <div class="text">All roles</div>
                                                </a>
                                            </li>
                                            <li class="sub-menu-item">
                                                <a href="create-role.html" class="">
                                                    <div class="text">Create role</div>
                                                </a>
                                            </li>
                                        </ul>
                                    </li>
                                    <li class="menu-item">
                                        <a href="gallery.html" class="">
                                            <div class="icon"><i class="icon-image"></i></div>
                                            <div class="text">Gallery</div>
                                        </a>
                                    </li>
                                    <li class="menu-item">
                                        <a href="report.html" class="">
                                            <div class="icon"><i class="icon-pie-chart"></i></div>
                                            <div class="text">Report</div>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="center-item">
                                <div class="center-heading">Setting</div>
                                <ul class="menu-list">
                                    <li class="menu-item has-children">
                                        <a href="javascript:void(0);" class="menu-item-button">
                                            <div class="icon"><i class="icon-map-pin"></i></div>
                                            <div class="text">Location</div>
                                        </a>
                                        <ul class="sub-menu">
                                            <li class="sub-menu-item">
                                                <a href="countries.html" class="">
                                                    <div class="text">Countries</div>
                                                </a>
                                            </li>
                                            <li class="sub-menu-item">
                                                <a href="states.html" class="">
                                                    <div class="text">States</div>
                                                </a>
                                            </li>
                                            <li class="sub-menu-item">
                                                <a href="cities.html" class="">
                                                    <div class="text">Cities</div>
                                                </a>
                                            </li>
                                        </ul>
                                    </li>
                                    <li class="menu-item">
                                        <a href="setting.html" class="">
                                            <div class="icon"><i class="icon-settings"></i></div>
                                            <div class="text">Setting</div>
                                        </a>
                                    </li>
                                    <li class="menu-item has-children">
                                        <a href="javascript:void(0);" class="menu-item-button">
                                            <div class="icon"><i class="icon-edit"></i></div>
                                            <div class="text">Pages</div>
                                        </a>
                                        <ul class="sub-menu">
                                            <li class="sub-menu-item">
                                                <a href="list-page.html" class="">
                                                    <div class="text">List page</div>
                                                </a>
                                            </li>
                                            <li class="sub-menu-item">
                                                <a href="new-page.html" class="">
                                                    <div class="text">New page</div>
                                                </a>
                                            </li>
                                            <li class="sub-menu-item">
                                                <a href="edit-page.html" class="">
                                                    <div class="text">Edit page</div>
                                                </a>
                                            </li>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                          <div class="center-item">
                                <div class="center-heading">Components</div>
                                <ul class="menu-list">
                                    <li class="menu-item">
                                        <a href="components.html" class="">
                                            <div class="icon"><i class="icon-database"></i></div>
                                            <div class="text">Components</div>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="center-item">
                                <div class="center-heading">Support</div>
                                <ul class="menu-list">
                                    <li class="menu-item">
                                        <a href="#" class="">
                                            <div class="icon"><i class="icon-help-circle"></i></div>
                                            <div class="text">Help center</div>
                                        </a>
                                    </li>
                                    <li class="menu-item">
                                        <a href="#" class="">
                                            <div class="icon">
                                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M15.7727 4.27031C15.025 3.51514 14.1357 2.91486 13.1558 2.50383C12.1758 2.09281 11.1244 1.87912 10.0617 1.875H10C7.84512 1.875 5.77849 2.73102 4.25476 4.25476C2.73102 5.77849 1.875 7.84512 1.875 10V14.375C1.875 14.8723 2.07254 15.3492 2.42417 15.7008C2.77581 16.0525 3.25272 16.25 3.75 16.25H5C5.49728 16.25 5.9742 16.0525 6.32583 15.7008C6.67746 15.3492 6.875 14.8723 6.875 14.375V11.25C6.875 10.7527 6.67746 10.2758 6.32583 9.92417C5.9742 9.57254 5.49728 9.375 5 9.375H3.15313C3.27366 8.07182 3.76315 6.83 4.56424 5.79508C5.36532 4.76016 6.44481 3.97502 7.67617 3.53169C8.90753 3.08836 10.2398 3.0052 11.5167 3.29196C12.7936 3.57872 13.9624 4.22352 14.8859 5.15078C16.0148 6.28539 16.7091 7.78052 16.8477 9.375H15C14.5027 9.375 14.0258 9.57254 13.6742 9.92417C13.3225 10.2758 13.125 10.7527 13.125 11.25V14.375C13.125 14.8723 13.3225 15.3492 13.6742 15.7008C14.0258 16.0525 14.5027 16.25 15 16.25H16.875C16.875 16.7473 16.6775 17.2242 16.3258 17.5758C15.9742 17.9275 15.4973 18.125 15 18.125H10.625C10.4592 18.125 10.3003 18.1908 10.1831 18.3081C10.0658 18.4253 10 18.5842 10 18.75C10 18.9158 10.0658 19.0747 10.1831 19.1919C10.3003 19.3092 10.4592 19.375 10.625 19.375H15C15.8288 19.375 16.6237 19.0458 17.2097 18.4597C17.7958 17.8737 18.125 17.0788 18.125 16.25V10C18.1291 8.93717 17.9234 7.88398 17.5197 6.90077C17.1161 5.91757 16.5224 5.02368 15.7727 4.27031ZM5 10.625C5.16576 10.625 5.32473 10.6908 5.44194 10.8081C5.55915 10.9253 5.625 11.0842 5.625 11.25V14.375C5.625 14.5408 5.55915 14.6997 5.44194 14.8169C5.32473 14.9342 5.16576 15 5 15H3.75C3.58424 15 3.42527 14.9342 3.30806 14.8169C3.19085 14.6997 3.125 14.5408 3.125 14.375V10.625H5ZM15 15C14.8342 15 14.6753 14.9342 14.5581 14.8169C14.4408 14.6997 14.375 14.5408 14.375 14.375V11.25C14.375 11.0842 14.4408 10.9253 14.5581 10.8081C14.6753 10.6908 14.8342 10.625 15 10.625H16.875V15H15Z" fill="#111111"/>
                                                </svg>
                                            </div>
                                            <div class="text">FAQs</div>
                                        </a>
                                    </li>
                                    <li class="menu-item">
                                        <a href="#" class="">
                                            <div class="icon">
                                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <g clip-path="url(#clip0_604_18468)">
                                                    <path d="M4.71875 7V1H15.5561L18.9991 4.44801V19H4.71875C4.71875 19 4.71875 16.2 4.71875 13.5" stroke="#111111" stroke-width="1.2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                    <path d="M19.0015 4.44801H15.5586V1L19.0015 4.44801Z" stroke="#111111" stroke-width="1.2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                    <path d="M7.53469 14.5507C9.89243 14.5507 11.8037 12.6366 11.8037 10.2754C11.8037 7.91415 9.89243 6 7.53469 6C5.17695 6 3.26562 7.91415 3.26562 10.2754C3.26562 12.6366 5.17695 14.5507 7.53469 14.5507Z" stroke="#111111" stroke-width="1.2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                    <path d="M5.41029 13.9852L2.90967 16.4895C2.47263 16.9272 1.76451 16.9272 1.3275 16.4895C0.890833 16.0522 0.890833 15.3427 1.3275 14.9054L3.82812 12.4011M6.14799 10.2051L7.11794 11.175L8.91794 9.375" stroke="#111111" stroke-width="1.2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                    </g>
                                                    <defs>
                                                    <clipPath id="clip0_604_18468">
                                                    <rect width="20" height="20" fill="white"/>
                                                    </clipPath>
                                                    </defs>
                                                </svg>
                                            </div>
                                            <div class="text">Privacy policy</div>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="center-item">
                                <div class="center-heading">Connect us</div>
                                <ul class="wg-social">
                                    <li>
                                        <a href="#"><i class="icon-facebook"></i></a>
                                    </li>
                                    <li class="active">
                                        <a href="#"><i class="icon-twitter"></i></a>
                                    </li>
                                    <li>
                                        <a href="#"><i class="icon-linkedin"></i></a>
                                    </li>
                                    <li>
                                        <a href="#"><i class="icon-instagram"></i></a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="bot text-center">
                            <div class="wrap">
                                <div class="mb-20">
                                    <img src="images/menu-left/img-bot.png" alt="">
                                </div>
                                <div class="mb-20">
                                    <h6>Hi, how can we help?</h6>
                                    <div class="text">Contact us if you have any assistance, we will contact you as soon as possible</div>
                                </div>
                                <a href="#" class="tf-button w-full">Contact</a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /section-menu-left -->
                <!-- section-content-right -->
                <div class="section-content-right">
                    <!-- header-dashboard -->
                    <div class="header-dashboard">
                        <div class="wrap">
                            <div class="header-left">
                                <a href="index.html">
                                    <img class="" id="logo_header_mobile" alt="" src="images/logo/logo.png" data-light="images/logo/logo.png" data-dark="images/logo/logo-dark.png" data-width="154px" data-height="52px" data-retina="images/logo/<EMAIL>">
                                </a>
                                <div class="button-show-hide">
                                    <i class="icon-menu-left"></i>
                                </div>
                                <form class="form-search flex-grow">
                                    <fieldset class="name">
                                        <input type="text" placeholder="Search here..." class="show-search" name="name" tabindex="2" value="" aria-required="true" required="">
                                    </fieldset>
                                    <div class="button-submit">
                                        <button class="" type="submit"><i class="icon-search"></i></button>
                                    </div>
                                    <div class="box-content-search" id="box-content-search">
                                        <ul class="mb-24">
                                            <li class="mb-14">
                                                <div class="body-title">Top selling product</div>
                                            </li>
                                            <li class="mb-14">
                                                <div class="divider"></div>
                                            </li>
                                            <li>
                                                <ul>
                                                    <li class="product-item gap14 mb-10">
                                                        <div class="image no-bg">
                                                            <img src="images/products/17.png" alt="">
                                                        </div>
                                                        <div class="flex items-center justify-between gap20 flex-grow">
                                                            <div class="name">
                                                                <a href="product-list.html" class="body-text">Dog Food Rachael Ray Nutrish®</a>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li class="mb-10">
                                                        <div class="divider"></div>
                                                    </li>
                                                    <li class="product-item gap14 mb-10">
                                                        <div class="image no-bg">
                                                            <img src="images/products/18.png" alt="">
                                                        </div>
                                                        <div class="flex items-center justify-between gap20 flex-grow">
                                                            <div class="name">
                                                                <a href="product-list.html" class="body-text">Natural Dog Food Healthy Dog Food</a>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li class="mb-10">
                                                        <div class="divider"></div>
                                                    </li>
                                                    <li class="product-item gap14">
                                                        <div class="image no-bg">
                                                            <img src="images/products/19.png" alt="">
                                                        </div>
                                                        <div class="flex items-center justify-between gap20 flex-grow">
                                                            <div class="name">
                                                                <a href="product-list.html" class="body-text">Freshpet Healthy Dog Food and Cat</a>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </li>
                                        </ul>
                                        <ul class="">
                                            <li class="mb-14">
                                                <div class="body-title">Order product</div>
                                            </li>
                                            <li class="mb-14">
                                                <div class="divider"></div>
                                            </li>
                                            <li>
                                                <ul>
                                                    <li class="product-item gap14 mb-10">
                                                        <div class="image no-bg">
                                                            <img src="images/products/20.png" alt="">
                                                        </div>
                                                        <div class="flex items-center justify-between gap20 flex-grow">
                                                            <div class="name">
                                                                <a href="product-list.html" class="body-text">Sojos Crunchy Natural Grain Free...</a>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li class="mb-10">
                                                        <div class="divider"></div>
                                                    </li>
                                                    <li class="product-item gap14 mb-10">
                                                        <div class="image no-bg">
                                                            <img src="images/products/21.png" alt="">
                                                        </div>
                                                        <div class="flex items-center justify-between gap20 flex-grow">
                                                            <div class="name">
                                                                <a href="product-list.html" class="body-text">Kristin Watson</a>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li class="mb-10">
                                                        <div class="divider"></div>
                                                    </li>
                                                    <li class="product-item gap14 mb-10">
                                                        <div class="image no-bg">
                                                            <img src="images/products/22.png" alt="">
                                                        </div>
                                                        <div class="flex items-center justify-between gap20 flex-grow">
                                                            <div class="name">
                                                                <a href="product-list.html" class="body-text">Mega Pumpkin Bone</a>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li class="mb-10">
                                                        <div class="divider"></div>
                                                    </li>
                                                    <li class="product-item gap14">
                                                        <div class="image no-bg">
                                                            <img src="images/products/23.png" alt="">
                                                        </div>
                                                        <div class="flex items-center justify-between gap20 flex-grow">
                                                            <div class="name">
                                                                <a href="product-list.html" class="body-text">Mega Pumpkin Bone</a>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </li>
                                        </ul>
                                    </div>
                                </form>
                            </div>
                            <div class="header-grid">
                                <div class="header-item country">
                                    <select class="image-select no-text">
                                        <option data-thumbnail="images/country/1.png">ENG</option>
                                        <option data-thumbnail="images/country/9.png">VIE</option>
                                    </select>
                                </div>
                                <div class="header-item button-dark-light">
                                    <i class="icon-moon"></i>
                                </div>
                                <div class="popup-wrap noti type-header">
                                    <div class="dropdown">
                                        <button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                            <span class="header-item">
                                                <span class="text-tiny">1</span>
                                                <i class="icon-bell"></i>
                                            </span>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end has-content" aria-labelledby="dropdownMenuButton1" >
                                            <li>
                                                <h6>Message</h6>
                                            </li>
                                            <li>
                                                <div class="noti-item w-full wg-user active">
                                                    <div class="image">
                                                        <img src="images/avatar/user-11.png" alt="">
                                                    </div>
                                                    <div class="flex-grow">
                                                        <div class="flex items-center justify-between">
                                                            <a href="#" class="body-title">Cameron Williamson</a>
                                                            <div class="time">10:13 PM</div>
                                                        </div>
                                                        <div class="text-tiny">Hello?</div>
                                                    </div>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="noti-item w-full wg-user active">
                                                    <div class="image">
                                                        <img src="images/avatar/user-12.png" alt="">
                                                    </div>
                                                    <div class="flex-grow">
                                                        <div class="flex items-center justify-between">
                                                            <a href="#" class="body-title">Ralph Edwards</a>
                                                            <div class="time">10:13 PM</div>
                                                        </div>
                                                        <div class="text-tiny">Are you there?  interested i this...</div>
                                                    </div>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="noti-item w-full wg-user active">
                                                    <div class="image">
                                                        <img src="images/avatar/user-13.png" alt="">
                                                    </div>
                                                    <div class="flex-grow">
                                                        <div class="flex items-center justify-between">
                                                            <a href="#" class="body-title">Eleanor Pena</a>
                                                            <div class="time">10:13 PM</div>
                                                        </div>
                                                        <div class="text-tiny">Interested in this loads?</div>
                                                    </div>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="noti-item w-full wg-user active">
                                                    <div class="image">
                                                        <img src="images/avatar/user-11.png" alt="">
                                                    </div>
                                                    <div class="flex-grow">
                                                        <div class="flex items-center justify-between">
                                                            <a href="#" class="body-title">Jane Cooper</a>
                                                            <div class="time">10:13 PM</div>
                                                        </div>
                                                        <div class="text-tiny">Okay...Do we have a deal?</div>
                                                    </div>
                                                </div>
                                            </li>
                                            <li><a href="#" class="tf-button w-full">View all</a></li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="popup-wrap message type-header">
                                    <div class="dropdown">
                                        <button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton2" data-bs-toggle="dropdown" aria-expanded="false">
                                            <span class="header-item">
                                                <span class="text-tiny">1</span>
                                                <i class="icon-message-square"></i>
                                            </span>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end has-content" aria-labelledby="dropdownMenuButton2" >
                                            <li>
                                                <h6>Notifications</h6>
                                            </li>
                                            <li>
                                                <div class="message-item item-1">
                                                    <div class="image">
                                                        <i class="icon-noti-1"></i>
                                                    </div>
                                                    <div>
                                                        <div class="body-title-2">Discount available</div>
                                                        <div class="text-tiny">Morbi sapien massa, ultricies at rhoncus at, ullamcorper nec diam</div>
                                                    </div>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="message-item item-2">
                                                    <div class="image">
                                                        <i class="icon-noti-2"></i>
                                                    </div>
                                                    <div>
                                                        <div class="body-title-2">Account has been verified</div>
                                                        <div class="text-tiny">Mauris libero ex, iaculis vitae rhoncus et</div>
                                                    </div>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="message-item item-3">
                                                    <div class="image">
                                                        <i class="icon-noti-3"></i>
                                                    </div>
                                                    <div>
                                                        <div class="body-title-2">Order shipped successfully</div>
                                                        <div class="text-tiny">Integer aliquam eros nec sollicitudin sollicitudin</div>
                                                    </div>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="message-item item-4">
                                                    <div class="image">
                                                        <i class="icon-noti-4"></i>
                                                    </div>
                                                    <div>
                                                        <div class="body-title-2">Order pending: <span>ID 305830</span></div>
                                                        <div class="text-tiny">Ultricies at rhoncus at ullamcorper</div>
                                                    </div>
                                                </div>
                                            </li>
                                            <li><a href="#" class="tf-button w-full">View all</a></li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="header-item button-zoom-maximize">
                                    <div class="">
                                        <i class="icon-maximize"></i>
                                    </div>
                                </div>
                                <div class="popup-wrap apps type-header">
                                    <div class="dropdown">
                                        <button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton4" data-bs-toggle="dropdown" aria-expanded="false">
                                            <span class="header-item">
                                                <i class="icon-grid"></i>
                                            </span>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end has-content" aria-labelledby="dropdownMenuButton4" >
                                            <li>
                                                <h6>Related apps</h6>
                                            </li>
                                            <li>
                                                <ul class="list-apps">
                                                    <li class="item">
                                                        <div class="image">
                                                            <img src="images/apps/item-1.png" alt="">
                                                        </div>
                                                        <a href="#">
                                                            <div class="text-tiny">Photoshop</div>
                                                        </a>
                                                    </li>
                                                    <li class="item">
                                                        <div class="image">
                                                            <img src="images/apps/item-2.png" alt="">
                                                        </div>
                                                        <a href="#">
                                                            <div class="text-tiny">illustrator</div>
                                                        </a>
                                                    </li>
                                                    <li class="item">
                                                        <div class="image">
                                                            <img src="images/apps/item-3.png" alt="">
                                                        </div>
                                                        <a href="#">
                                                            <div class="text-tiny">Sheets</div>
                                                        </a>
                                                    </li>
                                                    <li class="item">
                                                        <div class="image">
                                                            <img src="images/apps/item-4.png" alt="">
                                                        </div>
                                                        <a href="#">
                                                            <div class="text-tiny">Gmail</div>
                                                        </a>
                                                    </li>
                                                    <li class="item">
                                                        <div class="image">
                                                            <img src="images/apps/item-5.png" alt="">
                                                        </div>
                                                        <a href="#">
                                                            <div class="text-tiny">Messenger</div>
                                                        </a>
                                                    </li>
                                                    <li class="item">
                                                        <div class="image">
                                                            <img src="images/apps/item-6.png" alt="">
                                                        </div>
                                                        <a href="#">
                                                            <div class="text-tiny">Youtube</div>
                                                        </a>
                                                    </li>
                                                    <li class="item">
                                                        <div class="image">
                                                            <img src="images/apps/item-7.png" alt="">
                                                        </div>
                                                        <a href="#">
                                                            <div class="text-tiny">Flaticon</div>
                                                        </a>
                                                    </li>
                                                    <li class="item">
                                                        <div class="image">
                                                            <img src="images/apps/item-8.png" alt="">
                                                        </div>
                                                        <a href="#">
                                                            <div class="text-tiny">Instagram</div>
                                                        </a>
                                                    </li>
                                                    <li class="item">
                                                        <div class="image">
                                                            <img src="images/apps/item-9.png" alt="">
                                                        </div>
                                                        <a href="#">
                                                            <div class="text-tiny">PDF</div>
                                                        </a>
                                                    </li>
                                                </ul>
                                            </li>
                                            <li><a href="#" class="tf-button w-full">View all app</a></li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="popup-wrap user type-header">
                                    <div class="dropdown">
                                        <button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton3" data-bs-toggle="dropdown" aria-expanded="false">
                                            <span class="header-user wg-user">
                                                <span class="image">
                                                    <img src="images/avatar/user-1.png" alt="">
                                                </span>
                                                <span class="flex flex-column">
                                                    <span class="body-title mb-2">Kristin Watson</span>
                                                    <span class="text-tiny">Admin</span>
                                                </span>
                                            </span>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end has-content" aria-labelledby="dropdownMenuButton3" >
                                            <li>
                                                <a href="#" class="user-item">
                                                    <div class="icon">
                                                        <i class="icon-user"></i>
                                                    </div>
                                                    <div class="body-title-2">Account</div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#" class="user-item">
                                                    <div class="icon">
                                                        <i class="icon-mail"></i>
                                                    </div>
                                                    <div class="body-title-2">Inbox</div>
                                                    <div class="number">27</div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#" class="user-item">
                                                    <div class="icon">
                                                        <i class="icon-file-text"></i>
                                                    </div>
                                                    <div class="body-title-2">Taskboard</div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="setting.html" class="user-item">
                                                    <div class="icon">
                                                        <i class="icon-settings"></i>
                                                    </div>
                                                    <div class="body-title-2">Setting</div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#" class="user-item">
                                                    <div class="icon">
                                                        <i class="icon-headphones"></i>
                                                    </div>
                                                    <div class="body-title-2">Support</div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="login.html" class="user-item">
                                                    <div class="icon">
                                                        <i class="icon-log-out"></i>
                                                    </div>
                                                    <div class="body-title-2">Log out</div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- /header-dashboard -->
                    <!-- main-content -->
                    <div class="main-content">
                        <!-- main-content-wrap -->
                        <div class="main-content-inner">
                            <!-- main-content-wrap -->
                            <div class="main-content-wrap">
                                <div class="flex items-center flex-wrap justify-between gap20 mb-27">
                                    <h3>All category</h3>
                                    <ul class="breadcrumbs flex items-center flex-wrap justify-start gap10">
                                        <li>
                                            <a href="index.html"><div class="text-tiny">Dashboard</div></a>
                                        </li>
                                        <li>
                                            <i class="icon-chevron-right"></i>
                                        </li>
                                        <li>
                                            <a href="#"><div class="text-tiny">Category</div></a>
                                        </li>
                                        <li>
                                            <i class="icon-chevron-right"></i>
                                        </li>
                                        <li>
                                            <div class="text-tiny">All category</div>
                                        </li>
                                    </ul>
                                </div>
                                <!-- all-category -->
                                <div class="wg-box">
                                    <div class="flex items-center justify-between gap10 flex-wrap">
                                        <div class="wg-filter flex-grow">
                                            <div class="show">
                                                <div class="text-tiny">Showing</div>
                                                <div class="select">
                                                    <select class="">
                                                        <option>10</option>
                                                        <option>20</option>
                                                        <option>30</option>
                                                    </select>
                                                </div>
                                                <div class="text-tiny">entries</div>
                                            </div>
                                            <form class="form-search">
                                                <fieldset class="name">
                                                    <input type="text" placeholder="Search here..." class="" name="name" tabindex="2" value="" aria-required="true" required="">
                                                </fieldset>
                                                <div class="button-submit">
                                                    <button class="" type="submit"><i class="icon-search"></i></button>
                                                </div>
                                            </form>
                                        </div>
                                        <a class="tf-button style-1 w208" href="new-category.html"><i class="icon-plus"></i>Add new</a>
                                    </div>
                                    <div class="wg-table table-all-category">
                                        <ul class="table-title flex gap20 mb-14">
                                            <li>
                                                <div class="body-title">Category</div>
                                            </li>    
                                            <li>
                                                <div class="body-title">Icon</div>
                                            </li>
                                            <li>
                                                <div class="body-title">Quantity</div>
                                            </li>
                                            <li>
                                                <div class="body-title">Sale</div>
                                            </li>
                                            <li>
                                                <div class="body-title">Start date</div>
                                            </li>
                                            <li>
                                                <div class="body-title">Action</div>
                                            </li>
                                        </ul>
                                        <ul class="flex flex-column">
                                            <li class="product-item gap14">
                                                <div class="image no-bg">
                                                    <img src="images/products/51.png" alt="">
                                                </div>
                                                <div class="flex items-center justify-between gap20 flex-grow">
                                                    <div class="name">
                                                        <a href="product-list.html" class="body-title-2">Dried food</a>
                                                    </div>
                                                    <div>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                            <path d="M16.9219 15.8672V10.4881L17.5312 9.26948C17.8241 8.68369 17.9766 8.03775 17.9766 7.38281M17.9766 7.38281V5.97656H6.02344V7.38281M17.9766 7.38281H6.02344M6.02344 7.38281C6.02344 8.03775 6.17592 8.68369 6.4688 9.26948L7.07812 10.4881V15.8672M7.07812 17.2734V19.1369L6.4688 20.3555C6.1759 20.9413 6.02342 21.5873 6.02344 22.2422M6.02344 22.2422V23.6484H17.9766V22.2422M6.02344 22.2422H17.9766M17.9766 22.2422C17.9766 21.5873 17.8241 20.9413 17.5312 20.3555L16.9219 19.1369V17.2734M9.1875 5.97656V4.21875C9.1875 3.63628 9.65972 3.16406 10.2422 3.16406C10.8247 3.16406 11.2969 3.63628 11.2969 4.21875V5.97656M12.7031 3.51562V1.40625C12.7031 0.823781 13.1753 0.351562 13.7578 0.351562C14.3403 0.351562 14.8125 0.823781 14.8125 1.40625V3.51562C14.8125 4.09809 14.3403 4.57031 13.7578 4.57031C13.1753 4.57031 12.7031 4.09809 12.7031 3.51562Z" stroke="#111111" stroke-width="0.7" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M16.9219 12.9388C14.2014 10.2183 9.79861 10.2183 7.07812 12.9388" stroke="#111111" stroke-width="0.7" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M13.5251 17.2621C13.1584 16.875 13.1584 16.2657 13.5259 15.8784C13.8868 15.4977 14.1094 14.9822 14.1094 14.4141C14.1094 13.2491 13.165 12.3047 12 12.3047C10.835 12.3047 9.89062 13.2491 9.89062 14.4141C9.89062 14.9822 10.1132 15.4977 10.475 15.8785C10.8417 16.2657 10.8417 16.875 10.4741 17.2622C10.1132 17.6429 9.89062 18.1584 9.89062 18.7266C9.89062 19.8915 10.835 20.8359 12 20.8359C13.165 20.8359 14.1094 19.8915 14.1094 18.7266C14.1094 18.1584 13.8868 17.6429 13.5251 17.2621Z" stroke="#111111" stroke-width="0.7" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                        </svg>
                                                    </div>
                                                    <div class="body-text">1,638</div>
                                                    <div class="body-text">20</div>
                                                    <div class="body-text">20 Nov 2023</div>
                                                    <div class="list-icon-function">
                                                        <div class="item eye">
                                                            <i class="icon-eye"></i>
                                                        </div>
                                                        <div class="item edit">
                                                            <i class="icon-edit-3"></i>
                                                        </div>
                                                        <div class="item trash">
                                                            <i class="icon-trash-2"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="product-item gap14">
                                                <div class="image no-bg">
                                                    <img src="images/products/52.png" alt="">
                                                </div>
                                                <div class="flex items-center justify-between gap20 flex-grow">
                                                    <div class="name">
                                                        <a href="product-list.html" class="body-title-2">Wet food</a>
                                                    </div>
                                                    <div>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                            <path d="M18.1641 19.8516C18.2573 19.8516 18.3467 19.8145 18.4127 19.7486C18.4786 19.6827 18.5156 19.5932 18.5156 19.5V7.78617C18.5156 7.78106 18.5151 7.77614 18.5149 7.77098C18.5147 7.76519 18.5144 7.75941 18.5139 7.75364C18.5128 7.74067 18.511 7.72776 18.5085 7.71497C18.5081 7.71333 18.5074 7.71169 18.507 7.70981C18.5044 7.69813 18.5012 7.68659 18.4975 7.67522C18.4963 7.67165 18.4949 7.66811 18.4935 7.66462C18.4898 7.65477 18.4858 7.64506 18.4814 7.63552C18.4803 7.6335 18.4797 7.63148 18.4788 7.62942L16.9343 4.53497C16.9137 4.49366 16.8851 4.45682 16.8503 4.42655C16.8154 4.39628 16.7749 4.37317 16.7311 4.35854C16.6873 4.34391 16.6411 4.33805 16.595 4.3413C16.549 4.34454 16.504 4.35682 16.4627 4.37745C16.4214 4.39807 16.3846 4.42662 16.3543 4.46148C16.324 4.49634 16.3009 4.53682 16.2863 4.58061C16.2717 4.6244 16.2658 4.67065 16.269 4.7167C16.2723 4.76276 16.2846 4.80772 16.3052 4.84903L17.5957 7.43466H12.7881L15.1595 2.55356L15.6354 3.50686C15.656 3.5482 15.6845 3.58507 15.7193 3.61538C15.7542 3.64568 15.7946 3.66881 15.8384 3.68345C15.8822 3.6981 15.9285 3.70396 15.9745 3.70071C16.0206 3.69745 16.0656 3.68515 16.1069 3.6645C16.1903 3.62284 16.2538 3.54975 16.2834 3.46129C16.3129 3.37284 16.3062 3.27626 16.2645 3.1928L15.5068 1.67484V0.351562C15.5068 0.258322 15.4698 0.168901 15.4038 0.10297C15.3379 0.0370395 15.2485 0 15.1553 0L3.28017 0C3.08606 0 2.92861 0.1575 2.92861 0.351562V1.67686L0.0351562 7.63256C0.0338906 7.63533 0.0329531 7.63823 0.0316875 7.641C0.0283422 7.6486 0.0252159 7.65629 0.0223125 7.66406C0.0204844 7.66884 0.0185156 7.67377 0.0170156 7.67873C0.0131719 7.69045 0.00989063 7.70231 0.0073125 7.71459C0.006375 7.71938 0.00585938 7.72411 0.00510937 7.72889C0.0035828 7.73749 0.00242549 7.74616 0.00164063 7.75486C0.00114502 7.76108 0.000832383 7.76732 0.000703125 7.77356C0.0005625 7.77778 0 7.78219 0 7.78636V8.98444C0 9.1785 0.157266 9.336 0.351562 9.336C0.545859 9.336 0.703125 9.1785 0.703125 8.98444V8.13792H11.8749V21.7743C11.8749 22.3628 12.1049 22.8984 12.4794 23.2969H2.22563C1.38609 23.2969 0.703125 22.6139 0.703125 21.7744V10.5C0.703125 10.3059 0.545859 10.1484 0.351562 10.1484C0.157266 10.1484 0 10.3059 0 10.5V21.7744C0 23.0016 0.998437 24 2.22563 24H16.29C17.5172 24 18.5156 23.0016 18.5156 21.7744V20.9883C18.5156 20.7942 18.3584 20.6367 18.1641 20.6367C17.9698 20.6367 17.8125 20.7942 17.8125 20.9883V21.7744C17.8125 22.6139 17.1295 23.2969 16.29 23.2969H14.1006C13.261 23.2969 12.5781 22.6139 12.5781 21.7744V8.13797H17.8125V19.5C17.8125 19.5932 17.8495 19.6827 17.9155 19.7486C17.9814 19.8145 18.0708 19.8516 18.1641 19.8516ZM3.63173 0.703125H14.8037V1.40625H3.63173V0.703125ZM3.50025 2.10938H14.5935L12.0064 7.43461H0.913125L3.50025 2.10938Z" fill="#111111"/>
                                                            <path d="M10.5811 12.7927C10.5811 11.8515 9.8146 11.0859 8.87251 11.0859C8.06851 11.0859 7.39304 11.644 7.21229 12.3928C6.93268 12.3348 6.64786 12.3056 6.3623 12.3058C6.0692 12.3058 5.78462 12.3362 5.51255 12.3928C5.3318 11.644 4.65615 11.0859 3.85234 11.0859C2.91005 11.0859 2.14355 11.8515 2.14355 12.7927C2.14355 13.4386 2.50191 14.012 3.05199 14.3017C2.94685 14.5739 2.8899 14.864 2.8899 15.1646C2.8899 16.741 4.44755 18.0234 6.3623 18.0234C8.27705 18.0234 9.83495 16.7409 9.83495 15.1646C9.83495 14.8638 9.77776 14.5739 9.67285 14.3017C10.2229 14.012 10.5811 13.4387 10.5811 12.7927ZM2.84668 12.7927C2.84668 12.2393 3.29771 11.7891 3.85215 11.7891C4.33918 11.7891 4.74605 12.137 4.8376 12.5967C4.23887 12.8386 3.73641 13.218 3.39198 13.6852C3.22754 13.6012 3.08954 13.4734 2.99329 13.3159C2.89704 13.1584 2.8463 12.9773 2.84668 12.7927ZM6.3623 17.3203C4.83521 17.3203 3.59302 16.3531 3.59302 15.1646C3.59302 13.9759 4.83521 13.0089 6.3623 13.0089C7.8894 13.0089 9.13182 13.9759 9.13182 15.1646C9.13182 16.3533 7.88945 17.3203 6.3623 17.3203ZM9.33268 13.6852C8.98824 13.2182 8.48579 12.8386 7.88705 12.5967C7.9786 12.137 8.38548 11.7891 8.87251 11.7891C9.42695 11.7891 9.87798 12.2393 9.87798 12.7927C9.87798 13.1768 9.66226 13.5169 9.33268 13.6852Z" fill="#111111"/>
                                                            <path d="M5.83187 14.1326C5.83187 14.2259 5.79483 14.3153 5.7289 14.3812C5.66297 14.4471 5.57354 14.4842 5.4803 14.4842C5.38706 14.4842 5.29764 14.4471 5.23171 14.3812C5.16578 14.3153 5.12874 14.2259 5.12874 14.1326C5.12874 14.0394 5.16578 13.95 5.23171 13.884C5.29764 13.8181 5.38706 13.7811 5.4803 13.7811C5.57354 13.7811 5.66297 13.8181 5.7289 13.884C5.79483 13.95 5.83187 14.0394 5.83187 14.1326ZM7.59591 14.1326C7.59591 14.2259 7.55887 14.3153 7.49294 14.3812C7.42701 14.4471 7.33759 14.4842 7.24435 14.4842C7.15111 14.4842 7.06169 14.4471 6.99576 14.3812C6.92983 14.3153 6.89279 14.2259 6.89279 14.1326C6.89279 14.0394 6.92983 13.95 6.99576 13.884C7.06169 13.8181 7.15111 13.7811 7.24435 13.7811C7.33759 13.7811 7.42701 13.8181 7.49294 13.884C7.55887 13.95 7.59591 14.0394 7.59591 14.1326ZM14.8438 9.46875V19.7344C14.8438 19.8276 14.8808 19.917 14.9467 19.983C15.0127 20.0489 15.1021 20.0859 15.1953 20.0859C15.2886 20.0859 15.378 20.0489 15.4439 19.983C15.5099 19.917 15.5469 19.8276 15.5469 19.7344V9.46875C15.5469 9.37551 15.5099 9.28609 15.4439 9.22016C15.378 9.15423 15.2886 9.11719 15.1953 9.11719C15.1021 9.11719 15.0127 9.15423 14.9467 9.22016C14.8808 9.28609 14.8438 9.37551 14.8438 9.46875ZM4.01855 18.6797C3.43702 18.6797 2.96387 19.1528 2.96387 19.7344C2.96387 20.3159 3.43702 20.7891 4.01855 20.7891H8.70605C9.28763 20.7891 9.76074 20.3159 9.76074 19.7344C9.76074 19.1528 9.28763 18.6797 8.70605 18.6797H4.01855ZM9.05762 19.7344C9.05762 19.9283 8.89998 20.0859 8.70605 20.0859H4.01855C3.82468 20.0859 3.66699 19.9283 3.66699 19.7344C3.66699 19.5405 3.82468 19.3828 4.01855 19.3828H8.70605C8.89998 19.3828 9.05762 19.5405 9.05762 19.7344Z" fill="#111111"/>
                                                        </svg>
                                                    </div>
                                                    <div class="body-text">1,638</div>
                                                    <div class="body-text">20</div>
                                                    <div class="body-text">20 Nov 2023</div>
                                                    <div class="list-icon-function">
                                                        <div class="item eye">
                                                            <i class="icon-eye"></i>
                                                        </div>
                                                        <div class="item edit">
                                                            <i class="icon-edit-3"></i>
                                                        </div>
                                                        <div class="item trash">
                                                            <i class="icon-trash-2"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="product-item gap14">
                                                <div class="image no-bg">
                                                    <img src="images/products/53.png" alt="">
                                                </div>
                                                <div class="flex items-center justify-between gap20 flex-grow">
                                                    <div class="name">
                                                        <a href="product-list.html" class="body-title-2">Supplemental food</a>
                                                    </div>
                                                    <div>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                            <g clip-path="url(#clip0_2774_3720)">
                                                              <mask id="mask0_2774_3720" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                                                                <path d="M23.6499 23.65V0.350071H0.35V23.65H23.6499Z" fill="white" stroke="white" stroke-width="0.7"/>
                                                              </mask>
                                                              <g mask="url(#mask0_2774_3720)">
                                                                <path d="M1.40625 19.6895H22.5938C23.1094 19.6895 23.5313 19.2676 23.5313 18.752C23.5313 18.2363 23.1094 17.8145 22.5938 17.8145H1.40625C0.890624 17.8145 0.46875 18.2363 0.46875 18.752C0.46875 19.2676 0.890624 19.6895 1.40625 19.6895Z" stroke="#111111" stroke-width="0.7" stroke-miterlimit="22.926" stroke-linecap="round" stroke-linejoin="round"/>
                                                                <path d="M1.40625 17.8145V14.6109C1.4062 14.4314 1.44427 14.2539 1.51092 14.0875L2.28379 12.1554C2.46375 11.5835 2.99812 11.1688 3.62938 11.1688H20.3707C20.9459 11.169 21.4627 11.5225 21.6765 12.0558L22.4505 13.9911C22.545 14.184 22.594 14.3961 22.5938 14.6109V17.8145" stroke="#111111" stroke-width="0.7" stroke-miterlimit="22.926" stroke-linecap="round" stroke-linejoin="round"/>
                                                                <path d="M8.78945 14.4916C8.78945 14.5757 8.74932 14.636 8.70409 14.7193C8.62052 14.8733 8.57688 15.0458 8.57715 15.2211C8.57715 15.803 9.04885 16.2747 9.63071 16.2747C10.1414 16.2747 10.5672 15.9114 10.6638 15.4291H13.3361C13.4313 15.9114 13.8503 16.2747 14.3694 16.2747C14.942 16.2747 15.423 15.803 15.423 15.2211C15.4232 15.0458 15.3796 14.8733 15.2961 14.7193C15.2509 14.636 15.2106 14.5756 15.2106 14.4916C15.2106 14.4076 15.2509 14.3473 15.2961 14.264C15.3796 14.11 15.4232 13.9375 15.423 13.7622C15.423 13.1803 14.942 12.7086 14.3694 12.7086C13.8503 12.7086 13.4313 13.0719 13.3361 13.5541H10.6638C10.5672 13.0719 10.1414 12.7086 9.63071 12.7086C9.04885 12.7086 8.57715 13.1803 8.57715 13.7622C8.57715 13.9439 8.62313 14.1148 8.70409 14.264C8.74932 14.3473 8.78945 14.4076 8.78945 14.4916Z" stroke="#111111" stroke-width="0.7" stroke-miterlimit="22.926" stroke-linecap="round" stroke-linejoin="round"/>
                                                                <path d="M4.37177 11.1274C4.09361 10.6411 4.03544 10.0358 4.26648 9.47801C4.50405 8.9044 4.99361 8.51145 5.55376 8.37415C5.46629 7.80411 5.64193 7.20148 6.08101 6.76241C6.52004 6.32333 7.12271 6.14769 7.69276 6.23516C7.83005 5.67505 8.22296 5.18549 8.79661 4.94783C9.37031 4.71023 9.99426 4.77857 10.4875 5.07754C10.8287 4.61259 11.379 4.31071 11.9999 4.31071C12.6209 4.31066 13.1712 4.61263 13.5124 5.07758C14.0056 4.77862 14.6296 4.71027 15.2033 4.94788C15.777 5.18549 16.1698 5.675 16.3071 6.23516C16.8772 6.14769 17.4799 6.32333 17.9189 6.76241C18.358 7.20148 18.5336 7.80411 18.4462 8.37415C19.0063 8.51145 19.4958 8.9044 19.7335 9.47806C19.9638 10.0341 19.9066 10.6374 19.6307 11.123" stroke="#111111" stroke-width="0.7" stroke-miterlimit="22.926" stroke-linecap="round" stroke-linejoin="round"/>
                                                                <path d="M8.57788 8.01758H8.57826" stroke="#111111" stroke-width="0.7" stroke-miterlimit="22.926" stroke-linecap="round" stroke-linejoin="round"/>
                                                                <path d="M11.6716 8.7207H11.672" stroke="#111111" stroke-width="0.7" stroke-miterlimit="22.926" stroke-linecap="round" stroke-linejoin="round"/>
                                                                <path d="M14.3435 7.0332H14.3439" stroke="#111111" stroke-width="0.7" stroke-miterlimit="22.926" stroke-linecap="round" stroke-linejoin="round"/>
                                                                <path d="M16.3591 9.00195H16.3595" stroke="#111111" stroke-width="0.7" stroke-miterlimit="22.926" stroke-linecap="round" stroke-linejoin="round"/>
                                                              </g>
                                                            </g>
                                                            <defs>
                                                              <clipPath id="clip0_2774_3720">
                                                                <rect width="24" height="24" fill="white"/>
                                                              </clipPath>
                                                            </defs>
                                                        </svg>
                                                    </div>
                                                    <div class="body-text">1,638</div>
                                                    <div class="body-text">20</div>
                                                    <div class="body-text">20 Nov 2023</div>
                                                    <div class="list-icon-function">
                                                        <div class="item eye">
                                                            <i class="icon-eye"></i>
                                                        </div>
                                                        <div class="item edit">
                                                            <i class="icon-edit-3"></i>
                                                        </div>
                                                        <div class="item trash">
                                                            <i class="icon-trash-2"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="product-item gap14">
                                                <div class="image no-bg">
                                                    <img src="images/products/54.png" alt="">
                                                </div>
                                                <div class="flex items-center justify-between gap20 flex-grow">
                                                    <div class="name">
                                                        <a href="product-list.html" class="body-title-2">Puppy food</a>
                                                    </div>
                                                    <div>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                            <g clip-path="url(#clip0_2774_1803)">
                                                              <mask id="mask0_2774_1803" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                                                                <path d="M23.6499 23.65V0.350071H0.35V23.65H23.6499Z" fill="white" stroke="white" stroke-width="0.7"/>
                                                              </mask>
                                                              <g mask="url(#mask0_2774_1803)">
                                                                <path d="M17.4025 14.5421L17.9739 13.9707C17.2262 13.223 17.2262 12.0109 17.9739 11.2632C18.7215 10.5156 19.9337 10.5156 20.6813 11.2632C21.2059 11.7877 21.361 12.5405 21.1492 13.2019C21.8101 12.9901 22.5634 13.1452 23.0879 13.6698C23.8356 14.4174 23.8356 15.6296 23.0879 16.3772C22.3403 17.1249 21.1281 17.1249 20.3804 16.3772L17.0715 19.6861C17.8192 20.4338 17.8192 21.646 17.0715 22.3936C16.3239 23.1412 15.1117 23.1412 14.364 22.3936C13.8395 21.8691 13.6843 21.1158 13.8962 20.4549C13.2348 20.6667 12.482 20.5116 11.9575 19.987C11.2098 19.2394 11.2098 18.0272 11.9575 17.2795C12.7051 16.5319 13.9173 16.5319 14.665 17.2795L16.2428 15.7017" stroke="#111111" stroke-width="0.7" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                                <mask id="path-3-inside-1_2774_1803" fill="white">
                                                                  <path d="M19.8721 12.2392C19.8726 12.2772 19.8656 12.315 19.8513 12.3502C19.8371 12.3855 19.8161 12.4176 19.7893 12.4446C19.7626 12.4717 19.7308 12.4932 19.6957 12.5078C19.6607 12.5225 19.623 12.53 19.585 12.53C19.547 12.53 19.5093 12.5225 19.4743 12.5078C19.4392 12.4932 19.4074 12.4717 19.3807 12.4446C19.354 12.4176 19.3329 12.3855 19.3187 12.3502C19.3045 12.315 19.2974 12.2772 19.2979 12.2392C19.2988 12.1637 19.3295 12.0916 19.3833 12.0385C19.437 11.9855 19.5095 11.9557 19.585 11.9557C19.6605 11.9557 19.733 11.9855 19.7868 12.0385C19.8405 12.0916 19.8712 12.1637 19.8721 12.2392Z"/>
                                                                </mask>
                                                                <path d="M19.8721 12.2392C19.8726 12.2772 19.8656 12.315 19.8513 12.3502C19.8371 12.3855 19.8161 12.4176 19.7893 12.4446C19.7626 12.4717 19.7308 12.4932 19.6957 12.5078C19.6607 12.5225 19.623 12.53 19.585 12.53C19.547 12.53 19.5093 12.5225 19.4743 12.5078C19.4392 12.4932 19.4074 12.4717 19.3807 12.4446C19.354 12.4176 19.3329 12.3855 19.3187 12.3502C19.3045 12.315 19.2974 12.2772 19.2979 12.2392C19.2988 12.1637 19.3295 12.0916 19.3833 12.0385C19.437 11.9855 19.5095 11.9557 19.585 11.9557C19.6605 11.9557 19.733 11.9855 19.7868 12.0385C19.8405 12.0916 19.8712 12.1637 19.8721 12.2392Z" fill="#111111"/>
                                                                <path d="M19.8721 12.2392L20.5721 12.2303L19.8721 12.2392ZM19.2979 12.2392L18.5979 12.2303L19.2979 12.2392ZM20.5721 12.2303C20.5738 12.3609 20.5495 12.4907 20.5006 12.6119L19.2021 12.0885C19.1817 12.1392 19.1715 12.1935 19.1722 12.2481L20.5721 12.2303ZM20.5006 12.6119C20.4517 12.7331 20.3793 12.8434 20.2875 12.9364L19.2912 11.9528C19.2528 11.9917 19.2225 12.0379 19.2021 12.0885L20.5006 12.6119ZM20.2875 12.9364C20.1957 13.0294 20.0863 13.1033 19.9657 13.1537L19.4258 11.862C19.3754 11.883 19.3296 11.9139 19.2912 11.9528L20.2875 12.9364ZM19.9657 13.1537C19.8451 13.2041 19.7157 13.23 19.585 13.23V11.83C19.5303 11.83 19.4762 11.8409 19.4258 11.862L19.9657 13.1537ZM19.585 13.23C19.4543 13.23 19.3249 13.2041 19.2043 13.1537L19.7442 11.862C19.6938 11.8409 19.6397 11.83 19.585 11.83V13.23ZM19.2043 13.1537C19.0837 13.1033 18.9744 13.0294 18.8825 12.9364L19.8788 11.9528C19.8404 11.9139 19.7946 11.883 19.7442 11.862L19.2043 13.1537ZM18.8825 12.9364C18.7907 12.8434 18.7183 12.7331 18.6694 12.6119L19.9679 12.0885C19.9475 12.0379 19.9172 11.9917 19.8788 11.9528L18.8825 12.9364ZM18.6694 12.6119C18.6206 12.4907 18.5963 12.3609 18.5979 12.2303L19.9978 12.2481C19.9985 12.1935 19.9884 12.1392 19.9679 12.0885L18.6694 12.6119ZM18.5979 12.2303C18.6012 11.9707 18.7067 11.7228 18.8915 11.5404L19.8751 12.5367C19.9523 12.4604 19.9964 12.3567 19.9978 12.2481L18.5979 12.2303ZM18.8915 11.5404C19.0762 11.358 19.3254 11.2557 19.585 11.2557V12.6557C19.6936 12.6557 19.7978 12.6129 19.8751 12.5367L18.8915 11.5404ZM19.585 11.2557C19.8446 11.2557 20.0938 11.358 20.2786 11.5404L19.295 12.5367C19.3722 12.6129 19.4764 12.6557 19.585 12.6557V11.2557ZM20.2786 11.5404C20.4633 11.7228 20.5688 11.9707 20.5721 12.2303L19.1722 12.2481C19.1736 12.3567 19.2177 12.4604 19.295 12.5367L20.2786 11.5404Z" fill="#111111" mask="url(#path-3-inside-1_2774_1803)"/>
                                                                <mask id="path-5-inside-2_2774_1803" fill="white">
                                                                  <path d="M19.4984 14.3806C19.4988 14.4186 19.4918 14.4563 19.4776 14.4916C19.4634 14.5268 19.4423 14.5589 19.4156 14.586C19.3889 14.613 19.357 14.6345 19.322 14.6492C19.2869 14.6638 19.2492 14.6714 19.2112 14.6714C19.1732 14.6714 19.1356 14.6638 19.1005 14.6492C19.0654 14.6345 19.0336 14.613 19.0069 14.586C18.9802 14.5589 18.9591 14.5268 18.9449 14.4916C18.9307 14.4563 18.9236 14.4186 18.9241 14.3806C18.9251 14.305 18.9557 14.2329 19.0095 14.1799C19.0632 14.1268 19.1357 14.0971 19.2112 14.0971C19.2868 14.0971 19.3592 14.1268 19.413 14.1799C19.4667 14.2329 19.4974 14.305 19.4984 14.3806Z"/>
                                                                </mask>
                                                                <path d="M19.4984 14.3806C19.4988 14.4186 19.4918 14.4563 19.4776 14.4916C19.4634 14.5268 19.4423 14.5589 19.4156 14.586C19.3889 14.613 19.357 14.6345 19.322 14.6492C19.2869 14.6638 19.2492 14.6714 19.2112 14.6714C19.1732 14.6714 19.1356 14.6638 19.1005 14.6492C19.0654 14.6345 19.0336 14.613 19.0069 14.586C18.9802 14.5589 18.9591 14.5268 18.9449 14.4916C18.9307 14.4563 18.9236 14.4186 18.9241 14.3806C18.9251 14.305 18.9557 14.2329 19.0095 14.1799C19.0632 14.1268 19.1357 14.0971 19.2112 14.0971C19.2868 14.0971 19.3592 14.1268 19.413 14.1799C19.4667 14.2329 19.4974 14.305 19.4984 14.3806Z" fill="#111111"/>
                                                                <path d="M19.4984 14.3806L20.1983 14.3716L19.4984 14.3806ZM18.9241 14.3806L18.2242 14.3716L18.9241 14.3806ZM20.1983 14.3716C20.2 14.5023 20.1757 14.632 20.1268 14.7532L18.8283 14.2299C18.8079 14.2806 18.7977 14.3349 18.7984 14.3895L20.1983 14.3716ZM20.1268 14.7532C20.078 14.8745 20.0055 14.9848 19.9137 15.0778L18.9174 14.0942C18.879 14.1331 18.8487 14.1792 18.8283 14.2299L20.1268 14.7532ZM19.9137 15.0778C19.8219 15.1708 19.7125 15.2446 19.5919 15.295L19.052 14.0033C19.0016 14.0244 18.9558 14.0553 18.9174 14.0942L19.9137 15.0778ZM19.5919 15.295C19.4713 15.3454 19.3419 15.3714 19.2112 15.3714V13.9714C19.1566 13.9714 19.1025 13.9822 19.052 14.0033L19.5919 15.295ZM19.2112 15.3714C19.0805 15.3714 18.9511 15.3454 18.8305 15.295L19.3704 14.0033C19.32 13.9822 19.2659 13.9714 19.2112 13.9714V15.3714ZM18.8305 15.295C18.71 15.2446 18.6006 15.1708 18.5088 15.0778L19.505 14.0942C19.4666 14.0553 19.4209 14.0244 19.3704 14.0033L18.8305 15.295ZM18.5088 15.0778C18.4169 14.9848 18.3445 14.8745 18.2956 14.7532L19.5941 14.2299C19.5737 14.1792 19.5434 14.1331 19.505 14.0942L18.5088 15.0778ZM18.2956 14.7532C18.2468 14.632 18.2225 14.5023 18.2242 14.3716L19.624 14.3895C19.6247 14.3349 19.6146 14.2806 19.5941 14.2299L18.2956 14.7532ZM18.2242 14.3716C18.2275 14.112 18.3329 13.8642 18.5177 13.6818L19.5013 14.678C19.5785 14.6017 19.6227 14.4981 19.624 14.3895L18.2242 14.3716ZM18.5177 13.6818C18.7024 13.4994 18.9516 13.3971 19.2112 13.3971V14.7971C19.3198 14.7971 19.424 14.7543 19.5013 14.678L18.5177 13.6818ZM19.2112 13.3971C19.4709 13.3971 19.72 13.4994 19.9048 13.6818L18.9212 14.678C18.9984 14.7543 19.1026 14.7971 19.2112 14.7971V13.3971ZM19.9048 13.6818C20.0895 13.8642 20.195 14.112 20.1983 14.3716L18.7984 14.3895C18.7998 14.4981 18.8439 14.6017 18.9212 14.678L19.9048 13.6818Z" fill="#111111" mask="url(#path-5-inside-2_2774_1803)"/>
                                                                <mask id="path-7-inside-3_2774_1803" fill="white">
                                                                  <path d="M20.95 14.3806C20.9505 14.4186 20.9434 14.4563 20.9292 14.4916C20.915 14.5268 20.8939 14.5589 20.8672 14.586C20.8405 14.613 20.8087 14.6345 20.7736 14.6492C20.7385 14.6638 20.7009 14.6714 20.6629 14.6714C20.6249 14.6714 20.5872 14.6638 20.5522 14.6492C20.5171 14.6345 20.4853 14.613 20.4585 14.586C20.4318 14.5589 20.4108 14.5268 20.3966 14.4916C20.3823 14.4563 20.3753 14.4186 20.3758 14.3806C20.3767 14.305 20.4074 14.2329 20.4611 14.1799C20.5149 14.1268 20.5874 14.0971 20.6629 14.0971C20.7384 14.0971 20.8109 14.1268 20.8646 14.1799C20.9184 14.2329 20.9491 14.305 20.95 14.3806Z"/>
                                                                </mask>
                                                                <path d="M20.95 14.3806C20.9505 14.4186 20.9434 14.4563 20.9292 14.4916C20.915 14.5268 20.8939 14.5589 20.8672 14.586C20.8405 14.613 20.8087 14.6345 20.7736 14.6492C20.7385 14.6638 20.7009 14.6714 20.6629 14.6714C20.6249 14.6714 20.5872 14.6638 20.5522 14.6492C20.5171 14.6345 20.4853 14.613 20.4585 14.586C20.4318 14.5589 20.4108 14.5268 20.3966 14.4916C20.3823 14.4563 20.3753 14.4186 20.3758 14.3806C20.3767 14.305 20.4074 14.2329 20.4611 14.1799C20.5149 14.1268 20.5874 14.0971 20.6629 14.0971C20.7384 14.0971 20.8109 14.1268 20.8646 14.1799C20.9184 14.2329 20.9491 14.305 20.95 14.3806Z" fill="#111111"/>
                                                                <path d="M20.95 14.3806L21.65 14.3716L20.95 14.3806ZM20.3758 14.3806L19.6758 14.3716L20.3758 14.3806ZM21.65 14.3716C21.6516 14.5023 21.6273 14.632 21.5785 14.7532L20.28 14.2299C20.2595 14.2806 20.2494 14.3349 20.2501 14.3895L21.65 14.3716ZM21.5785 14.7532C21.5296 14.8745 21.4572 14.9848 21.3654 15.0778L20.3691 14.0942C20.3307 14.1331 20.3004 14.1792 20.28 14.2299L21.5785 14.7532ZM21.3654 15.0778C21.2735 15.1708 21.1642 15.2446 21.0436 15.295L20.5037 14.0033C20.4532 14.0244 20.4075 14.0553 20.3691 14.0942L21.3654 15.0778ZM21.0436 15.295C20.923 15.3454 20.7936 15.3714 20.6629 15.3714V13.9714C20.6082 13.9714 20.5541 13.9822 20.5037 14.0033L21.0436 15.295ZM20.6629 15.3714C20.5322 15.3714 20.4028 15.3454 20.2822 15.295L20.8221 14.0033C20.7717 13.9822 20.7175 13.9714 20.6629 13.9714V15.3714ZM20.2822 15.295C20.1616 15.2446 20.0522 15.1708 19.9604 15.0778L20.9567 14.0942C20.9183 14.0553 20.8725 14.0244 20.8221 14.0033L20.2822 15.295ZM19.9604 15.0778C19.8686 14.9848 19.7962 14.8745 19.7473 14.7532L21.0458 14.2299C21.0254 14.1792 20.9951 14.1331 20.9567 14.0942L19.9604 15.0778ZM19.7473 14.7532C19.6984 14.632 19.6741 14.5023 19.6758 14.3716L21.0757 14.3895C21.0764 14.3349 21.0662 14.2806 21.0458 14.2299L19.7473 14.7532ZM19.6758 14.3716C19.6791 14.112 19.7846 13.8642 19.9693 13.6818L20.9529 14.678C21.0302 14.6017 21.0743 14.4981 21.0757 14.3895L19.6758 14.3716ZM19.9693 13.6818C20.1541 13.4994 20.4033 13.3971 20.6629 13.3971V14.7971C20.7715 14.7971 20.8757 14.7543 20.9529 14.678L19.9693 13.6818ZM20.6629 13.3971C20.9225 13.3971 21.1717 13.4994 21.3564 13.6818L20.3728 14.678C20.4501 14.7543 20.5543 14.7971 20.6629 14.7971V13.3971ZM21.3564 13.6818C21.5412 13.8642 21.6466 14.112 21.65 14.3716L20.2501 14.3895C20.2515 14.4981 20.2956 14.6017 20.3728 14.678L21.3564 13.6818Z" fill="#111111" mask="url(#path-7-inside-3_2774_1803)"/>
                                                                <path d="M9.3874 2.02841C10.2438 1.41017 11.2957 1.04591 12.4329 1.04591C15.3096 1.04591 17.6416 3.37793 17.6416 6.25464C17.6416 9.13135 15.3096 11.4634 12.4329 11.4634C9.55615 11.4634 7.22412 9.13135 7.22412 6.25464C7.22412 5.10673 7.59537 4.0459 8.22443 3.18533M12.4329 8.17759C11.3708 8.17759 10.5099 7.31659 10.5099 6.25455C10.5099 5.19251 11.3708 4.33156 12.4329 4.33156C13.4949 4.33156 14.3559 5.19251 14.3559 6.25455C14.3559 7.31659 13.4949 8.17759 12.4329 8.17759Z" stroke="#111111" stroke-width="0.7" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                                <path d="M14.3067 3.32648C14.3077 3.32552 14.3082 3.32421 14.3082 3.32285C14.3082 3.3215 14.3077 3.32019 14.3067 3.31923C14.3058 3.31827 14.3045 3.31773 14.3031 3.31773C14.3018 3.31773 14.3005 3.31827 14.2995 3.31923C14.2985 3.32019 14.298 3.3215 14.298 3.32285C14.298 3.32421 14.2985 3.32552 14.2995 3.32648C14.3005 3.32744 14.3018 3.32798 14.3031 3.32798C14.3045 3.32798 14.3058 3.32744 14.3067 3.32648Z" fill="#111111" stroke="#111111" stroke-width="0.7"/>
                                                                <path d="M12.4364 2.79303C12.4373 2.79207 12.4379 2.79077 12.4379 2.78941C12.4379 2.78805 12.4373 2.78675 12.4364 2.78578C12.4354 2.78482 12.4341 2.78428 12.4328 2.78428C12.4314 2.78428 12.4301 2.78482 12.4291 2.78578C12.4282 2.78675 12.4276 2.78805 12.4276 2.78941C12.4276 2.79077 12.4282 2.79207 12.4291 2.79303C12.4301 2.79399 12.4314 2.79453 12.4328 2.79453C12.4341 2.79453 12.4354 2.79399 12.4364 2.79303Z" fill="#111111" stroke="#111111" stroke-width="0.7"/>
                                                                <path d="M7.68806 13.9256L7.91822 14.1557C8.54775 13.5257 9.56868 13.5257 10.1982 14.1557C10.8282 14.7853 10.8282 15.8062 10.1982 16.4357C9.75665 16.8777 9.12244 17.0081 8.56556 16.8299C8.74369 17.3868 8.61338 18.021 8.17135 18.4626C7.54182 19.0925 6.52088 19.0925 5.89135 18.4626C5.26135 17.8331 5.26135 16.8121 5.89135 16.1826L3.10417 13.3954C2.47464 14.025 1.45371 14.025 0.82371 13.3954C0.19418 12.7659 0.19418 11.7449 0.82371 11.115C1.26574 10.6734 1.89996 10.5426 2.45683 10.7212C2.27871 10.1643 2.40902 9.53008 2.85105 9.08809C3.48058 8.45856 4.50151 8.45856 5.13104 9.08809C5.76104 9.71809 5.76104 10.739 5.13104 11.3686L6.52838 12.7659" stroke="#111111" stroke-width="0.7" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                              </g>
                                                            </g>
                                                            <defs>
                                                              <clipPath id="clip0_2774_1803">
                                                                <rect width="24" height="24" fill="white"/>
                                                              </clipPath>
                                                            </defs>
                                                        </svg>
                                                    </div>
                                                    <div class="body-text">1,638</div>
                                                    <div class="body-text">20</div>
                                                    <div class="body-text">20 Nov 2023</div>
                                                    <div class="list-icon-function">
                                                        <div class="item eye">
                                                            <i class="icon-eye"></i>
                                                        </div>
                                                        <div class="item edit">
                                                            <i class="icon-edit-3"></i>
                                                        </div>
                                                        <div class="item trash">
                                                            <i class="icon-trash-2"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="product-item gap14">
                                                <div class="image no-bg">
                                                    <img src="images/products/55.png" alt="">
                                                </div>
                                                <div class="flex items-center justify-between gap20 flex-grow">
                                                    <div class="name">
                                                        <a href="product-list.html" class="body-title-2">Food for adult dogs</a>
                                                    </div>
                                                    <div>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                            <g clip-path="url(#clip0_2774_3783)">
                                                              <path d="M8.87427 12.6646C9.06843 12.6646 9.22583 12.5072 9.22583 12.313C9.22583 12.1188 9.06843 11.9614 8.87427 11.9614C8.6801 11.9614 8.52271 12.1188 8.52271 12.313C8.52271 12.5072 8.6801 12.6646 8.87427 12.6646Z" fill="#111111"/>
                                                              <path d="M12.3132 9.22583C12.5074 9.22583 12.6648 9.06843 12.6648 8.87427C12.6648 8.6801 12.5074 8.52271 12.3132 8.52271C12.1191 8.52271 11.9617 8.6801 11.9617 8.87427C11.9617 9.06843 12.1191 9.22583 12.3132 9.22583Z" fill="#111111"/>
                                                              <path d="M10.4866 12.8706C10.6807 12.8706 10.8381 12.7132 10.8381 12.519C10.8381 12.3249 10.6807 12.1675 10.4866 12.1675C10.2924 12.1675 10.135 12.3249 10.135 12.519C10.135 12.7132 10.2924 12.8706 10.4866 12.8706Z" fill="#111111"/>
                                                              <path d="M15.1257 6.41333C15.3199 6.41333 15.4773 6.25593 15.4773 6.06177C15.4773 5.86761 15.3199 5.71021 15.1257 5.71021C14.9316 5.71021 14.7742 5.86761 14.7742 6.06177C14.7742 6.25593 14.9316 6.41333 15.1257 6.41333Z" fill="#111111"/>
                                                              <path d="M6.06177 15.4771C6.25593 15.4771 6.41333 15.3197 6.41333 15.1255C6.41333 14.9313 6.25593 14.7739 6.06177 14.7739C5.86761 14.7739 5.71021 14.9313 5.71021 15.1255C5.71021 15.3197 5.86761 15.4771 6.06177 15.4771Z" fill="#111111"/>
                                                              <path d="M8.87427 16.5317C9.65092 16.5317 10.2805 15.9021 10.2805 15.1255C10.2805 14.3488 9.65092 13.7192 8.87427 13.7192C8.09762 13.7192 7.46802 14.3488 7.46802 15.1255C7.46802 15.9021 8.09762 16.5317 8.87427 16.5317Z" stroke="#111111" stroke-width="0.7" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                              <path d="M10.0921 15.8286C9.84889 15.4084 9.39486 15.1255 8.87441 15.1255C8.35396 15.1255 7.89993 15.4084 7.65674 15.8286" stroke="#111111" stroke-width="0.7" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                              <path d="M15.1257 10.2805C15.9024 10.2805 16.532 9.65092 16.532 8.87427C16.532 8.09762 15.9024 7.46802 15.1257 7.46802C14.3491 7.46802 13.7195 8.09762 13.7195 8.87427C13.7195 9.65092 14.3491 10.2805 15.1257 10.2805Z" stroke="#111111" stroke-width="0.7" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                              <path d="M16.3433 9.57739C16.1001 9.15721 15.6461 8.87427 15.1256 8.87427C14.6052 8.87427 14.1511 9.15721 13.908 9.57739" stroke="#111111" stroke-width="0.7" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                              <path d="M15.6582 4.57031C16.0465 4.57031 16.3613 4.25551 16.3613 3.86719C16.3613 3.47886 16.0465 3.16406 15.6582 3.16406C15.2699 3.16406 14.9551 3.47886 14.9551 3.86719C14.9551 4.25551 15.2699 4.57031 15.6582 4.57031Z" stroke="#111111" stroke-width="0.7" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                              <path d="M20.1328 9.04492C20.5211 9.04492 20.8359 8.73012 20.8359 8.3418C20.8359 7.95347 20.5211 7.63867 20.1328 7.63867C19.7445 7.63867 19.4297 7.95347 19.4297 8.3418C19.4297 8.73012 19.7445 9.04492 20.1328 9.04492Z" stroke="#111111" stroke-width="0.7" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                              <path d="M3.86719 16.3613C4.25551 16.3613 4.57031 16.0465 4.57031 15.6582C4.57031 15.2699 4.25551 14.9551 3.86719 14.9551C3.47886 14.9551 3.16406 15.2699 3.16406 15.6582C3.16406 16.0465 3.47886 16.3613 3.86719 16.3613Z" stroke="#111111" stroke-width="0.7" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                              <path d="M8.3418 20.8359C8.73012 20.8359 9.04492 20.5211 9.04492 20.1328C9.04492 19.7445 8.73012 19.4297 8.3418 19.4297C7.95347 19.4297 7.63867 19.7445 7.63867 20.1328C7.63867 20.5211 7.95347 20.8359 8.3418 20.8359Z" stroke="#111111" stroke-width="0.7" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                              <path d="M8.51736 9.51168L6.50905 11.5246C6.31318 11.7205 6.08064 11.8759 5.82471 11.9819C5.56879 12.0879 5.2945 12.1424 5.01749 12.1424H3.86718C2.96746 12.1424 2.06774 12.4857 1.38125 13.1721C0.00833201 14.5451 0.00833201 16.7711 1.38125 18.144C2.06047 18.8232 2.94843 19.1658 3.83863 19.173L4.82619 19.1737L4.82689 20.1613C4.83407 21.0515 5.17672 21.9395 5.85589 22.6187C7.22882 23.9916 9.45482 23.9916 10.8277 22.6187C11.5142 21.9322 11.8574 21.0325 11.8574 20.1328V18.9824C11.8574 18.7054 11.912 18.4311 12.018 18.1752C12.124 17.9193 12.2794 17.6868 12.4753 17.4909L17.4909 12.4753C17.6868 12.2794 17.9193 12.124 18.1752 12.018C18.4311 11.912 18.7054 11.8574 18.9824 11.8574H20.1328C21.0325 11.8574 21.9322 11.5142 22.6187 10.8277C23.9916 9.45482 23.9916 7.22882 22.6187 5.85589C21.9395 5.17668 21.0516 4.83407 20.1613 4.82689L19.1738 4.82619L19.1731 3.83863C19.1659 2.94847 18.8233 2.06047 18.1441 1.38125C16.7712 0.00833201 14.5452 0.00833201 13.1722 1.38125C12.4858 2.06774 12.1425 2.96746 12.1425 3.86718V5.01749C12.1425 5.2945 12.088 5.56879 11.982 5.82471C11.876 6.08064 11.7206 6.31317 11.5247 6.50905L9.51177 8.51736" stroke="#111111" stroke-width="0.7" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                            </g>
                                                            <defs>
                                                              <clipPath id="clip0_2774_3783">
                                                                <rect width="24" height="24" fill="white"/>
                                                              </clipPath>
                                                            </defs>
                                                        </svg>
                                                    </div>
                                                    <div class="body-text">1,638</div>
                                                    <div class="body-text">20</div>
                                                    <div class="body-text">20 Nov 2023</div>
                                                    <div class="list-icon-function">
                                                        <div class="item eye">
                                                            <i class="icon-eye"></i>
                                                        </div>
                                                        <div class="item edit">
                                                            <i class="icon-edit-3"></i>
                                                        </div>
                                                        <div class="item trash">
                                                            <i class="icon-trash-2"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="product-item gap14">
                                                <div class="image no-bg">
                                                    <img src="images/products/56.png" alt="">
                                                </div>
                                                <div class="flex items-center justify-between gap20 flex-grow">
                                                    <div class="name">
                                                        <a href="product-list.html" class="body-title-2">Food for elderly dogs</a>
                                                    </div>
                                                    <div>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                            <path d="M10.678 14.8417V17.7868C10.678 17.9706 10.5899 18.1435 10.439 18.2485C10.138 18.4576 9.94631 18.8134 9.96834 19.2128C9.99975 19.7846 10.4807 20.2426 11.053 20.2482C11.2537 20.2501 11.4416 20.1985 11.6043 20.1067C11.7248 20.038 11.8612 20.0019 11.9999 20.0019C12.1386 20.0019 12.275 20.038 12.3955 20.1067C12.5582 20.1985 12.7462 20.2501 12.9468 20.2482C13.5191 20.2426 14.0001 19.7846 14.0315 19.2128C14.0535 18.8134 13.8618 18.4576 13.5608 18.2485C13.4099 18.1435 13.3218 17.9706 13.3218 17.7868V12.7464C13.3218 12.5626 13.4099 12.3892 13.5608 12.2846C13.8618 12.0756 14.0535 11.7198 14.0315 11.3204C14.0001 10.7485 13.5191 10.2906 12.9468 10.2845C12.7462 10.2826 12.5582 10.3342 12.3955 10.426C12.1485 10.5662 11.8513 10.5662 11.6043 10.426C11.4362 10.3311 11.246 10.2823 11.053 10.2845C10.4807 10.2906 9.99975 10.7485 9.96834 11.3204C9.94631 11.7198 10.138 12.0756 10.439 12.2846C10.5899 12.3892 10.678 12.5626 10.678 12.7464V13.201M17.801 5.95239H6.19884M4.6649 21.8031V23.1798C4.6649 23.4385 4.8749 23.6485 5.13365 23.6485H6.5024M19.3349 21.8443V23.1798C19.3349 23.4385 19.1249 23.6485 18.8662 23.6485H8.14303M5.33508 12.7462V17.7868C5.33508 17.9704 5.24681 18.1437 5.09601 18.2484C4.79479 18.4574 4.60331 18.8134 4.62525 19.2126C4.6567 19.7845 5.13736 20.2426 5.71008 20.2483C5.90299 20.2506 6.09307 20.2018 6.261 20.1068C6.38157 20.0381 6.51796 20.0019 6.65674 20.0019C6.79552 20.0019 6.9319 20.0381 7.05248 20.1068C7.22042 20.2018 7.41052 20.2506 7.60345 20.2483C8.17622 20.2426 8.65678 19.7846 8.68828 19.2127C8.71026 18.8134 8.51873 18.4575 8.21751 18.2484C8.06672 18.1437 7.97845 17.9704 7.97845 17.7868V12.7462C7.97845 12.5626 8.06672 12.3893 8.21751 12.2846C8.51873 12.0755 8.71026 11.7196 8.68828 11.3203C8.65678 10.7484 8.17617 10.2904 7.60345 10.2847C7.41053 10.2824 7.22045 10.3312 7.05253 10.4262C6.93195 10.4949 6.79556 10.5311 6.65679 10.5311C6.51801 10.5311 6.38162 10.4949 6.26104 10.4262C6.09312 10.3312 5.90304 10.2824 5.71012 10.2847C5.13736 10.2904 4.65675 10.7485 4.62525 11.3204C4.60326 11.7196 4.79479 12.0756 5.09601 12.2846C5.24681 12.3893 5.33508 12.5626 5.33508 12.7462ZM18.6647 17.7868V12.7462C18.6647 12.5626 18.753 12.3893 18.9038 12.2846C19.205 12.0756 19.3965 11.7196 19.3746 11.3204C19.3431 10.7485 18.8625 10.2904 18.2897 10.2847C18.0968 10.2824 17.9067 10.3312 17.7388 10.4262C17.6182 10.4949 17.4818 10.5311 17.3431 10.5311C17.2043 10.5311 17.0679 10.4949 16.9473 10.4262C16.7794 10.3312 16.5893 10.2824 16.3964 10.2847C15.8236 10.2904 15.343 10.7484 15.3115 11.3203C15.2895 11.7196 15.4811 12.0755 15.7823 12.2846C15.9331 12.3893 16.0214 12.5626 16.0214 12.7462V17.7868C16.0214 17.9704 15.9331 18.1437 15.7823 18.2484C15.4811 18.4575 15.2895 18.8134 15.3115 19.2127C15.343 19.7846 15.8236 20.2426 16.3964 20.2483C16.5893 20.2506 16.7794 20.2018 16.9473 20.1068C17.0679 20.0381 17.2042 20.0019 17.343 20.0019C17.4818 20.0019 17.6182 20.0381 17.7388 20.1068C17.9067 20.2018 18.0968 20.2506 18.2897 20.2483C18.8625 20.2426 19.3431 19.7845 19.3746 19.2126C19.3965 18.8134 19.205 18.4574 18.9038 18.2484C18.753 18.1437 18.6647 17.9704 18.6647 17.7868Z" stroke="#111111" stroke-width="0.7" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M19.7371 7.81078L20.2331 8.31141C20.5937 8.67533 20.796 9.1669 20.796 9.67922V20.2481C20.796 21.053 20.1435 21.7055 19.3387 21.7055H4.6612C3.85636 21.7055 3.20386 21.053 3.20386 20.2481V9.67922C3.20386 9.16687 3.40636 8.67516 3.76683 8.31141L6.10495 5.95219V4.06875M17.895 4.08141V5.95219L18.5821 6.64547M7.81411 0.398484V3.91181M10.6045 3.91172V3.00094M10.6045 1.36031V0.398437M13.3952 0.398484V3.91181M16.1857 0.398484V3.91181M18.3975 0.351562H5.60245C5.3437 0.351562 5.1337 0.561562 5.1337 0.820312V3.54047C5.1337 3.79922 5.3437 4.00922 5.60245 4.00922H18.3975C18.6562 4.00922 18.8662 3.79922 18.8662 3.54047V0.820312C18.8662 0.561562 18.6562 0.351562 18.3975 0.351562Z" stroke="#111111" stroke-width="0.7" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                                        </svg>
                                                    </div>
                                                    <div class="body-text">1,638</div>
                                                    <div class="body-text">20</div>
                                                    <div class="body-text">20 Nov 2023</div>
                                                    <div class="list-icon-function">
                                                        <div class="item eye">
                                                            <i class="icon-eye"></i>
                                                        </div>
                                                        <div class="item edit">
                                                            <i class="icon-edit-3"></i>
                                                        </div>
                                                        <div class="item trash">
                                                            <i class="icon-trash-2"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="product-item gap14">
                                                <div class="image no-bg">
                                                    <img src="images/products/57.png" alt="">
                                                </div>
                                                <div class="flex items-center justify-between gap20 flex-grow">
                                                    <div class="name">
                                                        <a href="product-list.html" class="body-title-2">Kitten food</a>
                                                    </div>
                                                    <div>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                            <g clip-path="url(#clip0_2774_2556)">
                                                              <path d="M20.8511 11.0949C21.048 11.0949 21.2075 10.9353 21.2075 10.7384V8.11141C21.2075 6.85202 20.246 5.8129 19.0186 5.68966V1.57765C19.0186 0.707742 18.3109 2.34394e-05 17.441 2.34394e-05H9.9247C9.72783 2.34394e-05 9.56836 0.15968 9.56836 0.356508C9.56836 0.553336 9.72783 0.712852 9.9247 0.712852H17.441C17.9178 0.712852 18.3058 1.10084 18.3058 1.5777V5.6772H5.7143V1.57765C5.7143 1.10084 6.10214 0.712852 6.57914 0.712852H8.73647C8.78362 0.713392 8.83041 0.704573 8.87412 0.686904C8.91784 0.669235 8.95761 0.643068 8.99115 0.609918C9.02468 0.576768 9.0513 0.537295 9.06947 0.493784C9.08764 0.450273 9.09699 0.40359 9.09699 0.356438C9.09699 0.309285 9.08764 0.262602 9.06947 0.219091C9.0513 0.17558 9.02468 0.136107 8.99115 0.102957C8.95761 0.0698073 8.91784 0.0436402 8.87412 0.0259712C8.83041 0.00830225 8.78362 -0.00051728 8.73647 2.34394e-05H6.57914C5.70905 2.34394e-05 5.00133 0.707742 5.00133 1.57765V5.68966C3.774 5.8129 2.8125 6.85202 2.8125 8.11141V21.566C2.8125 22.9082 3.90436 24 5.24653 24H18.7734C20.1156 24 21.2075 22.9082 21.2075 21.566V11.9266C21.2075 11.7297 21.048 11.5703 20.8511 11.5703C20.6543 11.5703 20.4946 11.7297 20.4946 11.9266V21.566C20.4946 22.515 19.7225 23.2872 18.7734 23.2872H5.24653C4.2975 23.2872 3.52533 22.515 3.52533 21.566V8.11141C3.52533 7.16238 4.2975 6.39021 5.24653 6.39021H18.7734C19.7225 6.39021 20.4946 7.16238 20.4946 8.11141V10.7384C20.4946 10.9353 20.6543 11.0949 20.8511 11.0949Z" fill="#111111"/>
                                                              <path d="M6.38407 1.6854V4.75056C6.38404 4.79737 6.39323 4.84373 6.41113 4.88699C6.42902 4.93024 6.45526 4.96955 6.48836 5.00266C6.52145 5.03577 6.56074 5.06204 6.60399 5.07996C6.64724 5.09787 6.6936 5.10709 6.74041 5.10709C6.78723 5.1071 6.83359 5.0979 6.87685 5.07999C6.92011 5.06208 6.95942 5.03582 6.99252 5.00272C7.02563 4.96961 7.05189 4.9303 7.0698 4.88704C7.0877 4.84379 7.09691 4.79742 7.09689 4.7506V1.68545C7.09689 1.48857 6.93743 1.3291 6.74041 1.3291C6.69361 1.32909 6.64728 1.3383 6.60404 1.3562C6.5608 1.3741 6.52152 1.40034 6.48843 1.43343C6.45533 1.46652 6.42909 1.5058 6.41118 1.54903C6.39327 1.59227 6.38406 1.6386 6.38407 1.6854ZM7.89124 1.6854V4.75056C7.89121 4.79738 7.90042 4.84375 7.91832 4.88701C7.93623 4.93028 7.96249 4.96959 7.99559 5.0027C8.0287 5.03581 8.06801 5.06207 8.11127 5.07998C8.15453 5.09789 8.2009 5.1071 8.24772 5.10709C8.4446 5.10709 8.60407 4.94762 8.60407 4.7506V1.68545C8.60408 1.63865 8.59487 1.5923 8.57697 1.54906C8.55906 1.50582 8.53282 1.46654 8.49972 1.43344C8.46663 1.40035 8.42734 1.37411 8.3841 1.3562C8.34087 1.3383 8.29452 1.32909 8.24772 1.3291C8.20092 1.32908 8.15457 1.33827 8.11132 1.35617C8.06807 1.37406 8.02877 1.4003 7.99566 1.43339C7.96256 1.46648 7.9363 1.50577 7.91838 1.54901C7.90046 1.59225 7.89124 1.63859 7.89124 1.6854ZM9.75485 5.10713C9.95172 5.10713 10.1112 4.94762 10.1112 4.7506V1.68545C10.1112 1.48857 9.95172 1.3291 9.75485 1.3291C9.55797 1.3291 9.39836 1.48857 9.39836 1.6854V4.75056C9.39835 4.79737 9.40756 4.84374 9.42547 4.887C9.44338 4.93026 9.46964 4.96956 9.50275 5.00267C9.53585 5.03578 9.57515 5.06204 9.61841 5.07996C9.66167 5.09787 9.70803 5.10714 9.75485 5.10713ZM11.6184 4.7506V1.68545C11.6184 1.48857 11.4587 1.3291 11.2618 1.3291C11.215 1.32909 11.1687 1.3383 11.1255 1.3562C11.0822 1.3741 11.0429 1.40034 11.0098 1.43343C10.9768 1.46652 10.9505 1.5058 10.9326 1.54903C10.9147 1.59227 10.9055 1.6386 10.9055 1.6854V4.75056C10.9049 4.7977 10.9138 4.84449 10.9314 4.88821C10.9491 4.93192 10.9753 4.9717 11.0084 5.00523C11.0416 5.03876 11.081 5.06539 11.1246 5.08356C11.1681 5.10172 11.2148 5.11108 11.2619 5.11108C11.3091 5.11108 11.3557 5.10172 11.3992 5.08356C11.4428 5.06539 11.4822 5.03876 11.5154 5.00523C11.5485 4.9717 11.5747 4.93192 11.5924 4.88821C11.61 4.84449 11.6189 4.7977 11.6183 4.75056L11.6184 4.7506ZM13.1255 4.7506V1.68545C13.1255 1.48857 12.966 1.3291 12.769 1.3291C12.7222 1.3291 12.6758 1.33831 12.6326 1.35621C12.5894 1.37411 12.5501 1.40036 12.517 1.43344C12.4839 1.46653 12.4577 1.50581 12.4398 1.54904C12.4219 1.59227 12.4127 1.63861 12.4127 1.6854V4.75056C12.4126 4.79736 12.4218 4.84372 12.4397 4.88698C12.4576 4.93023 12.4838 4.96954 12.5169 5.00265C12.55 5.03576 12.5893 5.06202 12.6326 5.07994C12.6758 5.09786 12.7221 5.10709 12.769 5.10709C12.8158 5.10711 12.8622 5.09791 12.9054 5.08C12.9487 5.0621 12.988 5.03584 13.0211 5.00273C13.0542 4.96962 13.0805 4.93032 13.0984 4.88705C13.1163 4.84379 13.1255 4.79743 13.1255 4.7506ZM14.6326 4.7506V1.68545C14.6326 1.63865 14.6234 1.59231 14.6055 1.54907C14.5876 1.50584 14.5614 1.46655 14.5283 1.43346C14.4952 1.40037 14.4559 1.37412 14.4127 1.35622C14.3695 1.33831 14.3231 1.3291 14.2763 1.3291C14.2295 1.32907 14.1832 1.33826 14.1399 1.35616C14.0966 1.37405 14.0573 1.40029 14.0242 1.43338C13.9911 1.46646 13.9649 1.50575 13.9469 1.549C13.929 1.59224 13.9198 1.63859 13.9198 1.6854V4.75056C13.9198 4.79738 13.929 4.84376 13.9469 4.88702C13.9648 4.93029 13.991 4.9696 14.0242 5.00272C14.0573 5.03583 14.0966 5.06209 14.1398 5.08C14.1831 5.09791 14.2295 5.10711 14.2763 5.10709C14.4731 5.10709 14.6326 4.94762 14.6326 4.7506ZM16.1397 4.7506V1.68545C16.1398 1.63865 16.1305 1.59231 16.1126 1.54907C16.0947 1.50584 16.0685 1.46655 16.0354 1.43346C16.0023 1.40037 15.963 1.37412 15.9198 1.35622C15.8766 1.33831 15.8302 1.3291 15.7834 1.3291C15.6889 1.32908 15.5983 1.3666 15.5314 1.43342C15.4645 1.50023 15.4269 1.59087 15.4269 1.6854V4.75056C15.4264 4.7977 15.4352 4.84449 15.4529 4.88821C15.4705 4.93192 15.4967 4.9717 15.5298 5.00523C15.563 5.03876 15.6025 5.06539 15.646 5.08356C15.6895 5.10172 15.7362 5.11108 15.7833 5.11108C15.8305 5.11108 15.8772 5.10172 15.9207 5.08356C15.9642 5.06539 16.0037 5.03876 16.0368 5.00523C16.07 4.9717 16.0961 4.93192 16.1138 4.88821C16.1315 4.84449 16.1403 4.79775 16.1397 4.7506ZM17.6469 4.7506V1.68545C17.6469 1.63865 17.6377 1.5923 17.6198 1.54906C17.6019 1.50582 17.5757 1.46654 17.5426 1.43344C17.5095 1.40035 17.4702 1.37411 17.4269 1.3562C17.3837 1.3383 17.3374 1.32909 17.2906 1.3291C17.1961 1.32909 17.1054 1.36662 17.0385 1.43343C16.9717 1.50025 16.9341 1.59088 16.9341 1.6854V4.75056C16.9335 4.7977 16.9424 4.84449 16.96 4.88821C16.9777 4.93192 17.0039 4.9717 17.037 5.00523C17.0702 5.03876 17.1096 5.06539 17.1532 5.08356C17.1967 5.10172 17.2433 5.11108 17.2905 5.11108C17.3376 5.11108 17.3843 5.10172 17.4278 5.08356C17.4714 5.06539 17.5108 5.03876 17.544 5.00523C17.5771 4.9717 17.6033 4.93192 17.621 4.88821C17.6386 4.84449 17.6475 4.79775 17.6469 4.7506ZM14.234 8.89955C13.1022 8.89955 12.1816 9.82023 12.1816 10.952V11.7646L8.93585 15.0104H8.12322C6.99143 15.0104 6.0708 15.931 6.0708 17.0626C6.0708 18.0646 6.80613 18.9118 7.76561 19.083C7.9368 20.0424 8.78407 20.7778 9.78602 20.7778C10.9176 20.7778 11.8384 19.8572 11.8384 18.7254V17.9128L15.084 14.6672H15.8968C17.0283 14.6672 17.949 13.7464 17.949 12.6146C17.949 11.6126 17.2137 10.7654 16.2542 10.5944C16.0832 9.63493 15.2359 8.89955 14.234 8.89955ZM17.2362 12.6148C17.2362 13.3534 16.6354 13.9542 15.8968 13.9544H14.9364C14.8419 13.9544 14.7513 13.9919 14.6845 14.0588L11.23 17.5132C11.1631 17.58 11.1255 17.6706 11.1254 17.7652V18.7256C11.1254 19.0833 10.9861 19.4197 10.7332 19.6728C10.4801 19.9258 10.1438 20.065 9.78602 20.065C9.06147 20.065 8.46086 19.4758 8.44713 18.7512C8.44543 18.659 8.40807 18.571 8.34288 18.5058C8.2777 18.4406 8.18977 18.4032 8.09758 18.4015C7.37304 18.3879 6.78363 17.7873 6.78363 17.0627C6.78363 16.3241 7.38457 15.7232 8.12322 15.7232H9.08341C9.1781 15.7232 9.26871 15.6856 9.3356 15.6188L12.79 12.1644C12.8568 12.0975 12.8944 12.0069 12.8944 11.9124V10.952C12.8944 10.2133 13.4953 9.61252 14.234 9.61252C14.9585 9.61252 15.5591 10.2017 15.5729 10.9263C15.5746 11.0185 15.6119 11.1065 15.6771 11.1717C15.7423 11.2369 15.8302 11.2743 15.9224 11.276C16.6468 11.2894 17.2362 11.89 17.2362 12.6148Z" fill="#111111"/>
                                                            </g>
                                                            <defs>
                                                              <clipPath id="clip0_2774_2556">
                                                                <rect width="24" height="24" fill="white"/>
                                                              </clipPath>
                                                            </defs>
                                                        </svg>
                                                    </div>
                                                    <div class="body-text">1,638</div>
                                                    <div class="body-text">20</div>
                                                    <div class="body-text">20 Nov 2023</div>
                                                    <div class="list-icon-function">
                                                        <div class="item eye">
                                                            <i class="icon-eye"></i>
                                                        </div>
                                                        <div class="item edit">
                                                            <i class="icon-edit-3"></i>
                                                        </div>
                                                        <div class="item trash">
                                                            <i class="icon-trash-2"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="product-item gap14">
                                                <div class="image no-bg">
                                                    <img src="images/products/58.png" alt="">
                                                </div>
                                                <div class="flex items-center justify-between gap20 flex-grow">
                                                    <div class="name">
                                                        <a href="product-list.html" class="body-title-2">Food for adult cats</a>
                                                    </div>
                                                    <div>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                            <g clip-path="url(#clip0_2774_3907)">
                                                              <path d="M19.5382 5.61253V7.50684C19.5382 7.60027 19.5753 7.68987 19.6414 7.75593C19.7074 7.822 19.797 7.85911 19.8904 7.85911C19.9839 7.85911 20.0735 7.822 20.1395 7.75593C20.2056 7.68987 20.2427 7.60027 20.2427 7.50684V5.69569L21.3327 3.51562C21.3571 3.4667 21.3698 3.41279 21.3699 3.35812V0.352266C21.3699 0.0679687 21.0342 -0.1005 20.8063 0.0704531L19.5147 1.03912L18.2231 0.0704531C18.1621 0.0247213 18.088 0 18.0117 0C17.9355 0 17.8614 0.0247213 17.8004 0.0704531L16.5088 1.03912L15.2172 0.0704531C15.1563 0.0247213 15.0821 0 15.0059 0C14.9297 0 14.8555 0.0247213 14.7945 0.0704531L13.5029 1.03912L12.2114 0.0704531C12.1504 0.0247213 12.0762 0 12 0C11.9238 0 11.8496 0.0247213 11.7887 0.0704531L10.4971 1.03912L9.20552 0.0704531C9.14454 0.0247213 9.07038 0 8.99416 0C8.91794 0 8.84377 0.0247213 8.7828 0.0704531L7.49121 1.03912L6.19961 0.0704531C6.13864 0.0247213 6.06447 0 5.98825 0C5.91203 0 5.83787 0.0247213 5.77689 0.0704531L4.48535 1.03912L3.19375 0.0704531C2.96585 -0.1005 2.63017 0.0679687 2.63017 0.352266V3.35812C2.63028 3.41281 2.64301 3.46673 2.66739 3.51567L3.75738 5.69573V18.3044L2.66735 20.4844C2.64296 20.5333 2.63022 20.5872 2.63013 20.6419V23.6477C2.63013 23.932 2.9658 24.1005 3.19371 23.9295L4.4853 22.9609L5.77689 23.9295C5.83787 23.9753 5.91203 24 5.98825 24C6.06447 24 6.13864 23.9753 6.19961 23.9295L7.49121 22.9609L8.7828 23.9295C8.84377 23.9753 8.91794 24 8.99416 24C9.07038 24 9.14454 23.9753 9.20552 23.9295L10.4971 22.9609L11.7887 23.9295C11.8497 23.9753 11.9238 24 12.0001 24C12.0763 24 12.1504 23.9753 12.2114 23.9295L13.503 22.9609L14.7946 23.9295C14.8556 23.9753 14.9298 24 15.006 24C15.0822 24 15.1564 23.9753 15.2173 23.9295L16.5089 22.9609L17.8005 23.9295C17.8615 23.9753 17.9357 24 18.0119 24C18.0881 24 18.1623 23.9753 18.2232 23.9295L19.5148 22.9609L20.8064 23.9295C21.0345 24.1006 21.37 23.9317 21.37 23.6477V20.6419C21.3699 20.5872 21.3572 20.5333 21.3328 20.4843L20.2428 18.3043V9.22898C20.2428 9.18272 20.2337 9.13692 20.216 9.09418C20.1983 9.05144 20.1723 9.01261 20.1396 8.97989C20.1069 8.94718 20.0681 8.92124 20.0253 8.90353C19.9826 8.88583 19.9368 8.87672 19.8905 8.87672C19.8443 8.87672 19.7985 8.88583 19.7557 8.90353C19.713 8.92124 19.6742 8.94718 19.6414 8.97989C19.6087 9.01261 19.5828 9.05144 19.5651 9.09418C19.5474 9.13692 19.5383 9.18272 19.5383 9.22898V18.3875C19.5383 18.4422 19.551 18.4962 19.5755 18.5451L20.4477 20.2897H3.55239L4.42464 18.5451C4.44912 18.4962 4.46187 18.4422 4.46186 18.3875V5.61253C4.46186 5.55784 4.44912 5.5039 4.42464 5.45498L3.55239 3.71039H20.4477L19.5754 5.45503C19.5509 5.50391 19.5382 5.55785 19.5382 5.61253ZM20.6654 20.9941V22.9432L19.726 22.2388C19.6651 22.193 19.5909 22.1683 19.5147 22.1683C19.4385 22.1683 19.3643 22.193 19.3033 22.2388L18.0117 23.2074L16.7201 22.2388C16.6575 22.1918 16.5831 22.1683 16.5088 22.1683C16.4344 22.1683 16.3601 22.1918 16.2974 22.2388L15.0058 23.2074L13.7142 22.2388C13.6533 22.193 13.5791 22.1683 13.5029 22.1683C13.4267 22.1683 13.3525 22.193 13.2915 22.2388L12 23.2074L10.7084 22.2388C10.6474 22.193 10.5733 22.1683 10.4971 22.1683C10.4208 22.1683 10.3467 22.193 10.2857 22.2388L8.99411 23.2074L7.70252 22.2388C7.64154 22.193 7.56738 22.1683 7.49116 22.1683C7.41494 22.1683 7.34078 22.193 7.2798 22.2388L5.98821 23.2074L4.69661 22.2388C4.63564 22.193 4.56147 22.1683 4.48525 22.1683C4.40903 22.1683 4.33487 22.193 4.27389 22.2388L3.33456 22.9432V20.9941H20.6654ZM3.33466 3.00586V1.05675L4.27399 1.76123C4.33496 1.80697 4.40913 1.83169 4.48535 1.83169C4.56157 1.83169 4.63573 1.80697 4.69671 1.76123L5.9883 0.792563L7.27989 1.76123C7.34087 1.80697 7.41503 1.83169 7.49125 1.83169C7.56747 1.83169 7.64164 1.80697 7.70261 1.76123L8.99421 0.792563L10.2858 1.76123C10.3468 1.80697 10.4209 1.83169 10.4972 1.83169C10.5734 1.83169 10.6475 1.80697 10.7085 1.76123L12 0.792563L13.2916 1.76123C13.3526 1.80697 13.4268 1.83169 13.503 1.83169C13.5792 1.83169 13.6534 1.80697 13.7143 1.76123L15.0059 0.792563L16.2975 1.76123C16.3585 1.80697 16.4327 1.83169 16.5089 1.83169C16.5851 1.83169 16.6593 1.80697 16.7202 1.76123L18.0118 0.792563L19.3034 1.76123C19.3644 1.80697 19.4386 1.83169 19.5148 1.83169C19.591 1.83169 19.6652 1.80697 19.7261 1.76123L20.6655 1.05675V3.00586H3.33466Z" fill="#111111"/>
                                                              <path d="M16.151 9.44312C15.8627 9.15508 15.4877 8.96995 15.0837 8.91634C15.0301 8.51237 14.845 8.1373 14.5569 7.84904C14.2065 7.4986 13.7407 7.30566 13.2451 7.30566C12.7495 7.30566 12.2836 7.4986 11.9332 7.84904C11.295 8.48724 11.2199 9.47851 11.7079 10.1999L10.0334 11.8744C9.72904 11.6677 9.37007 11.5566 8.99413 11.5566C8.49857 11.5566 8.03273 11.7496 7.68234 12.1C7.3319 12.4504 7.13892 12.9163 7.13892 13.4119C7.13892 13.9074 7.3319 14.3733 7.68229 14.7237C7.97056 15.0118 8.34565 15.1969 8.74963 15.2505C8.80326 15.6545 8.9884 16.0296 9.27646 16.3178C9.6381 16.6794 10.1132 16.8603 10.5883 16.8603C11.0633 16.8603 11.5384 16.6794 11.9001 16.3178C12.2505 15.9674 12.4435 15.5015 12.4435 15.006C12.4435 14.63 12.3324 14.271 12.1258 13.9667L13.8002 12.2922C14.5216 12.7801 15.5129 12.7051 16.1511 12.0669C16.5015 11.7165 16.6945 11.2506 16.6945 10.755C16.6944 10.2594 16.5014 9.79355 16.151 9.44312ZM15.6528 11.5686C15.2043 12.0173 14.4742 12.0174 14.0256 11.5686C13.9595 11.5026 13.8699 11.4655 13.7765 11.4655C13.683 11.4655 13.5934 11.5026 13.5274 11.5686L11.4019 13.6941C11.2663 13.8297 11.2663 14.0567 11.402 14.1923C11.5091 14.2989 11.5941 14.4257 11.652 14.5653C11.7098 14.705 11.7394 14.8547 11.739 15.0059C11.7394 15.1571 11.7098 15.3068 11.652 15.4465C11.5941 15.5862 11.5091 15.713 11.402 15.8196C10.9533 16.2681 10.2233 16.2683 9.77465 15.8196C9.65926 15.704 9.56977 15.5653 9.51211 15.4125C9.45446 15.2597 9.42996 15.0964 9.44024 14.9334C9.45337 14.7249 9.27501 14.5469 9.06665 14.5599C8.90369 14.5701 8.74042 14.5456 8.58765 14.4879C8.43489 14.4303 8.29613 14.3408 8.18057 14.2254C7.73695 13.7819 7.73695 13.0417 8.18057 12.5981C8.62406 12.1546 9.36426 12.1546 9.80779 12.5981C9.94345 12.7338 10.1703 12.7337 10.306 12.5981L12.4315 10.4726C12.4642 10.4399 12.4901 10.4011 12.5078 10.3584C12.5255 10.3156 12.5347 10.2698 12.5347 10.2236C12.5347 10.1773 12.5255 10.1315 12.5078 10.0888C12.4901 10.046 12.4642 10.0072 12.4315 9.97449C11.9828 9.5258 11.9828 8.79582 12.4315 8.34718C12.875 7.90356 13.6153 7.9037 14.0588 8.34718C14.1742 8.46271 14.2637 8.60147 14.3213 8.75424C14.379 8.90701 14.4034 9.0703 14.3932 9.23326C14.38 9.44181 14.5583 9.61984 14.7667 9.6069C14.9297 9.59668 15.093 9.62121 15.2457 9.67886C15.3985 9.73651 15.5373 9.82597 15.6529 9.94131C16.0964 10.3849 16.0964 11.125 15.6528 11.5686Z" fill="#111111"/>
                                                              <path d="M13.2287 9.49677C13.4233 9.49677 13.581 9.33905 13.581 9.1445C13.581 8.94995 13.4233 8.79224 13.2287 8.79224C13.0342 8.79224 12.8765 8.94995 12.8765 9.1445C12.8765 9.33905 13.0342 9.49677 13.2287 9.49677Z" fill="#111111"/>
                                                              <path d="M11.9497 12.4355C12.1442 12.4355 12.3019 12.2778 12.3019 12.0832C12.3019 11.8887 12.1442 11.731 11.9497 11.731C11.7551 11.731 11.5974 11.8887 11.5974 12.0832C11.5974 12.2778 11.7551 12.4355 11.9497 12.4355Z" fill="#111111"/>
                                                              <path d="M14.8059 11.1071C15.0004 11.1071 15.1581 10.9494 15.1581 10.7549C15.1581 10.5603 15.0004 10.4026 14.8059 10.4026C14.6113 10.4026 14.4536 10.5603 14.4536 10.7549C14.4536 10.9494 14.6113 11.1071 14.8059 11.1071Z" fill="#111111"/>
                                                              <path d="M8.99411 13.7307C9.18866 13.7307 9.34638 13.5729 9.34638 13.3784C9.34638 13.1838 9.18866 13.0261 8.99411 13.0261C8.79956 13.0261 8.64185 13.1838 8.64185 13.3784C8.64185 13.5729 8.79956 13.7307 8.99411 13.7307Z" fill="#111111"/>
                                                              <path d="M10.5717 15.3417C10.7663 15.3417 10.924 15.184 10.924 14.9895C10.924 14.7949 10.7663 14.6372 10.5717 14.6372C10.3772 14.6372 10.2195 14.7949 10.2195 14.9895C10.2195 15.184 10.3772 15.3417 10.5717 15.3417Z" fill="#111111"/>
                                                              <path d="M12 4.50879C11.3914 4.50879 10.8963 5.00393 10.8963 5.61251C10.8963 6.22109 11.3914 6.71623 12 6.71623C12.6086 6.71623 13.1037 6.22109 13.1037 5.61251C13.1037 5.00388 12.6086 4.50879 12 4.50879ZM12 6.0117C11.7799 6.0117 11.6008 5.83263 11.6008 5.61246C11.6008 5.39229 11.7798 5.21323 12 5.21323C12.2202 5.21323 12.3992 5.39229 12.3992 5.61246C12.3992 5.83263 12.2201 6.0117 12 6.0117ZM15.7574 4.50879C15.1488 4.50879 14.6536 5.00393 14.6536 5.61251C14.6536 6.22109 15.1488 6.71623 15.7574 6.71623C16.3659 6.71623 16.8611 6.22109 16.8611 5.61251C16.8611 5.00388 16.3659 4.50879 15.7574 4.50879ZM15.7574 6.0117C15.5372 6.0117 15.3581 5.83263 15.3581 5.61246C15.3581 5.39229 15.5372 5.21323 15.7574 5.21323C15.9775 5.21323 16.1566 5.39229 16.1566 5.61246C16.1566 5.83263 15.9775 6.0117 15.7574 6.0117ZM8.24264 4.50879C7.63406 4.50879 7.13892 5.00393 7.13892 5.61251C7.13892 6.22109 7.63406 6.71623 8.24264 6.71623C8.85121 6.71623 9.34635 6.22109 9.34635 5.61251C9.34635 5.00388 8.85126 4.50879 8.24264 4.50879ZM8.24264 6.0117C8.02251 6.0117 7.8434 5.83263 7.8434 5.61246C7.8434 5.39229 8.02246 5.21323 8.24264 5.21323C8.46281 5.21323 8.64187 5.39229 8.64187 5.61246C8.64187 5.83263 8.46281 6.0117 8.24264 6.0117ZM12 17.2837C11.3914 17.2837 10.8963 17.7789 10.8963 18.3874C10.8963 18.996 11.3914 19.4912 12 19.4912C12.6086 19.4912 13.1037 18.996 13.1037 18.3874C13.1037 17.7789 12.6086 17.2837 12 17.2837ZM12 18.7867C11.7799 18.7867 11.6008 18.6076 11.6008 18.3874C11.6008 18.1673 11.7798 17.9882 12 17.9882C12.2202 17.9882 12.3992 18.1673 12.3992 18.3874C12.3992 18.6076 12.2201 18.7867 12 18.7867ZM15.7574 17.2837C15.1488 17.2837 14.6536 17.7789 14.6536 18.3874C14.6536 18.996 15.1488 19.4912 15.7574 19.4912C16.3659 19.4912 16.8611 18.996 16.8611 18.3874C16.8611 17.7789 16.3659 17.2837 15.7574 17.2837ZM15.7574 18.7867C15.5372 18.7867 15.3581 18.6076 15.3581 18.3874C15.3581 18.1673 15.5372 17.9882 15.7574 17.9882C15.9775 17.9882 16.1566 18.1673 16.1566 18.3874C16.1565 18.6076 15.9775 18.7867 15.7574 18.7867ZM8.24264 17.2837C7.63406 17.2837 7.13892 17.7789 7.13892 18.3874C7.13892 18.996 7.63406 19.4912 8.24264 19.4912C8.85121 19.4912 9.34635 18.996 9.34635 18.3874C9.34635 17.7789 8.85126 17.2837 8.24264 17.2837ZM8.24264 18.7867C8.02251 18.7867 7.8434 18.6076 7.8434 18.3874C7.8434 18.1673 8.02246 17.9882 8.24264 17.9882C8.46281 17.9882 8.64187 18.1673 8.64187 18.3874C8.64187 18.6076 8.46281 18.7867 8.24264 18.7867Z" fill="#111111"/>
                                                            </g>
                                                            <defs>
                                                              <clipPath id="clip0_2774_3907">
                                                                <rect width="24" height="24" fill="white"/>
                                                              </clipPath>
                                                            </defs>
                                                        </svg>
                                                    </div>
                                                    <div class="body-text">1,638</div>
                                                    <div class="body-text">20</div>
                                                    <div class="body-text">20 Nov 2023</div>
                                                    <div class="list-icon-function">
                                                        <div class="item eye">
                                                            <i class="icon-eye"></i>
                                                        </div>
                                                        <div class="item edit">
                                                            <i class="icon-edit-3"></i>
                                                        </div>
                                                        <div class="item trash">
                                                            <i class="icon-trash-2"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="product-item gap14">
                                                <div class="image no-bg">
                                                    <img src="images/products/59.png" alt="">
                                                </div>
                                                <div class="flex items-center justify-between gap20 flex-grow">
                                                    <div class="name">
                                                        <a href="product-list.html" class="body-title-2">Food for elderly cats</a>
                                                    </div>
                                                    <div>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                            <path d="M21.75 18.375V7.04152C21.7498 6.78662 21.6849 6.53594 21.5612 6.31303L19.875 3.27783V1.5C19.8748 1.30116 19.7957 1.11053 19.6551 0.96993C19.5145 0.829329 19.3238 0.750236 19.125 0.75H4.875C4.67616 0.750236 4.48553 0.829329 4.34493 0.96993C4.20433 1.11053 4.12524 1.30116 4.125 1.5V3.27783L2.43872 6.31308C2.31512 6.53598 2.25018 6.78664 2.25 7.04152V7.875H3V7.04152C3.00008 6.91407 3.03255 6.78875 3.09436 6.6773L4.72064 3.75H18.8625L17.4385 6.31308C17.315 6.536 17.2501 6.78665 17.25 7.04152V13.1544L15.7042 10.3204C15.5625 10.0312 15.1435 10.1946 15.1435 10.1946C14.7764 10.3688 14.4637 10.64 14.2392 10.9787C14.0147 11.3175 13.8868 11.7111 13.8694 12.1172L11.3593 13.0585C11.3593 13.0585 10.4414 11.9531 9.15661 12.3287C8.97806 12.3809 8.58984 12.5156 8.69531 13.0195L11.8547 22.5H10.7599L9.72614 19.7433C9.69934 19.6719 9.65135 19.6103 9.58861 19.5668C9.52586 19.5233 9.45134 19.5 9.375 19.5H9C8.99955 19.1608 8.88438 18.8318 8.67321 18.5664C8.46204 18.3009 8.16729 18.1148 7.83689 18.0381C7.77319 17.7643 7.63406 17.5137 7.43528 17.3149C7.2365 17.1161 6.986 16.9769 6.71217 16.9131C6.6358 16.5826 6.44967 16.2876 6.18415 16.0765C5.91864 15.8653 5.5894 15.7503 5.25014 15.7502C4.91087 15.7502 4.58161 15.8651 4.31605 16.0763C4.0505 16.2874 3.86431 16.5823 3.78787 16.9128C3.51359 16.9759 3.2627 17.1152 3.06408 17.3145C3.04181 17.3369 3.02067 17.36 3 17.3834V8.625H2.25V21.75C2.25045 22.1477 2.40863 22.529 2.68983 22.8102C2.97104 23.0914 3.35231 23.2496 3.75 23.25H20.25C20.6477 23.2496 21.029 23.0914 21.3102 22.8102C21.5914 22.529 21.7496 22.1477 21.75 21.75V19.125H21V19.875H19.5V17.625H21V18.375H21.75ZM3 18.75C3.09946 18.75 3.19484 18.7105 3.26516 18.6402C3.33549 18.5698 3.375 18.4745 3.375 18.375C3.375 18.1761 3.45402 17.9853 3.59467 17.8447C3.73532 17.704 3.92609 17.625 4.125 17.625C4.22446 17.625 4.31984 17.5855 4.39016 17.5152C4.46049 17.4448 4.5 17.3495 4.5 17.25C4.5 17.0511 4.57902 16.8603 4.71967 16.7197C4.86032 16.579 5.05109 16.5 5.25 16.5C5.44891 16.5 5.63968 16.579 5.78033 16.7197C5.92098 16.8603 6 17.0511 6 17.25C6 17.3495 6.03951 17.4448 6.10983 17.5152C6.18016 17.5855 6.27554 17.625 6.375 17.625C6.57384 17.6252 6.76447 17.7043 6.90507 17.8449C7.04567 17.9855 7.12476 18.1762 7.125 18.375C7.125 18.4745 7.16451 18.5698 7.23483 18.6402C7.30516 18.7105 7.40054 18.75 7.5 18.75C7.69884 18.7502 7.88947 18.8293 8.03007 18.9699C8.17067 19.1105 8.24976 19.3012 8.25 19.5H3V18.75ZM3.75 22.5C3.55116 22.4998 3.36053 22.4207 3.21993 22.2801C3.07933 22.1395 3.00024 21.9488 3 21.75V20.25H9.11512L9.95887 22.5H3.75ZM17.25 22.5H12.6453L9.48895 13.0309C9.72466 13.0033 9.96354 13.033 10.1853 13.1175C10.4071 13.202 10.6052 13.3388 10.7627 13.5163C10.9255 13.6994 11.0966 13.958 11.3815 13.8511L14.3815 12.7261C14.4584 12.6973 14.5237 12.644 14.5674 12.5744C14.6111 12.5048 14.6307 12.4228 14.6233 12.341C14.6007 12.0863 14.6445 11.8301 14.7503 11.5973C14.8562 11.3645 15.0205 11.1631 15.2273 11.0126L17.25 14.7206V22.5ZM4.875 3V1.5H19.125V3H4.875ZM19.125 16.875C19.0255 16.875 18.9302 16.9145 18.8598 16.9848C18.7895 17.0552 18.75 17.1505 18.75 17.25V20.25C18.75 20.3495 18.7895 20.4448 18.8598 20.5152C18.9302 20.5855 19.0255 20.625 19.125 20.625H21V21.75C20.9998 21.9488 20.9207 22.1395 20.7801 22.2801C20.6395 22.4207 20.4488 22.4998 20.25 22.5H18V7.04152C18.0001 6.91407 18.0325 6.78875 18.0944 6.6773L19.5 4.14717L20.9056 6.67725C20.9674 6.7887 20.9999 6.91405 21 7.04152V10.875H18.75V11.625H21V12.375H18.75V13.125H21V13.875H18.75V14.625H21V15.375H18.75V16.125H21V16.875H19.125Z" fill="#111111"/>
                                                            <path d="M9.83789 9.75118C10.4023 9.75091 10.9653 9.69481 11.5186 9.5837C11.6542 9.55626 11.7762 9.48285 11.8639 9.37586C11.9516 9.26888 11.9997 9.13488 12 8.99654V6.18001C11.9883 5.90626 11.6836 5.7422 11.3715 5.84828C10.3051 6.06305 9.20545 6.05134 8.14383 5.81392C6.98031 5.55382 5.77513 5.54104 4.60636 5.77642C4.47077 5.80384 4.34882 5.87724 4.26111 5.98421C4.1734 6.09117 4.12532 6.22515 4.125 6.36348V9.18001C4.14844 9.56251 4.55859 9.57423 4.75345 9.51174C5.81989 9.29703 6.91954 9.30872 7.98117 9.54606C8.59072 9.68208 9.21335 9.75087 9.83789 9.75118ZM6.28711 8.60884C5.81395 8.60899 5.34162 8.64848 4.875 8.72692V6.4884C5.9037 6.29815 6.96024 6.31776 7.98117 6.54606C9.0549 6.78622 10.165 6.81577 11.25 6.63306V8.87162C10.2213 9.06188 9.16475 9.04225 8.14383 8.81392C7.53427 8.67792 6.91165 8.60915 6.28711 8.60884Z" fill="#111111"/>
                                                            <path d="M12.1875 16.5C12.4982 16.5 12.75 16.2482 12.75 15.9375C12.75 15.6268 12.4982 15.375 12.1875 15.375C11.8768 15.375 11.625 15.6268 11.625 15.9375C11.625 16.2482 11.8768 16.5 12.1875 16.5Z" fill="#111111"/>
                                                            <path d="M15.1875 15.375C15.4982 15.375 15.75 15.1232 15.75 14.8125C15.75 14.5018 15.4982 14.25 15.1875 14.25C14.8768 14.25 14.625 14.5018 14.625 14.8125C14.625 15.1232 14.8768 15.375 15.1875 15.375Z" fill="#111111"/>
                                                            <path d="M15.363 16.956C15.248 16.9981 15.1223 17.0006 15.0057 16.9633C14.8891 16.9259 14.7883 16.8508 14.7192 16.7498C14.7892 16.688 14.8427 16.6098 14.8749 16.5221C14.9071 16.4345 14.9169 16.3402 14.9035 16.2478C14.8901 16.1554 14.8539 16.0678 14.7982 15.9929C14.7424 15.918 14.6689 15.8581 14.5843 15.8188C14.3184 15.6951 14.053 15.8372 13.8018 15.9289C13.6618 15.9802 13.5478 16.0849 13.4849 16.2201C13.422 16.3553 13.4153 16.5099 13.4662 16.6501C13.5061 16.7555 13.5762 16.8468 13.6678 16.9125C13.7594 16.9782 13.8684 17.0154 13.981 17.0194C13.992 17.1312 13.9685 17.2437 13.9137 17.3419C13.859 17.44 13.7755 17.5191 13.6746 17.5685C13.5737 17.618 13.4601 17.6354 13.349 17.6185C13.2379 17.6017 13.1346 17.5512 13.0529 17.4741L12.8599 17.2916L12.3447 17.8366L12.5377 18.0191C12.7143 18.1837 12.9327 18.2966 13.1692 18.3455C13.4056 18.3943 13.651 18.3772 13.8783 18.2961C14.0392 18.2379 14.1869 18.1484 14.3129 18.0327C14.439 17.917 14.5408 17.7775 14.6125 17.6222L14.6166 17.6135C14.8036 17.7023 15.0092 17.7451 15.2161 17.7385C15.4231 17.7319 15.6255 17.676 15.8065 17.5754C15.9875 17.4749 16.1419 17.3326 16.2569 17.1604C16.3718 16.9881 16.444 16.791 16.4675 16.5852L16.4976 16.3214L15.7523 16.2362L15.7221 16.5001C15.7108 16.6021 15.6712 16.6988 15.6077 16.7794C15.5442 16.86 15.4595 16.9212 15.363 16.956ZM4.125 10.5H4.875V11.25H4.125V10.5ZM5.625 10.5H7.875V11.25H5.625V10.5ZM4.125 12H4.875V12.75H4.125V12ZM5.625 12H7.875V12.75H5.625V12ZM4.125 13.5H4.875V14.25H4.125V13.5ZM5.625 13.5H7.875V14.25H5.625V13.5Z" fill="#111111"/>
                                                        </svg>
                                                    </div>
                                                    <div class="body-text">1,638</div>
                                                    <div class="body-text">20</div>
                                                    <div class="body-text">20 Nov 2023</div>
                                                    <div class="list-icon-function">
                                                        <div class="item eye">
                                                            <i class="icon-eye"></i>
                                                        </div>
                                                        <div class="item edit">
                                                            <i class="icon-edit-3"></i>
                                                        </div>
                                                        <div class="item trash">
                                                            <i class="icon-trash-2"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="product-item gap14">
                                                <div class="image no-bg">
                                                    <img src="images/products/60.png" alt="">
                                                </div>
                                                <div class="flex items-center justify-between gap20 flex-grow">
                                                    <div class="name">
                                                        <a href="product-list.html" class="body-title-2">Special pet food</a>
                                                    </div>
                                                    <div>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                            <path d="M13.9395 17.6183C13.4797 17.6183 13.0297 17.4631 12.6338 17.1508C12.4515 17.0071 12.2264 16.9278 11.9999 16.9278C11.7735 16.9278 11.5483 17.0069 11.3661 17.1508C10.529 17.8112 9.44911 17.7694 8.5482 17.0417C8.42652 16.9434 8.28025 16.7696 8.20065 16.6287C7.69756 15.7381 7.60475 14.7265 7.94592 13.853C8.2289 13.1285 8.77118 12.5905 9.43392 12.3767C9.61832 12.3173 9.76247 12.1726 9.81959 11.9897C10.1492 10.9339 11.0254 10.2245 11.9999 10.2245C12.9745 10.2245 13.8508 10.9339 14.1804 11.9897C14.2376 12.1726 14.3817 12.3172 14.5661 12.3767C15.2289 12.5905 15.7711 13.1285 16.0541 13.853C16.3953 14.7266 16.3025 15.7383 15.7994 16.6287C15.7198 16.7696 15.5737 16.9434 15.4518 17.0417C14.9769 17.4252 14.452 17.6183 13.9395 17.6183ZM11.9999 16.1988C12.3896 16.1988 12.7751 16.3337 13.0854 16.5784C13.6576 17.0297 14.353 16.9919 14.9936 16.4747C15.0394 16.4376 15.1272 16.336 15.1643 16.2704C15.5591 15.5713 15.6359 14.7871 15.3747 14.1184C15.1717 13.5988 14.7953 13.217 14.3421 13.0708C13.9325 12.9388 13.6117 12.6158 13.4841 12.2071C13.2502 11.4574 12.6536 10.9538 11.9996 10.9538C11.3457 10.9538 10.7492 11.4574 10.5151 12.2071C10.3875 12.6158 10.0669 12.9388 9.65721 13.0708C9.20394 13.2169 8.82754 13.5988 8.6246 14.1184C8.36349 14.7871 8.4402 15.5713 8.83498 16.2702C8.8722 16.336 8.95999 16.4376 9.00587 16.4747C9.34946 16.7522 9.70901 16.8918 10.0567 16.8918C10.3572 16.8918 10.6488 16.7877 10.914 16.5786C11.2248 16.3335 11.6103 16.1988 11.9999 16.1988ZM15.8835 11.7994H15.8691C15.4268 11.7956 15.0479 11.613 14.8295 11.2983C14.6112 10.9835 14.5728 10.5647 14.7241 10.1491C14.8616 9.77167 15.142 9.4217 15.5137 9.16378C15.8854 8.90585 16.3114 8.7661 16.7131 8.76914C17.1554 8.77294 17.5342 8.95552 17.7527 9.27026C17.9711 9.58499 18.0094 10.0038 17.8581 10.4194C17.7206 10.7968 17.4402 11.1468 17.0685 11.4047C16.7012 11.6595 16.2809 11.7994 15.8835 11.7994ZM16.6988 9.4981C16.4519 9.4981 16.172 9.59426 15.9293 9.76271C15.421 10.1154 15.2574 10.636 15.4286 10.8825C15.5402 11.0434 15.7564 11.0692 15.8753 11.0701H15.8834C16.1302 11.0701 16.4102 10.974 16.6529 10.8055C17.1612 10.4528 17.3248 9.93223 17.1536 9.6857C17.0419 9.52484 16.8258 9.49902 16.7068 9.4981H16.6988ZM13.4447 9.80722C13.28 9.80722 13.1202 9.77289 12.9735 9.70378C12.627 9.54033 12.3842 9.19689 12.3075 8.76124C12.2378 8.36555 12.3057 7.92231 12.4986 7.51309C12.6915 7.10388 12.9905 6.76955 13.34 6.57162C13.7249 6.3538 14.1444 6.32251 14.4908 6.4858C14.8371 6.64909 15.08 6.99269 15.1567 7.42833C15.2264 7.82403 15.1585 8.26727 14.9656 8.67648C14.7727 9.0857 14.4738 9.42003 14.1242 9.61795C13.9023 9.74357 13.6689 9.80722 13.4447 9.80722ZM14.0185 7.11056C13.8921 7.11056 13.772 7.16509 13.6992 7.2061C13.4828 7.32869 13.2856 7.55395 13.1582 7.82403C13.0307 8.09426 12.9824 8.3897 13.0256 8.63456C13.0462 8.75167 13.1073 8.96054 13.2844 9.04408C13.3313 9.06626 13.3848 9.07719 13.4424 9.07719C13.7176 9.07719 14.0879 8.82808 14.3061 8.3654C14.4335 8.09517 14.4818 7.79972 14.4385 7.55486C14.4179 7.43775 14.3568 7.22889 14.1797 7.14534C14.1271 7.12043 14.0721 7.11056 14.0185 7.11056ZM8.11635 11.7994C7.71883 11.7994 7.29868 11.6595 6.93139 11.4047C6.55969 11.1468 6.27928 10.7968 6.14182 10.4194C5.99052 10.0038 6.02895 9.58484 6.24723 9.27026C6.46551 8.95567 6.8445 8.77294 7.28683 8.76914C7.6883 8.7658 8.11452 8.90585 8.48622 9.16378C8.85792 9.4217 9.13832 9.77167 9.27579 10.1491C9.42708 10.5647 9.38865 10.9837 9.17037 11.2983C8.95209 11.6128 8.57311 11.7956 8.13078 11.7994H8.11635ZM7.30111 9.4981H7.29306C7.17412 9.49917 6.95797 9.52484 6.84632 9.6857C6.73468 9.84656 6.78617 10.0582 6.82688 10.1698C6.91194 10.4036 7.10151 10.6352 7.34698 10.8055C7.85523 11.1582 8.40009 11.129 8.57128 10.8825C8.74247 10.636 8.57888 10.1153 8.07063 9.76271C7.82789 9.59426 7.54794 9.4981 7.30111 9.4981ZM10.5552 9.80722C10.3309 9.80722 10.0976 9.74357 9.87549 9.61795C9.52582 9.42003 9.22703 9.0857 9.03412 8.67648C8.84121 8.26727 8.77331 7.82403 8.84303 7.42833C8.91974 6.99269 9.16247 6.64924 9.50895 6.4858C9.85544 6.32251 10.2748 6.35365 10.6597 6.57162C11.0094 6.76955 11.3082 7.10388 11.5011 7.51309C11.694 7.92231 11.7619 8.36555 11.6922 8.76124C11.6155 9.19689 11.3728 9.54033 11.0263 9.70378C10.8797 9.77289 10.7199 9.80722 10.5552 9.80722ZM9.98136 7.11056C9.92759 7.11056 9.87275 7.12043 9.81989 7.14534C9.64278 7.22889 9.58171 7.43775 9.56106 7.55486C9.51792 7.79988 9.56622 8.09532 9.69351 8.3654C9.8208 8.63547 10.0181 8.86074 10.2346 8.98332C10.338 9.0418 10.5381 9.12762 10.7153 9.04423C10.9869 8.91618 11.1056 8.38362 10.8417 7.82403C10.7143 7.5538 10.5171 7.32869 10.3007 7.2061C10.228 7.16509 10.1079 7.11056 9.98136 7.11056ZM4.45513 21.6804C4.39468 21.6804 4.33331 21.6653 4.2768 21.6336C4.10136 21.5349 4.03908 21.3126 4.13766 21.1372C4.64835 20.2291 4.79007 19.1741 4.53685 18.1665C4.03073 16.1522 3.77417 14.0774 3.77417 12.0002C3.77417 9.92281 4.03073 7.84818 4.53685 5.83385C4.79007 4.82631 4.6482 3.77122 4.13751 2.86317C4.03878 2.68772 4.10106 2.46535 4.27665 2.36676C4.45209 2.26803 4.67447 2.33031 4.77306 2.5059C5.37549 3.57724 5.54273 4.82236 5.24394 6.01157C4.25964 9.92935 4.25964 14.0712 5.24394 17.9889C5.54273 19.1783 5.37549 20.4233 4.77306 21.4946C4.70637 21.6134 4.58257 21.6804 4.45513 21.6804ZM19.5448 21.6804C19.4173 21.6804 19.2935 21.6134 19.2267 21.4945C18.6242 20.4231 18.457 19.178 18.7558 17.9888C19.7401 14.0709 19.7401 9.92919 18.7558 6.01142C18.457 4.82205 18.6242 3.57709 19.2267 2.50575C19.3254 2.33031 19.5476 2.26803 19.7231 2.36661C19.8985 2.46535 19.9608 2.68757 19.8622 2.86302C19.3515 3.77107 19.2098 4.82616 19.4629 5.8337C19.969 7.84803 20.2256 9.92281 20.2256 12C20.2256 14.0772 19.9689 16.152 19.4629 18.1664C19.2097 19.1739 19.3515 20.229 19.8622 21.137C19.961 21.3125 19.8987 21.5349 19.7231 21.6334C19.6666 21.6653 19.6052 21.6804 19.5448 21.6804Z" fill="#111111"/>
                                                            <path d="M19.5444 3.04881H4.45538C4.25396 3.04881 4.09082 2.88567 4.09082 2.68425V0.698785C4.09082 0.497368 4.25396 0.334229 4.45538 0.334229H19.5444C19.7458 0.334229 19.9089 0.497368 19.9089 0.698785V2.68425C19.9089 2.88567 19.7457 3.04881 19.5444 3.04881ZM4.81993 2.3197H19.1798V1.06334H4.81993V2.3197ZM19.5444 23.6659H4.45538C4.25396 23.6659 4.09082 23.5027 4.09082 23.3013V21.3158C4.09082 21.1144 4.25396 20.9513 4.45538 20.9513H19.5444C19.7458 20.9513 19.9089 21.1144 19.9089 21.3158V23.3013C19.9089 23.5027 19.7457 23.6659 19.5444 23.6659ZM4.81993 22.9368H19.1798V21.6804H4.81993V22.9368Z" fill="#111111"/>
                                                        </svg>
                                                    </div>
                                                    <div class="body-text">1,638</div>
                                                    <div class="body-text">20</div>
                                                    <div class="body-text">20 Nov 2023</div>
                                                    <div class="list-icon-function">
                                                        <div class="item eye">
                                                            <i class="icon-eye"></i>
                                                        </div>
                                                        <div class="item edit">
                                                            <i class="icon-edit-3"></i>
                                                        </div>
                                                        <div class="item trash">
                                                            <i class="icon-trash-2"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="divider"></div>
                                    <div class="flex items-center justify-between flex-wrap gap10">
                                        <div class="text-tiny">Showing 10 entries</div>
                                        <ul class="wg-pagination">
                                            <li>
                                                <a href="#"><i class="icon-chevron-left"></i></a>
                                            </li>
                                            <li>
                                                <a href="#">1</a>
                                            </li>
                                            <li class="active">
                                                <a href="#">2</a>
                                            </li>
                                            <li>
                                                <a href="#">3</a>
                                            </li>
                                            <li>
                                                <a href="#"><i class="icon-chevron-right"></i></a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <!-- /all-category -->
                            </div>
                            <!-- /main-content-wrap -->
                        </div>
                        <!-- /main-content-wrap -->
                        <!-- bottom-page -->
                        <div class="bottom-page">
                            <div class="body-text">Copyright © 2024 Remos. Design with</div>
                            <i class="icon-heart"></i>
                            <div class="body-text">by <a href="https://themeforest.net/user/themesflat/portfolio">Themesflat</a> All rights reserved.</div>
                        </div>
                        <!-- /bottom-page -->
                    </div>
                    <!-- /main-content -->
                </div>
                <!-- /section-content-right -->
            </div>
            <!-- /layout-wrap -->
        </div>
        <!-- /#page -->
    </div>
    <!-- /#wrapper -->

    <!-- Javascript -->
    <script src="js/jquery.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/bootstrap-select.min.js"></script>
    <script src="js/zoom.js"></script>
    <!-- <script src="js/switcher.js"></script> -->
    <script src="js/theme-settings.js"></script>
    <script src="js/main.js"></script>

</body>

</html>