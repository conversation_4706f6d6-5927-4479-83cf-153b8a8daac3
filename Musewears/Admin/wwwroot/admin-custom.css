/* Custom Admin Styles */

/* Status badges */
.block-available {
    background-color: #22c55e;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.block-not-available {
    background-color: #ef4444;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.block-pending {
    background-color: #f59e0b;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.block-warning {
    background-color: #f97316;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

/* Text colors */
.text-danger {
    color: #ef4444 !important;
}

.text-success {
    color: #22c55e !important;
}

.text-warning {
    color: #f59e0b !important;
}

/* Spinner */
.spinner-border {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    vertical-align: text-bottom;
    border: 0.125em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}

.spinner-border-sm {
    width: 0.875rem;
    height: 0.875rem;
    border-width: 0.125em;
}

@keyframes spinner-border {
    to {
        transform: rotate(360deg);
    }
}

/* Form validation */
.validation-message {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Responsive utilities */
.w-half {
    width: 50%;
}

.w-quarter {
    width: 25%;
}

.w-full {
    width: 100%;
}

/* Flexbox utilities */
.flex {
    display: flex;
}

.flex-column {
    flex-direction: column;
}

.flex-wrap {
    flex-wrap: wrap;
}

.flex-grow {
    flex-grow: 1;
}

.items-center {
    align-items: center;
}

.justify-between {
    justify-content: space-between;
}

.justify-start {
    justify-content: flex-start;
}

.justify-center {
    justify-content: center;
}

/* Gap utilities */
.gap10 {
    gap: 10px;
}

.gap14 {
    gap: 14px;
}

.gap20 {
    gap: 20px;
}

.gap22 {
    gap: 22px;
}

.gap40 {
    gap: 40px;
}

/* Margin utilities */
.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-10 {
    margin-bottom: 10px;
}

.mb-14 {
    margin-bottom: 14px;
}

.mb-20 {
    margin-bottom: 20px;
}

.mb-27 {
    margin-bottom: 27px;
}

.mb-30 {
    margin-bottom: 30px;
}

.mt-3 {
    margin-top: 0.75rem;
}

.me-2 {
    margin-right: 0.5rem;
}

/* Padding utilities */
.p-4 {
    padding: 1rem;
}

/* Text utilities */
.text-center {
    text-align: center;
}

.text-tiny {
    font-size: 0.75rem;
}

.body-text {
    font-size: 0.875rem;
    color: #6b7280;
}

.body-title {
    font-weight: 600;
    color: #111827;
}

.body-title-2 {
    font-weight: 500;
    color: #111827;
    text-decoration: none;
}

.body-title-2:hover {
    color: #2563eb;
}

/* Button utilities */
.btn-primary {
    background-color: #2563eb;
    border-color: #2563eb;
    color: white;
}

.btn-secondary {
    background-color: #6b7280;
    border-color: #6b7280;
    color: white;
}

/* Dropdown */
.dropdown-toggle::after {
    margin-left: 0.5rem;
}

/* Table utilities */
.table-title {
    list-style: none;
    padding: 0;
    margin: 0;
    font-weight: 600;
}

.table-title li {
    flex: 1;
}

/* Product item */
.product-item {
    list-style: none;
    padding: 15px 0;
    border-bottom: 1px solid #f3f4f6;
    align-items: center;
}

.product-item:last-child {
    border-bottom: none;
}

.product-item .image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.product-item .image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-item .name {
    min-width: 200px;
}

/* Icon functions */
.list-icon-function {
    display: flex;
    gap: 8px;
}

.list-icon-function .item {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.list-icon-function .item.edit {
    background-color: #dbeafe;
    color: #2563eb;
}

.list-icon-function .item.trash {
    background-color: #fee2e2;
    color: #dc2626;
}

.list-icon-function .item.eye {
    background-color: #f3f4f6;
    color: #6b7280;
}

.list-icon-function .item:hover {
    opacity: 0.8;
}

.list-icon-function .item a {
    color: inherit;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

/* Responsive */
@media (max-width: 768px) {
    .w-half {
        width: 100%;
    }
    
    .w-quarter {
        width: 50%;
    }
    
    .cols {
        flex-direction: column;
    }
    
    .gap22 {
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .w-quarter {
        width: 100%;
    }
}
