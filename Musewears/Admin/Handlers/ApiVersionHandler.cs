using System.Web;

namespace Admin.Handlers;

public class ApiVersionHandler : DelegatingHandler
{
    private readonly string _apiVersion;

    public ApiVersionHandler(string apiVersion = "2.0")
    {
        _apiVersion = apiVersion;
    }

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        // Add api-version query parameter to the request
        if (request.RequestUri != null)
        {
            var uriBuilder = new UriBuilder(request.RequestUri);
            var query = HttpUtility.ParseQueryString(uriBuilder.Query);
            
            // Only add if not already present
            if (string.IsNullOrEmpty(query["api-version"]))
            {
                query["api-version"] = _apiVersion;
                uriBuilder.Query = query.ToString();
                request.RequestUri = uriBuilder.Uri;
            }
        }

        return await base.SendAsync(request, cancellationToken);
    }
}
