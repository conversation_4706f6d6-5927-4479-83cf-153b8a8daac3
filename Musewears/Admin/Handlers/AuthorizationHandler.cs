using System.Net.Http.Headers;
using Microsoft.AspNetCore.Components.Authorization;
using Admin.Authentication;

namespace Admin.Handlers;

public class AuthorizationHandler : DelegatingHandler
{
    private readonly AuthenticationStateProvider _authenticationStateProvider;

    public AuthorizationHandler(AuthenticationStateProvider authenticationStateProvider)
    {
        _authenticationStateProvider = authenticationStateProvider;
    }

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        try
        {
            // Get token from authentication state provider (no JavaScript interop needed)
            if (_authenticationStateProvider is AdminAuthenticationStateProvider adminProvider)
            {
                var token = adminProvider.GetCurrentToken();

                if (!string.IsNullOrEmpty(token))
                {
                    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
                }
            }
        }
        catch (Exception)
        {
            // Ignore errors during token retrieval to avoid breaking API calls
            // This can happen during prerendering when authentication isn't fully initialized
        }

        return await base.SendAsync(request, cancellationToken);
    }
}
