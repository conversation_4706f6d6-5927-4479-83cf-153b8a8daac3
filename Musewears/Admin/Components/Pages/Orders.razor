@page "/orders"
@layout AdminLayout
@attribute [Authorize(Policy = "AdminOnly")]
@using Microsoft.AspNetCore.Authorization
@using Admin.Models
@using Admin.Services
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@inject RefitApiService ApiService

<PageTitle>Order List - Musewears Admin</PageTitle>

@if (!string.IsNullOrEmpty(errorMessage))
{
    <div class="alert alert-danger" role="alert">
        <strong>Error:</strong> @errorMessage
    </div>
}

<!-- Breadcrumb -->
<div class="flex items-center flex-wrap justify-between gap20 mb-27">
        <h3>Order List</h3>
        <ul class="breadcrumbs flex items-center flex-wrap justify-start gap10">
            <li>
                <a href="/"><div class="text-tiny">Dashboard</div></a>
            </li>
            <li>
                <i class="icon-chevron-right"></i>
            </li>
            <li>
                <a href="#"><div class="text-tiny">Order</div></a>
            </li>
            <li>
                <i class="icon-chevron-right"></i>
            </li>
            <li>
                <div class="text-tiny">Order List</div>
            </li>
        </ul>
    </div>

<!-- order-list -->
<div class="wg-box">
        <div class="flex items-center justify-between gap10 flex-wrap">
            <div class="wg-filter flex-grow">
                <form class="form-search" @onsubmit="HandleSearch" @onsubmit:preventDefault="true">
                    <fieldset class="name">
                        <input type="text" placeholder="Search here..." @bind="searchTerm" @bind:event="oninput" name="name" tabindex="2" aria-required="true">
                    </fieldset>
                    <div class="button-submit">
                        <button type="submit"><i class="icon-search"></i></button>
                    </div>
                </form>
            </div>
            <a class="tf-button style-1 w208" href="#" @onclick="ExportOrders" @onclick:preventDefault="true"><i class="icon-file-text"></i>Export all order</a>
        </div>
        <div class="wg-table table-all-category">
            <ul class="table-title flex gap20 mb-14">
                <li>
                    <div class="body-title">Customer</div>
                </li>
                <li>
                    <div class="body-title">Order ID</div>
                </li>
                <li>
                    <div class="body-title">Price</div>
                </li>
                <li>
                    <div class="body-title">Quantity</div>
                </li>
                <li>
                    <div class="body-title">Payment</div>
                </li>
                <li>
                    <div class="body-title">Status</div>
                </li>
                <li>
                    <div class="body-title">Date</div>
                </li>
                <li>
                    <div class="body-title">Action</div>
                </li>
            </ul>
            <ul class="flex flex-column">
                @if (isLoading)
                {
                    <li class="text-center p-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </li>
                }
                else if (orders.Any())
                {
                    @foreach (var order in orders)
                    {
                        <li class="product-item gap14">
                            <div class="image no-bg">
                                <img src="assets/images/avatar/user-1.png" alt="Customer">
                            </div>
                            <div class="flex items-center justify-between gap20 flex-grow">
                                <div class="name">
                                    <a href="/orders/@order.OrderNumber" class="body-title-2">Customer #@order.OrderNumber</a>
                                </div>
                                <div class="body-text">#@order.OrderNumber</div>
                                <div class="body-text">₦@order.Total.ToString("N2")</div>
                                <div class="body-text">1</div>
                                <div class="body-text">Card</div>
                                <div>
                                    @switch (order.Status.ToLower())
                                    {
                                        case "completed":
                                        case "delivered":
                                            <div class="block-available">@order.Status</div>
                                            break;
                                        case "pending":
                                        case "processing":
                                            <div class="block-pending">@order.Status</div>
                                            break;
                                        case "cancelled":
                                        case "failed":
                                            <div class="block-not-available">@order.Status</div>
                                            break;
                                        default:
                                            <div class="block-tracking">@order.Status</div>
                                            break;
                                    }
                                </div>
                                <div class="body-text">@order.Date.ToString("dd MMM yyyy")</div>
                                <div class="list-icon-function">
                                    <div class="item eye">
                                        <a href="/orders/@order.OrderNumber"><i class="icon-eye"></i></a>
                                    </div>
                                    <div class="item edit">
                                        <a href="/orders/@order.OrderNumber/edit"><i class="icon-edit-3"></i></a>
                                    </div>
                                    <div class="item trash">
                                        <button @onclick="() => DeleteOrder(order.OrderNumber)" class="btn-link"><i class="icon-trash-2"></i></button>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li class="divider"></li>
                    }
                }
                else
                {
                    <li class="text-center p-4">
                        <div class="body-text">No orders found.</div>
                    </li>
                }
            </ul>
        </div>

        <!-- Pagination -->
        @if (totalPages > 1)
        {
            <div class="divider"></div>
            <div class="flex items-center justify-between flex-wrap gap10">
                <div class="text-tiny">Showing @((currentPage - 1) * pageSize + 1) to @Math.Min(currentPage * pageSize, totalItems) of @totalItems entries</div>
                <ul class="wg-pagination">
                    <li class="@(currentPage == 1 ? "disabled" : "")">
                        <a href="#" @onclick="() => ChangePage(currentPage - 1)" @onclick:preventDefault="true">Previous</a>
                    </li>
                    @for (int i = 1; i <= totalPages; i++)
                    {
                        <li class="@(i == currentPage ? "active" : "")">
                            <a href="#" @onclick="() => ChangePage(i)" @onclick:preventDefault="true">@i</a>
                        </li>
                    }
                    <li class="@(currentPage == totalPages ? "disabled" : "")">
                        <a href="#" @onclick="() => ChangePage(currentPage + 1)" @onclick:preventDefault="true">Next</a>
                    </li>
                </ul>
            </div>
        }
</div>
<!-- /wg-table -->

@code {
    private List<OrderSummary> orders = new();
    private bool isLoading = true;
    private string? errorMessage;
    private string searchTerm = "";
    private int pageSize = 10;
    private int currentPage = 1;
    private int totalItems = 0;
    private int totalPages = 0;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await LoadOrders();
        }
    }

    private async Task LoadOrders()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            var result = await ApiService.GetOrdersAsync(
                pageIndex: currentPage - 1,
                pageSize: pageSize);

            orders = result.Data.ToList();
            totalItems = (int)result.Count;
            totalPages = (int)Math.Ceiling((double)totalItems / pageSize);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading orders: {ex.Message}");
            errorMessage = $"Failed to load orders: {ex.Message}";
            orders = new List<OrderSummary>();
            totalItems = 0;
            totalPages = 0;
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task HandleSearch()
    {
        currentPage = 1;
        await LoadOrders();
    }

    private async Task ChangePage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            await LoadOrders();
        }
    }

    private async Task DeleteOrder(int orderId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this order?"))
        {
            try
            {
                // TODO: Implement delete API call when available
                await Task.Delay(100);
                await LoadOrders();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting order: {ex.Message}");
                await JSRuntime.InvokeVoidAsync("alert", "Failed to delete order. Please try again.");
            }
        }
    }

    private async Task ExportOrders()
    {
        // TODO: Implement export functionality
        await JSRuntime.InvokeVoidAsync("alert", "Export functionality will be implemented soon!");
    }
}
