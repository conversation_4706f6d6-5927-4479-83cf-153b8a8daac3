@page "/"
@layout AdminLayout
@attribute [Authorize(Policy = "AdminOnly")]
@using Microsoft.AspNetCore.Authorization
@using Admin.Models
@using Admin.Services
@inject RefitApiService ApiService

<PageTitle>Dashboard - Musewears Admin</PageTitle>

@if (!string.IsNullOrEmpty(errorMessage))
{
    <div class="alert alert-danger" role="alert">
        <strong>Error:</strong> @errorMessage
    </div>
}

<!-- Breadcrumb -->
<div class="flex items-center flex-wrap justify-between gap20 mb-27">
    <h3>Dashboard</h3>
    <ul class="breadcrumbs flex items-center flex-wrap justify-start gap10">
        <li>
            <a href="/"><div class="text-tiny">Dashboard</div></a>
        </li>
        <li>
            <i class="icon-chevron-right"></i>
        </li>
        <li>
            <div class="text-tiny">Home</div>
        </li>
    </ul>
</div>

<!-- Dashboard Stats Cards -->
<div class="tf-section-4 mb-30">
    <!-- chart-default -->
    <div class="wg-chart-default">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap14">
                <div class="image">
                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="52" viewBox="0 0 48 52" fill="none">
                        <path opacity="0.08" d="M19.1086 2.12943C22.2027 0.343099 26.0146 0.343099 29.1086 2.12943L42.4913 9.85592C45.5853 11.6423 47.4913 14.9435 47.4913 18.5162V33.9692C47.4913 37.5418 45.5853 40.8431 42.4913 42.6294L29.1086 50.3559C26.0146 52.1423 22.2027 52.1423 19.1086 50.3559L5.72596 42.6294C2.63194 40.8431 0.725956 37.5418 0.725956 33.9692V18.5162C0.725956 14.9435 2.63195 11.6423 5.72596 9.85592L19.1086 2.12943Z" fill="url(#paint0_linear_53_110)"/>
                        <defs>
                          <linearGradient id="paint0_linear_53_110" x1="-43.532" y1="-34.3465" x2="37.6769" y2="43.9447" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#92BCFF"/>
                            <stop offset="1" stop-color="#2377FC"/>
                          </linearGradient>
                        </defs>
                    </svg>
                    <i class="icon-shopping-bag"></i>
                </div>
                <div>
                    <div class="body-text mb-2">Total Sales</div>
                    <h4>@(isLoading ? "..." : totalOrders.ToString())</h4>
                </div>
            </div>
            <div class="box-icon-trending up">
                <i class="icon-trending-up"></i>
                <div class="body-title number">1.56%</div>
            </div>
        </div>
        <div class="wrap-chart">
            <div id="line-chart-1"></div>
        </div>
    </div>
    <!-- /chart-default -->
    <!-- chart-default -->
    <div class="wg-chart-default">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap14">
                <div class="image">
                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="52" viewBox="0 0 48 52" fill="none">
                        <path opacity="0.08" d="M19.1086 2.12943C22.2027 0.343099 26.0146 0.343099 29.1086 2.12943L42.4913 9.85592C45.5853 11.6423 47.4913 14.9435 47.4913 18.5162V33.9692C47.4913 37.5418 45.5853 40.8431 42.4913 42.6294L29.1086 50.3559C26.0146 52.1423 22.2027 52.1423 19.1086 50.3559L5.72596 42.6294C2.63194 40.8431 0.725956 37.5418 0.725956 33.9692V18.5162C0.725956 14.9435 2.63195 11.6423 5.72596 9.85592L19.1086 2.12943Z" fill="url(#paint0_linear_53_110)"/>
                        <defs>
                          <linearGradient id="paint0_linear_53_110" x1="-43.532" y1="-34.3465" x2="37.6769" y2="43.9447" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#92BCFF"/>
                            <stop offset="1" stop-color="#2377FC"/>
                          </linearGradient>
                        </defs>
                    </svg>
                    <i class="icon-dollar-sign"></i>
                </div>
                <div>
                    <div class="body-text mb-2">Total Income</div>
                    <h4>@(isLoading ? "..." : "₦" + totalRevenue.ToString("N0"))</h4>
                </div>
            </div>
            <div class="box-icon-trending down">
                <i class="icon-trending-down"></i>
                <div class="body-title number">1.56%</div>
            </div>
        </div>
        <div class="wrap-chart">
            <div id="line-chart-2"></div>
        </div>
    </div>
    <!-- /chart-default -->
    <!-- chart-default -->
    <div class="wg-chart-default">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap14">
                <div class="image">
                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="52" viewBox="0 0 48 52" fill="none">
                        <path opacity="0.08" d="M19.1086 2.12943C22.2027 0.343099 26.0146 0.343099 29.1086 2.12943L42.4913 9.85592C45.5853 11.6423 47.4913 14.9435 47.4913 18.5162V33.9692C47.4913 37.5418 45.5853 40.8431 42.4913 42.6294L29.1086 50.3559C26.0146 52.1423 22.2027 52.1423 19.1086 50.3559L5.72596 42.6294C2.63194 40.8431 0.725956 37.5418 0.725956 33.9692V18.5162C0.725956 14.9435 2.63195 11.6423 5.72596 9.85592L19.1086 2.12943Z" fill="url(#paint0_linear_53_110)"/>
                        <defs>
                          <linearGradient id="paint0_linear_53_110" x1="-43.532" y1="-34.3465" x2="37.6769" y2="43.9447" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#92BCFF"/>
                            <stop offset="1" stop-color="#2377FC"/>
                          </linearGradient>
                        </defs>
                    </svg>
                    <i class="icon-file"></i>
                </div>
                <div>
                    <div class="body-text mb-2">Total Products</div>
                    <h4>@(isLoading ? "..." : totalProducts.ToString())</h4>
                </div>
            </div>
            <div class="box-icon-trending">
                <i class="icon-trending-up"></i>
                <div class="body-title number">0.00%</div>
            </div>
        </div>
        <div class="wrap-chart">
            <div id="line-chart-3"></div>
        </div>
    </div>
    <!-- /chart-default -->
    <!-- chart-default -->
    <div class="wg-chart-default">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap14">
                <div class="image">
                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="52" viewBox="0 0 48 52" fill="none">
                        <path opacity="0.08" d="M19.1086 2.12943C22.2027 0.343099 26.0146 0.343099 29.1086 2.12943L42.4913 9.85592C45.5853 11.6423 47.4913 14.9435 47.4913 18.5162V33.9692C47.4913 37.5418 45.5853 40.8431 42.4913 42.6294L29.1086 50.3559C26.0146 52.1423 22.2027 52.1423 19.1086 50.3559L5.72596 42.6294C2.63194 40.8431 0.725956 37.5418 0.725956 33.9692V18.5162C0.725956 14.9435 2.63195 11.6423 5.72596 9.85592L19.1086 2.12943Z" fill="url(#paint0_linear_53_110)"/>
                        <defs>
                          <linearGradient id="paint0_linear_53_110" x1="-43.532" y1="-34.3465" x2="37.6769" y2="43.9447" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#92BCFF"/>
                            <stop offset="1" stop-color="#2377FC"/>
                          </linearGradient>
                        </defs>
                    </svg>
                    <i class="icon-users"></i>
                </div>
                <div>
                    <div class="body-text mb-2">Total Users</div>
                    <h4>@(isLoading ? "..." : totalCustomers.ToString())</h4>
                </div>
            </div>
            <div class="box-icon-trending up">
                <i class="icon-trending-up"></i>
                <div class="body-title number">1.56%</div>
            </div>
        </div>
        <div class="wrap-chart">
            <div id="line-chart-4"></div>
        </div>
    </div>
    <!-- /chart-default -->
</div>

<!-- Top selling product and Orders section -->
<div class="tf-section-2 mb-30">
    <!-- product-overview -->
    <div class="wg-box">
        <div class="flex items-center justify-between">
            <h5>Top selling product</h5>
            <div class="dropdown default">
                <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span class="view-all">View all<i class="icon-chevron-down"></i></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                        <a href="javascript:void(0);">3 days</a>
                    </li>
                    <li>
                        <a href="javascript:void(0);">7 days</a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="wg-table table-top-selling-product">
            <ul class="table-title flex gap20 mb-14">
                <li>
                    <div class="body-title">Product</div>
                </li>
                <li>
                    <div class="body-title">Category</div>
                </li>
                <li>
                    <div class="body-title">Total sale</div>
                </li>
                <li>
                    <div class="body-title">Stock</div>
                </li>
            </ul>
            <div class="divider mb-14"></div>
            @if (isLoading)
            {
                <div class="text-center p-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            }
            else if (topProducts?.Any() == true)
            {
                <ul class="flex flex-column gap10">
                    @foreach (var product in topProducts)
                    {
                        <li class="product-item gap14">
                            <div class="image">
                                <img src="@(product.PictureUri ?? "assets/images/products/16.png")" alt="@product.Name">
                            </div>
                            <div class="flex items-center justify-between flex-grow">
                                <div class="name">
                                    <a href="/products/@product.Id" class="body-title-2">@product.Name</a>
                                </div>
                                <div class="body-text">Fashion</div>
                                <div class="body-text">₦@product.Price.ToString("N2")</div>
                                <div>
                                    @if (product.AvailableStock > 0)
                                    {
                                        <div class="block-available">In stock</div>
                                    }
                                    else
                                    {
                                        <div class="block-not-available">Out of stock</div>
                                    }
                                </div>
                            </div>
                        </li>
                        <li class="divider"></li>
                    }
                </ul>
            }
            else
            {
                <div class="text-center p-4">
                    <div class="body-text">No products found.</div>
                </div>
            }
        </div>
    </div>
    <!-- /product-overview -->
    <!-- orders -->
    <div class="wg-box">
        <div class="flex items-center justify-between">
            <h5>Orders</h5>
            <div class="dropdown default">
                <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                   <span class="icon-more"><i class="icon-more-horizontal"></i></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                        <a href="javascript:void(0);">This Week</a>
                    </li>
                    <li>
                        <a href="javascript:void(0);">Last Week</a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="wg-table table-orders-1">
            <ul class="table-title flex gap10 mb-14">
                <li>
                    <div class="body-title">Product</div>
                </li>
                <li>
                    <div class="body-title">Customer</div>
                </li>
                <li>
                    <div class="body-title">Price</div>
                </li>
                <li>
                    <div class="body-title">Delivery date</div>
                </li>
            </ul>
            <div class="divider mb-14"></div>
            @if (isLoading)
            {
                <div class="text-center p-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            }
            else if (recentOrders?.Any() == true)
            {
                <ul class="flex flex-column gap18">
                    @foreach (var order in recentOrders)
                    {
                        <li class="product-item gap14">
                            <div class="image no-bg">
                                <img src="assets/images/products/21.png" alt="">
                            </div>
                            <div class="flex items-center justify-between flex-grow gap10">
                                <div class="name">
                                    <a href="/orders/@order.OrderNumber" class="body-text">Order #@order.OrderNumber</a>
                                </div>
                                <div class="body-text">@order.Status</div>
                                <div class="body-text">₦@order.Total.ToString("N2")</div>
                                <div class="body-text">@order.Date.ToString("dd MMM yyyy")</div>
                            </div>
                        </li>
                        <li class="divider"></li>
                    }
                </ul>
            }
            else
            {
                <div class="text-center p-4">
                    <div class="body-text">No recent orders found.</div>
                </div>
            }
        </div>
    </div>
    <!-- /orders -->
</div>

<!-- Customer Growth and Product Overview section -->
<div class="tf-section-4 mb-30">
    <!-- Customer Growth -->
    <div class="wg-box">
        <div class="flex items-center justify-between">
            <h5>Customer Growth</h5>
            <div class="dropdown default">
                <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span class="view-all">View all<i class="icon-chevron-down"></i></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a href="javascript:void(0);">3 days</a></li>
                    <li><a href="javascript:void(0);">7 days</a></li>
                    <li><a href="javascript:void(0);">30 days</a></li>
                </ul>
            </div>
        </div>
        <div class="wrap-chart">
            <div id="line-chart-5"></div>
        </div>
        <div class="flex items-center justify-between">
            <div class="flex items-center gap14">
                <div class="image">
                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="52" viewBox="0 0 48 52" fill="none">
                        <path opacity="0.08" d="M19.1086 2.12943C22.2027 0.343099 26.0146 0.343099 29.1086 2.12943L42.4913 9.85592C45.5853 11.6423 47.4913 14.9435 47.4913 18.5162V33.9692C47.4913 37.5418 45.5853 40.8431 42.4913 42.6294L29.1086 50.3559C26.0146 52.1423 22.2027 52.1423 19.1086 50.3559L5.72596 42.6294C2.63194 40.8431 0.725956 37.5418 0.725956 33.9692V18.5162C0.725956 14.9435 2.63195 11.6423 5.72596 9.85592L19.1086 2.12943Z" fill="url(#paint0_linear_53_110)"/>
                        <defs>
                          <linearGradient id="paint0_linear_53_110" x1="-43.532" y1="-34.3465" x2="37.6769" y2="43.9447" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#92BCFF"/>
                            <stop offset="1" stop-color="#2377FC"/>
                          </linearGradient>
                        </defs>
                    </svg>
                    <i class="icon-users"></i>
                </div>
                <div>
                    <div class="body-text mb-2">Total Customer</div>
                    <h4>@(isLoading ? "..." : totalCustomers.ToString())</h4>
                </div>
            </div>
            <div class="box-icon-trending up">
                <i class="icon-trending-up"></i>
                <div class="body-title number">1.56%</div>
            </div>
        </div>
    </div>

    <!-- Product Overview -->
    <div class="wg-box">
        <div class="flex items-center justify-between">
            <h5>Product Overview</h5>
            <div class="dropdown default">
                <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span class="view-all">View all<i class="icon-chevron-down"></i></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a href="javascript:void(0);">3 days</a></li>
                    <li><a href="javascript:void(0);">7 days</a></li>
                    <li><a href="javascript:void(0);">30 days</a></li>
                </ul>
            </div>
        </div>
        <div class="wrap-chart">
            <div id="line-chart-6"></div>
        </div>
        <div class="flex items-center justify-between">
            <div class="flex items-center gap14">
                <div class="image">
                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="52" viewBox="0 0 48 52" fill="none">
                        <path opacity="0.08" d="M19.1086 2.12943C22.2027 0.343099 26.0146 0.343099 29.1086 2.12943L42.4913 9.85592C45.5853 11.6423 47.4913 14.9435 47.4913 18.5162V33.9692C47.4913 37.5418 45.5853 40.8431 42.4913 42.6294L29.1086 50.3559C26.0146 52.1423 22.2027 52.1423 19.1086 50.3559L5.72596 42.6294C2.63194 40.8431 0.725956 37.5418 0.725956 33.9692V18.5162C0.725956 14.9435 2.63195 11.6423 5.72596 9.85592L19.1086 2.12943Z" fill="url(#paint0_linear_53_110)"/>
                        <defs>
                          <linearGradient id="paint0_linear_53_110" x1="-43.532" y1="-34.3465" x2="37.6769" y2="43.9447" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#92BCFF"/>
                            <stop offset="1" stop-color="#2377FC"/>
                          </linearGradient>
                        </defs>
                    </svg>
                    <i class="icon-file"></i>
                </div>
                <div>
                    <div class="body-text mb-2">Total Products</div>
                    <h4>@(isLoading ? "..." : totalProducts.ToString())</h4>
                </div>
            </div>
            <div class="box-icon-trending up">
                <i class="icon-trending-up"></i>
                <div class="body-title number">2.34%</div>
            </div>
        </div>
    </div>
</div>

<!-- Group of potential customers and Top Countries section -->
<div class="tf-section-7 mb-30">
    <!-- customers -->
    <div class="wg-box">
        <div class="flex items-center justify-between">
            <h5>Group of potential customers</h5>
            <div class="dropdown default">
                <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                   <span class="icon-more"><i class="icon-more-horizontal"></i></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                        <a href="javascript:void(0);">This Week</a>
                    </li>
                    <li>
                        <a href="javascript:void(0);">Last Week</a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="wg-table table-customers">
            <ul class="table-title flex gap10 mb-14">
                <li>
                    <div class="body-title">Age</div>
                </li>
                <li>
                    <div class="body-title">Category</div>
                </li>
                <li>
                    <div class="body-title">Purchases</div>
                </li>
                <li>
                    <div class="body-title">Country</div>
                </li>
                <li>
                    <div class="body-title">Amount of money</div>
                </li>
            </ul>
            <div class="divider mb-22"></div>
            <ul class="flex flex-column gap34">
                <li class="item flex gap10 items-center ">
                    <div class="body-text">18-22</div>
                    <div class="body-text">Industrial</div>
                    <div class="body-text">130</div>
                    <div class="body-text">India</div>
                    <div class="body-text">$120 - $240</div>
                </li>
                <li class="item flex gap10 items-center ">
                    <div class="body-text">23-27</div>
                    <div class="body-text">Video Games</div>
                    <div class="body-text">583</div>
                    <div class="body-text">Russia</div>
                    <div class="body-text">$120 - $240</div>
                </li>
                <li class="item flex gap10 items-center ">
                    <div class="body-text">28-34</div>
                    <div class="body-text">Books</div>
                    <div class="body-text">426</div>
                    <div class="body-text">China</div>
                    <div class="body-text">$712 - $1,778</div>
                </li>
                <li class="item flex gap10 items-center ">
                    <div class="body-text">35-44</div>
                    <div class="body-text">Men's Fashion</div>
                    <div class="body-text">561</div>
                    <div class="body-text">UK</div>
                    <div class="body-text">$573 - $940</div>
                </li>
                <li class="item flex gap10 items-center ">
                    <div class="body-text">45-59</div>
                    <div class="body-text">Home, Kitchen, Pets</div>
                    <div class="body-text">177</div>
                    <div class="body-text">USA</div>
                    <div class="body-text">$120 - $240</div>
                </li>
            </ul>
        </div>
    </div>
    <!-- /customers -->
    <!-- top-countries -->
    <div class="wg-box">
        <div class="flex items-center justify-between">
            <h5>Top Countries By Sales</h5>
            <div class="dropdown default">
                <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span class="view-all">View all<i class="icon-chevron-down"></i></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                        <a href="javascript:void(0);">3 days</a>
                    </li>
                    <li>
                        <a href="javascript:void(0);">7 days</a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="flex items-center gap10">
            <h4>$37,802</h4>
            <div class="box-icon-trending up">
                <i class="icon-trending-up"></i>
                <div class="body-title number">1.56%</div>
            </div>
            <div class="text-tiny">since last weekend</div>
        </div>
        <ul class="flex flex-column justify-between gap10 h-full">
            <li class="country-item">
                <div class="image">
                    <img src="assets/images/country/6.png" alt="">
                </div>
                <div class="flex-grow flex items-center justify-between">
                    <a href="#" class="body-text name">Turkish Flag</a>
                    <div class="box-icon-trending up">
                        <i class="icon-trending-up"></i>
                    </div>
                    <div class="body-text number">6,972</div>
                </div>
            </li>
            <li class="country-item">
                <div class="image">
                    <img src="assets/images/country/7.png" alt="">
                </div>
                <div class="flex-grow flex items-center justify-between">
                    <a href="#" class="body-text name">Belgium</a>
                    <div class="box-icon-trending up">
                        <i class="icon-trending-up"></i>
                    </div>
                    <div class="body-text number">6,972</div>
                </div>
            </li>
            <li class="country-item">
                <div class="image">
                    <img src="assets/images/country/8.png" alt="">
                </div>
                <div class="flex-grow flex items-center justify-between">
                    <a href="#" class="body-text name">Sweden</a>
                    <div class="box-icon-trending down">
                        <i class="icon-trending-down"></i>
                    </div>
                    <div class="body-text number">6,972</div>
                </div>
            </li>
            <li class="country-item">
                <div class="image">
                    <img src="assets/images/country/9.png" alt="">
                </div>
                <div class="flex-grow flex items-center justify-between">
                    <a href="#" class="body-text name">Vietnamese</a>
                    <div class="box-icon-trending up">
                        <i class="icon-trending-up"></i>
                    </div>
                    <div class="body-text number">6,972</div>
                </div>
            </li>
            <li class="country-item">
                <div class="image">
                    <img src="assets/images/country/10.png" alt="">
                </div>
                <div class="flex-grow flex items-center justify-between">
                    <a href="#" class="body-text name">Australia</a>
                    <div class="box-icon-trending down">
                        <i class="icon-trending-down"></i>
                    </div>
                    <div class="body-text number">6,972</div>
                </div>
            </li>
            <li class="country-item">
                <div class="image">
                    <img src="assets/images/country/11.png" alt="">
                </div>
                <div class="flex-grow flex items-center justify-between">
                    <a href="#" class="body-text name">Saudi Arabia</a>
                    <div class="box-icon-trending down">
                        <i class="icon-trending-down"></i>
                    </div>
                    <div class="body-text number">6,972</div>
                </div>
            </li>
        </ul>
    </div>
    <!-- /top-countries -->
</div>

@code {
    private bool isLoading = true;
    private string? errorMessage;
    private int totalOrders = 0;
    private decimal totalRevenue = 0;
    private int totalProducts = 0;
    private int totalCustomers = 0;
    private List<CatalogItem>? topProducts;
    private List<OrderSummary>? recentOrders;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await LoadDashboardData();
        }
    }

    private async Task LoadDashboardData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            // Load analytics data in parallel
            var analyticsTask = LoadAnalyticsAsync();
            var productsTask = LoadTopProductsAsync();
            var ordersTask = LoadRecentOrdersAsync();

            await Task.WhenAll(analyticsTask, productsTask, ordersTask);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading dashboard data: {ex.Message}");
            errorMessage = $"Failed to load dashboard data: {ex.Message}";
            totalOrders = 0;
            totalRevenue = 0;
            totalProducts = 0;
            totalCustomers = 0;
            topProducts = new List<CatalogItem>();
            recentOrders = new List<OrderSummary>();
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadAnalyticsAsync()
    {
        try
        {
            // Get catalog analytics
            var catalogAnalytics = await ApiService.GetCatalogAnalyticsAsync();
            totalProducts = catalogAnalytics.TotalItems;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading catalog analytics: {ex.Message}");
            totalProducts = 0;
        }

        try
        {
            // Get orders data (using first page to get count)
            var ordersData = await ApiService.GetOrdersAsync(pageIndex: 0, pageSize: 1);
            totalOrders = (int)ordersData.Count;

            // Calculate total revenue from recent orders
            var revenueOrders = await ApiService.GetOrdersAsync(pageIndex: 0, pageSize: 100);
            totalRevenue = revenueOrders.Data.Sum(o => o.Total);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading orders analytics: {ex.Message}");
            totalOrders = 0;
            totalRevenue = 0;
        }

        try
        {
            // Get users count
            var usersData = await ApiService.GetUsersAsync(pageIndex: 0, pageSize: 1);
            totalCustomers = (int)usersData.Count;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading users analytics: {ex.Message}");
            totalCustomers = 0;
        }
    }

    private async Task LoadTopProductsAsync()
    {
        try
        {
            var productsData = await ApiService.GetCatalogItemsAsync(pageIndex: 0, pageSize: 5);
            topProducts = productsData.Data.ToList();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading top products: {ex.Message}");
            topProducts = new List<CatalogItem>();
        }
    }

    private async Task LoadRecentOrdersAsync()
    {
        try
        {
            var ordersData = await ApiService.GetOrdersAsync(pageIndex: 0, pageSize: 3);
            recentOrders = ordersData.Data.ToList();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading recent orders: {ex.Message}");
            recentOrders = new List<OrderSummary>();
        }
    }
}