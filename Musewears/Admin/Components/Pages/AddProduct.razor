@page "/products/add"
@layout AdminLayout
@* @attribute [Authorize(Policy = "AdminOnly")] *@
@inject NavigationManager Navigation

<PageTitle>Add Product - Musewears Admin</PageTitle>

<!-- Breadcrumb -->
<div class="flex items-center flex-wrap justify-between gap20 mb-27">
    <h3>Add Product</h3>
    <ul class="breadcrumbs flex items-center flex-wrap justify-start gap10">
        <li>
            <a href="/"><div class="text-tiny">Dashboard</div></a>
        </li>
        <li>
            <i class="icon-chevron-right"></i>
        </li>
        <li>
            <a href="#"><div class="text-tiny">Ecommerce</div></a>
        </li>
        <li>
            <i class="icon-chevron-right"></i>
        </li>
        <li>
            <div class="text-tiny">Add product</div>
        </li>
    </ul>
</div>

<!-- form-add-product -->
<EditForm class="tf-section-2 form-add-product" Model="@productModel" OnValidSubmit="@HandleValidSubmit">
    <DataAnnotationsValidator />

    <div class="wg-box">
        <fieldset class="name">
            <div class="body-title mb-10">Product name <span class="tf-color-1">*</span></div>
            <InputText @bind-Value="productModel.Name" class="mb-10" placeholder="Enter product name" />
            <ValidationMessage For="@(() => productModel.Name)" />
            <div class="text-tiny">Do not exceed 20 characters when entering the product name.</div>
        </fieldset>

        <div class="gap22 cols">
            <fieldset class="category">
                <div class="body-title mb-10">Category <span class="tf-color-1">*</span></div>
                <div class="select">
                    <InputSelect @bind-Value="productModel.CategoryId" class="">
                        <option value="">Choose category</option>
                        @foreach (var category in categories)
                        {
                            <option value="@category.Id">@category.Name</option>
                        }
                    </InputSelect>
                </div>
                <ValidationMessage For="@(() => productModel.CategoryId)" />
            </fieldset>

            <fieldset class="male">
                <div class="body-title mb-10">Gender <span class="tf-color-1">*</span></div>
                <div class="select">
                    <InputSelect @bind-Value="productModel.Gender" class="">
                        <option value="">Male</option>
                        <option value="Female">Female</option>
                        <option value="Other">Other</option>
                    </InputSelect>
                </div>
                <ValidationMessage For="@(() => productModel.Gender)" />
            </fieldset>
        </div>

        <fieldset class="brand">
            <div class="body-title mb-10">Brand <span class="tf-color-1">*</span></div>
            <div class="select">
                <InputSelect @bind-Value="productModel.BrandId" class="">
                    <option value="">Choose category</option>
                    @foreach (var brand in brands)
                    {
                        <option value="@brand.Id">@brand.Name</option>
                    }
                </InputSelect>
            </div>
            <ValidationMessage For="@(() => productModel.BrandId)" />
        </fieldset>

        <fieldset class="description">
            <div class="body-title mb-10">Description <span class="tf-color-1">*</span></div>
            <InputTextArea @bind-Value="productModel.Description" class="mb-10" placeholder="Description" />
            <ValidationMessage For="@(() => productModel.Description)" />
            <div class="text-tiny">Do not exceed 100 characters when entering the product name.</div>
        </fieldset>
    </div>

    <div class="wg-box">
        <fieldset>
            <div class="body-title mb-10">Upload images</div>
            <div class="upload-image mb-16">
                <div class="item">
                    <img src="assets/images/upload/upload-1.png" alt="">
                </div>
                <div class="item">
                    <img src="assets/images/upload/upload-2.png" alt="">
                </div>
                <div class="item up-load">
                    <label class="uploadfile" for="myFile">
                        <span class="icon">
                            <i class="icon-upload-cloud"></i>
                        </span>
                        <span class="text-tiny">Drop your images here or select <span class="tf-color">click to browse</span></span>
                        <input type="file" id="myFile" name="filename" accept="image/*" multiple>
                    </label>
                </div>
            </div>
            <div class="body-text">You need to add at least 4 images. Pay attention to the quality of the pictures you add, comply with the background color standards. Pictures must be in certain dimensions. Notice that the product shows all the details</div>
        </fieldset>

        <div class="cols gap22">
            <fieldset class="name">
                <div class="body-title mb-10">Add size</div>
                <div class="select mb-10">
                    <select class="">
                        <option>EU - 44</option>
                        <option>EU - 40</option>
                        <option>EU - 50</option>
                    </select>
                </div>
                <div class="list-box-value mb-10">
                    <div class="box-value-item"><div class="body-text">EU - 38.5</div></div>
                    <div class="box-value-item"><div class="body-text">EU - 39</div></div>
                    <div class="box-value-item"><div class="body-text">EU - 40</div></div>
                </div>
                <div class="list-box-value">
                    <div class="box-value-item"><div class="body-text">EU - 41.5</div></div>
                    <div class="box-value-item"><div class="body-text">EU - 42</div></div>
                    <div class="box-value-item"><div class="body-text">EU - 43</div></div>
                </div>
            </fieldset>
            <fieldset class="name">
                <div class="body-title mb-10">Product date</div>
                <div class="select">
                    <input type="date" name="date" value="@DateTime.Now.ToString("yyyy-MM-dd")">
                </div>
            </fieldset>
        </div>

        <div class="cols gap10">
            <button class="tf-button w-full" type="submit" disabled="@isSubmitting">
                @if (isSubmitting)
                {
                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                }
                Add product
            </button>
            <button class="tf-button style-1 w-full" type="button" @onclick="SaveDraft">Save product</button>
            <a href="#" class="tf-button style-2 w-full">Schedule</a>
        </div>
    </div>
</EditForm>
<!-- /form-add-product -->

@code {
    private AddProductModel productModel = new();
    private bool isSubmitting = false;
    private List<CategoryDto> categories = new();
    private List<BrandDto> brands = new();

    public class AddProductModel
    {
        [Required(ErrorMessage = "Product name is required")]
        [StringLength(100, ErrorMessage = "Product name cannot exceed 100 characters")]
        public string Name { get; set; } = "";

        [Required(ErrorMessage = "Description is required")]
        [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
        public string Description { get; set; } = "";

        [Required(ErrorMessage = "Category is required")]
        public int CategoryId { get; set; }

        [Required(ErrorMessage = "Brand is required")]
        public int BrandId { get; set; }

        [Required(ErrorMessage = "Gender is required")]
        public string Gender { get; set; } = "";

        [Required(ErrorMessage = "Price is required")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Price must be greater than 0")]
        public decimal Price { get; set; }

        public decimal? SalePrice { get; set; }

        [Required(ErrorMessage = "Stock quantity is required")]
        [Range(0, int.MaxValue, ErrorMessage = "Stock must be 0 or greater")]
        public int AvailableStock { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "Restock threshold must be 0 or greater")]
        public int RestockThreshold { get; set; } = 10;
    }

    public class CategoryDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
    }

    public class BrandDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadCategories();
        await LoadBrands();
    }

    private async Task LoadCategories()
    {
        // TODO: Replace with actual API call to Catalog.API
        await Task.Delay(100);
        
        categories = new List<CategoryDto>
        {
            new() { Id = 1, Name = "Clothing" },
            new() { Id = 2, Name = "Shoes" },
            new() { Id = 3, Name = "Accessories" },
            new() { Id = 4, Name = "Electronics" }
        };
    }

    private async Task LoadBrands()
    {
        // TODO: Replace with actual API call to Catalog.API
        await Task.Delay(100);
        
        brands = new List<BrandDto>
        {
            new() { Id = 1, Name = "Nike" },
            new() { Id = 2, Name = "Adidas" },
            new() { Id = 3, Name = "Puma" },
            new() { Id = 4, Name = "Generic" }
        };
    }

    private async Task HandleValidSubmit()
    {
        isSubmitting = true;
        
        try
        {
            // TODO: Implement API call to create product
            await Task.Delay(1000); // Simulate API call
            
            // Navigate back to products list
            Navigation.NavigateTo("/products");
        }
        catch (Exception ex)
        {
            // Handle error
            Console.WriteLine($"Error creating product: {ex.Message}");
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private async Task SaveDraft()
    {
        // TODO: Implement save as draft functionality
        await Task.Delay(100);
    }
}
