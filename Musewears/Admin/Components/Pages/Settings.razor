@page "/settings"
@layout AdminLayout
@attribute [Authorize(Policy = "AdminOnly")]

<PageTitle>Settings - Musewears Admin</PageTitle>

<div class="tf-section-2 mb-30">
    <!-- Breadcrumb -->
    <div class="flex items-center flex-wrap justify-between gap20 mb-27">
        <h3>Settings</h3>
        <ul class="breadcrumbs flex items-center flex-wrap justify-start gap10">
            <li>
                <a href="/"><div class="text-tiny">Dashboard</div></a>
            </li>
            <li>
                <i class="icon-chevron-right"></i>
            </li>
            <li>
                <div class="text-tiny">Settings</div>
            </li>
        </ul>
    </div>
    
    <!-- Settings Content -->
    <div class="wg-box">
        <h5>Application Settings</h5>
        
        <div class="settings-section">
            <h6>General Settings</h6>
            <div class="form-group">
                <label>Site Name</label>
                <input type="text" class="form-control" value="Musewears Admin" />
            </div>
            <div class="form-group">
                <label>Admin Email</label>
                <input type="email" class="form-control" value="<EMAIL>" />
            </div>
        </div>
        
        <div class="settings-section">
            <h6>Notification Settings</h6>
            <div class="form-check">
                <input type="checkbox" class="form-check-input" id="emailNotifications" checked />
                <label class="form-check-label" for="emailNotifications">
                    Email Notifications
                </label>
            </div>
            <div class="form-check">
                <input type="checkbox" class="form-check-input" id="lowStockAlerts" checked />
                <label class="form-check-label" for="lowStockAlerts">
                    Low Stock Alerts
                </label>
            </div>
        </div>
        
        <div class="settings-section">
            <h6>Security Settings</h6>
            <div class="form-group">
                <label>Session Timeout (minutes)</label>
                <input type="number" class="form-control" value="30" min="5" max="480" />
            </div>
            <div class="form-check">
                <input type="checkbox" class="form-check-input" id="twoFactorAuth" />
                <label class="form-check-label" for="twoFactorAuth">
                    Enable Two-Factor Authentication
                </label>
            </div>
        </div>
        
        <div class="settings-actions">
            <button class="tf-button" type="button">Save Settings</button>
            <button class="tf-button style-2" type="button">Reset to Defaults</button>
        </div>
    </div>
</div>

<style>
    .settings-section {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }

    .settings-section:last-of-type {
        border-bottom: none;
    }

    .settings-section h6 {
        margin-bottom: 15px;
        color: #333;
        font-weight: 600;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #333;
    }

    .form-control {
        width: 100%;
        max-width: 400px;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }

    .form-check {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .form-check-input {
        margin: 0;
    }

    .form-check-label {
        margin: 0;
        cursor: pointer;
    }

    .settings-actions {
        margin-top: 30px;
        display: flex;
        gap: 15px;
    }
</style>
