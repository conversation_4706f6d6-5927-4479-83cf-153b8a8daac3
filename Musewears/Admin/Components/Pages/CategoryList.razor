@page "/categories"
@layout AdminLayout
@attribute [Authorize(Policy = "AdminOnly")]
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<PageTitle>Categories - Musewears Admin</PageTitle>

<div class="tf-section-2 mb-30">
    <!-- Breadcrumb -->
    <div class="flex items-center flex-wrap justify-between gap20 mb-27">
        <h3>Categories</h3>
        <ul class="breadcrumbs flex items-center flex-wrap justify-start gap10">
            <li>
                <a href="/"><div class="text-tiny">Dashboard</div></a>
            </li>
            <li>
                <i class="icon-chevron-right"></i>
            </li>
            <li>
                <a href="#"><div class="text-tiny">Products</div></a>
            </li>
            <li>
                <i class="icon-chevron-right"></i>
            </li>
            <li>
                <div class="text-tiny">Categories</div>
            </li>
        </ul>
    </div>
    
    <!-- category-list -->
    <div class="wg-box">
        <div class="flex items-center justify-between gap10 flex-wrap">
            <div class="wg-filter flex-grow">
                <form class="form-search" @onsubmit="HandleSearch" @onsubmit:preventDefault="true">
                    <fieldset class="name">
                        <input type="text" placeholder="Search categories..." @bind="searchTerm" @bind:event="oninput" name="name" tabindex="2" aria-required="true">
                    </fieldset>
                    <div class="button-submit">
                        <button type="submit"><i class="icon-search"></i></button>
                    </div>
                </form>
            </div>
            <button class="tf-button style-1 w208" @onclick="ShowAddModal"><i class="icon-plus"></i>Add Category</button>
        </div>
        <div class="wg-table table-all-category">
            <ul class="table-title flex gap20 mb-14">
                <li>
                    <div class="body-title">Category</div>
                </li>    
                <li>
                    <div class="body-title">Description</div>
                </li>
                <li>
                    <div class="body-title">Products</div>
                </li>
                <li>
                    <div class="body-title">Status</div>
                </li>
                <li>
                    <div class="body-title">Created</div>
                </li>
                <li>
                    <div class="body-title">Action</div>
                </li>
            </ul>
            <ul class="flex flex-column">
                @if (isLoading)
                {
                    <li class="text-center p-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </li>
                }
                else if (categories.Any())
                {
                    @foreach (var category in categories)
                    {
                        <li class="product-item gap14">
                            <div class="image no-bg">
                                <img src="@(category.ImageUrl ?? "assets/images/category/default.png")" alt="@category.Name">
                            </div>
                            <div class="flex items-center justify-between gap20 flex-grow">
                                <div class="name">
                                    <div class="body-title-2">@category.Name</div>
                                </div>
                                <div class="body-text">@category.Description</div>
                                <div class="body-text">@category.ProductCount</div>
                                <div>
                                    @if (category.IsActive)
                                    {
                                        <div class="block-available">Active</div>
                                    }
                                    else
                                    {
                                        <div class="block-not-available">Inactive</div>
                                    }
                                </div>
                                <div class="body-text">@category.CreatedDate.ToString("dd MMM yyyy")</div>
                                <div class="list-icon-function">
                                    <div class="item edit">
                                        <button @onclick="() => EditCategory(category)" class="btn-link"><i class="icon-edit-3"></i></button>
                                    </div>
                                    <div class="item trash">
                                        <button @onclick="() => DeleteCategory(category.Id)" class="btn-link"><i class="icon-trash-2"></i></button>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li class="divider"></li>
                    }
                }
                else
                {
                    <li class="text-center p-4">
                        <div class="body-text">No categories found.</div>
                    </li>
                }
            </ul>
        </div>
    </div>
</div>

<!-- Add/Edit Category Modal -->
@if (showModal)
{
    <div class="modal fade show" style="display: block;" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">@(editingCategory?.Id > 0 ? "Edit Category" : "Add Category")</h5>
                    <button type="button" class="btn-close" @onclick="CloseModal"></button>
                </div>
                <EditForm Model="categoryModel" OnValidSubmit="SaveCategory">
                    <DataAnnotationsValidator />
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Category Name <span class="text-danger">*</span></label>
                            <InputText @bind-Value="categoryModel.Name" class="form-control" placeholder="Enter category name" />
                            <ValidationMessage For="@(() => categoryModel.Name)" />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <InputTextArea @bind-Value="categoryModel.Description" class="form-control" rows="3" placeholder="Enter description" />
                            <ValidationMessage For="@(() => categoryModel.Description)" />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Status</label>
                            <div class="form-check">
                                <InputCheckbox @bind-Value="categoryModel.IsActive" class="form-check-input" />
                                <label class="form-check-label">Active</label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" @onclick="CloseModal">Cancel</button>
                        <button type="submit" class="btn btn-primary" disabled="@isSubmitting">
                            @if (isSubmitting)
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            }
                            @(editingCategory?.Id > 0 ? "Update" : "Add") Category
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
}

@code {
    private List<CategoryDto> categories = new();
    private bool isLoading = true;
    private string searchTerm = "";
    private bool showModal = false;
    private bool isSubmitting = false;
    private CategoryDto? editingCategory = null;
    private CategoryModel categoryModel = new();

    public class CategoryDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public int ProductCount { get; set; }
        public bool IsActive { get; set; }
        public string? ImageUrl { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    public class CategoryModel
    {
        [Required(ErrorMessage = "Category name is required")]
        [StringLength(50, ErrorMessage = "Category name cannot exceed 50 characters")]
        public string Name { get; set; } = "";

        [StringLength(200, ErrorMessage = "Description cannot exceed 200 characters")]
        public string Description { get; set; } = "";

        public bool IsActive { get; set; } = true;
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadCategories();
    }

    private async Task LoadCategories()
    {
        isLoading = true;
        
        try
        {
            // TODO: Replace with actual API call to Catalog.API
            await Task.Delay(500); // Simulate API call
            
            // Mock data for now
            var allCategories = GenerateMockCategories();
            
            // Apply search filter
            if (!string.IsNullOrEmpty(searchTerm))
            {
                allCategories = allCategories.Where(c => c.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)).ToList();
            }
            
            categories = allCategories;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading categories: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private List<CategoryDto> GenerateMockCategories()
    {
        return new List<CategoryDto>
        {
            new() { Id = 1, Name = "Clothing", Description = "All types of clothing items", ProductCount = 45, IsActive = true, CreatedDate = DateTime.Now.AddDays(-30) },
            new() { Id = 2, Name = "Shoes", Description = "Footwear for all occasions", ProductCount = 23, IsActive = true, CreatedDate = DateTime.Now.AddDays(-25) },
            new() { Id = 3, Name = "Accessories", Description = "Fashion accessories and jewelry", ProductCount = 18, IsActive = true, CreatedDate = DateTime.Now.AddDays(-20) },
            new() { Id = 4, Name = "Electronics", Description = "Electronic gadgets and devices", ProductCount = 12, IsActive = false, CreatedDate = DateTime.Now.AddDays(-15) },
            new() { Id = 5, Name = "Sports", Description = "Sports and fitness equipment", ProductCount = 8, IsActive = true, CreatedDate = DateTime.Now.AddDays(-10) }
        };
    }

    private async Task HandleSearch()
    {
        await LoadCategories();
    }

    private void ShowAddModal()
    {
        editingCategory = null;
        categoryModel = new CategoryModel();
        showModal = true;
    }

    private void EditCategory(CategoryDto category)
    {
        editingCategory = category;
        categoryModel = new CategoryModel
        {
            Name = category.Name,
            Description = category.Description,
            IsActive = category.IsActive
        };
        showModal = true;
    }

    private void CloseModal()
    {
        showModal = false;
        editingCategory = null;
        categoryModel = new CategoryModel();
    }

    private async Task SaveCategory()
    {
        isSubmitting = true;
        
        try
        {
            // TODO: Implement API call to create/update category
            await Task.Delay(1000); // Simulate API call
            
            CloseModal();
            await LoadCategories();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error saving category: {ex.Message}");
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private async Task DeleteCategory(int categoryId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this category?"))
        {
            // TODO: Implement delete API call
            await Task.Delay(100);
            await LoadCategories();
        }
    }
}
