@page "/users"
@layout AdminLayout
@attribute [Authorize(Policy = "AdminOnly")]
@using Microsoft.AspNetCore.Authorization
@using Admin.Models
@using Admin.Services
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@inject RefitApiService ApiService

<PageTitle>Users - Musewears Admin</PageTitle>

@if (!string.IsNullOrEmpty(errorMessage))
{
    <div class="alert alert-danger" role="alert">
        <strong>Error:</strong> @errorMessage
    </div>
}

<!-- Breadcrumb -->
<div class="flex items-center flex-wrap justify-between gap20 mb-27">
        <h3>All Users</h3>
        <ul class="breadcrumbs flex items-center flex-wrap justify-start gap10">
            <li>
                <a href="/"><div class="text-tiny">Dashboard</div></a>
            </li>
            <li>
                <i class="icon-chevron-right"></i>
            </li>
            <li>
                <a href="#"><div class="text-tiny">User</div></a>
            </li>
            <li>
                <i class="icon-chevron-right"></i>
            </li>
            <li>
                <div class="text-tiny">All User</div>
            </li>
        </ul>
    </div>

<!-- User List -->
<div class="wg-box">
        <div class="flex items-center justify-between gap10 flex-wrap">
            <div class="wg-filter flex-grow">
                <form class="form-search">
                    <fieldset class="name">
                        <input type="text" placeholder="Search here..." @bind="searchTerm" @oninput="OnSearchChanged" name="name" tabindex="2" aria-required="true">
                    </fieldset>
                    <div class="button-submit">
                        <button type="submit"><i class="icon-search"></i></button>
                    </div>
                </form>
            </div>
            <a class="tf-button style-1 w208" href="/users/add"><i class="icon-plus"></i>Add new</a>
        </div>
        
        <div class="wg-table table-all-user">
            <ul class="table-title flex gap20 mb-14">
                <li>
                    <div class="body-title">User</div>
                </li>
                <li>
                    <div class="body-title">Phone</div>
                </li>
                <li>
                    <div class="body-title">Email</div>
                </li>
                <li>
                    <div class="body-title">Role</div>
                </li>
                <li>
                    <div class="body-title">Status</div>
                </li>
                <li>
                    <div class="body-title">Join Date</div>
                </li>
                <li>
                    <div class="body-title">Action</div>
                </li>
            </ul>
            
            @if (isLoading)
            {
                <div class="text-center p-4">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            }
            else if (filteredUsers?.Any() == true)
            {
                <ul class="flex flex-column">
                    @if (isLoading)
                    {
                        <div class="text-center p-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    }
                    else if (filteredUsers?.Any() == true)
                    {
                        @foreach (var user in filteredUsers)
                        {
                            <li class="wg-user item-row">
                                <div class="image">
                                    <img src="@(string.IsNullOrEmpty(user.Avatar) ? "assets/images/avatar/user-1.png" : user.Avatar)" alt="@user.Name">
                                </div>
                                <div class="flex items-center justify-between gap20 flex-grow">
                                    <div class="name">
                                        <a href="/users/@user.Id" class="body-title-2">@user.Name</a>
                                        <div class="text-tiny mt-3">@user.Username</div>
                                    </div>
                                    <div class="body-text">@user.Phone</div>
                                    <div class="body-text">@user.Email</div>
                                    <div class="body-text">
                                    @switch (user.Role)
                                    {
                                        case "Admin":
                                            <div class="block-available">Admin</div>
                                            break;
                                        case "Manager":
                                            <div class="block-warning">Manager</div>
                                            break;
                                        case "Customer":
                                            <div class="block-pending">Customer</div>
                                            break;
                                        default:
                                            <div class="block-pending">@user.Role</div>
                                            break;
                                    }
                                    </div>
                                    <div class="body-text">
                                    @if (user.IsActive)
                                    {
                                        <div class="block-available">Active</div>
                                    }
                                    else
                                    {
                                        <div class="block-not-available">Inactive</div>
                                    }
                                    </div>
                                    <div class="body-text">@user.JoinDate.ToString("MM/dd/yyyy")</div>
                                    <div class="list-icon-function">
                                    <div class="item eye">
                                        <a href="/users/@user.Id"><i class="icon-eye"></i></a>
                                    </div>
                                    <div class="item edit">
                                        <a href="/users/edit/@user.Id"><i class="icon-edit-3"></i></a>
                                    </div>
                                    <div class="item trash">
                                        <a href="#" @onclick="() => ToggleUserStatus(user.Id)" @onclick:preventDefault="true">
                                            @if (user.IsActive)
                                            {
                                                <i class="icon-user-x"></i>
                                            }
                                            else
                                            {
                                                <i class="icon-user-check"></i>
                                            }
                                        </a>
                                    </div>
                                    </div>
                                </div>
                        </li>
                        }
                    }
                    else
                    {
                        <div class="text-center p-4">
                            <div class="body-text">No users found.</div>
                        </div>
                    }
                </ul>
            }
            else
            {
                <div class="text-center p-4">
                    <p>No users found.</p>
                </div>
            }
        </div>
        
        <!-- Pagination -->
        <div class="divider"></div>
        <div class="flex items-center justify-between flex-wrap gap10 wgp-pagination">
            <div class="text-tiny">Showing @Math.Min(currentPage * pageSize, totalUsers) of @totalUsers entries</div>
            <ul class="wg-pagination">
                <li class="@(currentPage == 1 ? "disabled" : "")">
                    <a href="#" @onclick="() => ChangePage(currentPage - 1)" @onclick:preventDefault="true">Previous</a>
                </li>
                @for (int i = 1; i <= totalPages; i++)
                {
                    <li class="@(i == currentPage ? "active" : "")">
                        <a href="#" @onclick="() => ChangePage(i)" @onclick:preventDefault="true">@i</a>
                    </li>
                }
                <li class="@(currentPage == totalPages ? "disabled" : "")">
                    <a href="#" @onclick="() => ChangePage(currentPage + 1)" @onclick:preventDefault="true">Next</a>
                </li>
            </ul>
        </div>
</div>
<!-- /User List -->

@code {
    private List<UserDto>? users;
    private List<UserDto>? filteredUsers;
    private string searchTerm = "";
    private bool isLoading = true;
    private string? errorMessage;
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalUsers = 0;
    private int totalPages = 1;

    public class UserDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Username { get; set; } = "";
        public string Email { get; set; } = "";
        public string Phone { get; set; } = "";
        public string Role { get; set; } = "";
        public bool IsActive { get; set; }
        public DateTime JoinDate { get; set; }
        public string? Avatar { get; set; }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await LoadUsers();
        }
    }

    private async Task LoadUsers()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            var result = await ApiService.GetUsersAsync(
                pageIndex: currentPage - 1,
                pageSize: pageSize);

            users = result.Data.Select(u => new UserDto
            {
                Id = int.Parse(u.Id),
                Name = u.UserName ?? "Unknown",
                Username = u.UserName ?? "",
                Email = u.Email ?? "",
                Phone = "N/A",
                Role = "Customer",
                IsActive = true,
                JoinDate = DateTime.Now
            }).ToList();

            totalUsers = (int)result.Count;
            totalPages = (int)Math.Ceiling((double)totalUsers / pageSize);
            FilterUsers();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading users: {ex.Message}");
            errorMessage = $"Failed to load users: {ex.Message}";
            users = new List<UserDto>();
            totalUsers = 0;
            totalPages = 0;
            FilterUsers();
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void OnSearchChanged(ChangeEventArgs e)
    {
        searchTerm = e.Value?.ToString() ?? "";
        FilterUsers();
    }

    private void FilterUsers()
    {
        if (users == null) return;

        var filtered = string.IsNullOrEmpty(searchTerm) 
            ? users 
            : users.Where(u => u.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                              u.Email.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                              u.Username.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)).ToList();

        filteredUsers = filtered.Skip((currentPage - 1) * pageSize).Take(pageSize).ToList();
        totalUsers = filtered.Count;
        totalPages = (int)Math.Ceiling((double)totalUsers / pageSize);
    }

    private void ChangePage(int page)
    {
        if (page < 1 || page > totalPages) return;
        currentPage = page;
        FilterUsers();
    }

    private async Task ToggleUserStatus(int userId)
    {
        try
        {
            // TODO: Implement user status toggle functionality via API
            await Task.Delay(100); // Simulate API call
            var user = users?.FirstOrDefault(u => u.Id == userId);
            if (user != null)
            {
                user.IsActive = !user.IsActive;
                FilterUsers();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error toggling user status: {ex.Message}");
        }
    }
}
