@page "/inventory"
@layout AdminLayout
@attribute [Authorize(Policy = "AdminOnly")]

<PageTitle>Inventory Management - Musewears Admin</PageTitle>

<div class="tf-section-2 mb-30">
    <!-- Breadcrumb -->
    <div class="flex items-center flex-wrap justify-between gap20 mb-27">
        <h3>Inventory Management</h3>
        <ul class="breadcrumbs flex items-center flex-wrap justify-start gap10">
            <li>
                <a href="/"><div class="text-tiny">Dashboard</div></a>
            </li>
            <li>
                <i class="icon-chevron-right"></i>
            </li>
            <li>
                <a href="#"><div class="text-tiny">Products</div></a>
            </li>
            <li>
                <i class="icon-chevron-right"></i>
            </li>
            <li>
                <div class="text-tiny">Inventory</div>
            </li>
        </ul>
    </div>
    
    <!-- Inventory Stats -->
    <div class="wg-box mb-30">
        <h5>Inventory Overview</h5>
        <div class="flex flex-wrap gap20">
            <div class="w-quarter">
                <div class="wg-chart-default">
                    <div class="flex items-center gap14">
                        <div class="image ic-bg">
                            <i class="icon-package"></i>
                        </div>
                        <div>
                            <div class="body-text mb-2">Total Products</div>
                            <h4>@totalProducts</h4>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-quarter">
                <div class="wg-chart-default">
                    <div class="flex items-center gap14">
                        <div class="image ic-bg">
                            <i class="icon-alert-triangle"></i>
                        </div>
                        <div>
                            <div class="body-text mb-2">Low Stock</div>
                            <h4 class="text-danger">@lowStockCount</h4>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-quarter">
                <div class="wg-chart-default">
                    <div class="flex items-center gap14">
                        <div class="image ic-bg">
                            <i class="icon-x-circle"></i>
                        </div>
                        <div>
                            <div class="body-text mb-2">Out of Stock</div>
                            <h4 class="text-danger">@outOfStockCount</h4>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-quarter">
                <div class="wg-chart-default">
                    <div class="flex items-center gap14">
                        <div class="image ic-bg">
                            <i class="icon-trending-up"></i>
                        </div>
                        <div>
                            <div class="body-text mb-2">Total Value</div>
                            <h4>₦@totalInventoryValue.ToString("N0")</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Inventory List -->
    <div class="wg-box">
        <div class="flex items-center justify-between gap10 flex-wrap">
            <div class="wg-filter flex-grow">
                <form class="form-search">
                    <fieldset class="name">
                        <input type="text" placeholder="Search products..." @bind="searchTerm" @oninput="OnSearchChanged" name="name" tabindex="2" aria-required="true">
                    </fieldset>
                    <div class="button-submit">
                        <button type="submit"><i class="icon-search"></i></button>
                    </div>
                </form>
            </div>
            <div class="dropdown default">
                <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <span class="view-all">@selectedFilter<i class="icon-chevron-down"></i></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="#" @onclick='() => FilterByStock("All")' @onclick:preventDefault="true">All Products</a></li>
                    <li><a class="dropdown-item" href="#" @onclick='() => FilterByStock("Low Stock")' @onclick:preventDefault="true">Low Stock</a></li>
                    <li><a class="dropdown-item" href="#" @onclick='() => FilterByStock("Out of Stock")' @onclick:preventDefault="true">Out of Stock</a></li>
                </ul>
            </div>
        </div>
        
        <div class="wg-table table-all-category">
            <ul class="table-title flex gap20 mb-14">
                <li>
                    <div class="body-title">Product</div>
                </li>
                <li>
                    <div class="body-title">SKU</div>
                </li>
                <li>
                    <div class="body-title">Current Stock</div>
                </li>
                <li>
                    <div class="body-title">Restock Level</div>
                </li>
                <li>
                    <div class="body-title">Max Stock</div>
                </li>
                <li>
                    <div class="body-title">Unit Price</div>
                </li>
                <li>
                    <div class="body-title">Total Value</div>
                </li>
                <li>
                    <div class="body-title">Status</div>
                </li>
                <li>
                    <div class="body-title">Action</div>
                </li>
            </ul>
            
            @if (isLoading)
            {
                <div class="text-center p-4">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            }
            else if (filteredInventory?.Any() == true)
            {
                <ul class="flex flex-column">
                    @foreach (var item in filteredInventory)
                    {
                        <li class="product-item gap14">
                            <div class="image no-bg">
                                <img src="@(string.IsNullOrEmpty(item.PictureUri) ? "assets/images/products/default.png" : item.PictureUri)" alt="@item.Name">
                            </div>
                            <div class="flex items-center justify-between gap20 flex-grow">
                                <div class="name">
                                    <a href="/products/@item.Id" class="body-title-2">@item.Name</a>
                                    <div class="text-tiny mt-3">@item.Category</div>
                                </div>
                                <div class="body-text">@item.Sku</div>
                                <div class="body-text">
                                    <span class="@(item.CurrentStock <= item.RestockThreshold ? "text-danger" : "")">
                                        @item.CurrentStock
                                    </span>
                                </div>
                                <div class="body-text">@item.RestockThreshold</div>
                                <div class="body-text">@item.MaxStockThreshold</div>
                                <div class="body-text">₦@item.Price.ToString("N0")</div>
                                <div class="body-text">₦@((item.CurrentStock * item.Price).ToString("N0"))</div>
                                <div class="body-text">
                                    @if (item.CurrentStock == 0)
                                    {
                                        <div class="block-not-available">Out of Stock</div>
                                    }
                                    else if (item.CurrentStock <= item.RestockThreshold)
                                    {
                                        <div class="block-warning">Low Stock</div>
                                    }
                                    else
                                    {
                                        <div class="block-available">In Stock</div>
                                    }
                                </div>
                                <div class="list-icon-function">
                                    <div class="item edit">
                                        <a href="#" @onclick="() => ShowUpdateStockModal(item)" @onclick:preventDefault="true" title="Update Stock">
                                            <i class="icon-edit-3"></i>
                                        </a>
                                    </div>
                                    <div class="item eye">
                                        <a href="/products/@item.Id" title="View Product">
                                            <i class="icon-eye"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </li>
                    }
                </ul>
            }
            else
            {
                <div class="text-center p-4">
                    <p>No inventory items found.</p>
                </div>
            }
        </div>
    </div>
</div>

<!-- Update Stock Modal -->
@if (showUpdateModal && selectedItem != null)
{
    <div class="modal-overlay" @onclick="CloseUpdateModal">
        <div class="modal-content" @onclick:stopPropagation="true">
            <div class="modal-header">
                <h5>Update Stock - @selectedItem.Name</h5>
                <button type="button" class="btn-close" @onclick="CloseUpdateModal">
                    <i class="icon-x"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>Current Stock: @selectedItem.CurrentStock</label>
                </div>
                <div class="form-group">
                    <label for="newStock">New Stock Quantity</label>
                    <input type="number" id="newStock" @bind="newStockQuantity" class="form-control" min="0" />
                </div>
                <div class="form-group">
                    <label for="reason">Reason for Change</label>
                    <select id="reason" @bind="updateReason" class="form-control">
                        <option value="">Select reason</option>
                        <option value="Restock">Restock</option>
                        <option value="Sale">Sale</option>
                        <option value="Damage">Damage</option>
                        <option value="Return">Return</option>
                        <option value="Adjustment">Inventory Adjustment</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="tf-button style-2" @onclick="CloseUpdateModal">Cancel</button>
                <button type="button" class="tf-button" @onclick="UpdateStock" disabled="@isUpdating">
                    @if (isUpdating)
                    {
                        <span class="spinner-border spinner-border-sm me-2"></span>
                    }
                    Update Stock
                </button>
            </div>
        </div>
    </div>
}

@code {
    private List<InventoryItemDto>? inventory;
    private List<InventoryItemDto>? filteredInventory;
    private string searchTerm = "";
    private string selectedFilter = "All Products";
    private bool isLoading = true;
    private bool showUpdateModal = false;
    private bool isUpdating = false;
    private InventoryItemDto? selectedItem;
    private int newStockQuantity = 0;
    private string updateReason = "";

    // Stats
    private int totalProducts = 0;
    private int lowStockCount = 0;
    private int outOfStockCount = 0;
    private decimal totalInventoryValue = 0;

    public class InventoryItemDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Sku { get; set; } = "";
        public string Category { get; set; } = "";
        public int CurrentStock { get; set; }
        public int RestockThreshold { get; set; }
        public int MaxStockThreshold { get; set; }
        public decimal Price { get; set; }
        public string? PictureUri { get; set; }
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadInventory();
    }

    private async Task LoadInventory()
    {
        isLoading = true;
        
        // TODO: Replace with actual API call to Catalog.API
        await Task.Delay(500); // Simulate API call
        
        // Mock data for now
        inventory = new List<InventoryItemDto>
        {
            new() { Id = 1, Name = "Classic T-Shirt", Sku = "TSH-001", Category = "Clothing", CurrentStock = 50, RestockThreshold = 10, MaxStockThreshold = 100, Price = 2500 },
            new() { Id = 2, Name = "Denim Jeans", Sku = "JNS-001", Category = "Clothing", CurrentStock = 8, RestockThreshold = 5, MaxStockThreshold = 50, Price = 8500 },
            new() { Id = 3, Name = "Sneakers", Sku = "SNK-001", Category = "Shoes", CurrentStock = 0, RestockThreshold = 8, MaxStockThreshold = 40, Price = 12000 },
            new() { Id = 4, Name = "Hoodie", Sku = "HOD-001", Category = "Clothing", CurrentStock = 3, RestockThreshold = 10, MaxStockThreshold = 60, Price = 6500 },
            new() { Id = 5, Name = "Summer Dress", Sku = "DRS-001", Category = "Clothing", CurrentStock = 15, RestockThreshold = 5, MaxStockThreshold = 30, Price = 4500 },
            new() { Id = 6, Name = "Baseball Cap", Sku = "CAP-001", Category = "Accessories", CurrentStock = 25, RestockThreshold = 15, MaxStockThreshold = 80, Price = 1500 },
            new() { Id = 7, Name = "Leather Jacket", Sku = "JKT-001", Category = "Clothing", CurrentStock = 2, RestockThreshold = 5, MaxStockThreshold = 20, Price = 15000 }
        };
        
        CalculateStats();
        FilterInventory();
        isLoading = false;
    }

    private void CalculateStats()
    {
        if (inventory == null) return;

        totalProducts = inventory.Count;
        lowStockCount = inventory.Count(i => i.CurrentStock > 0 && i.CurrentStock <= i.RestockThreshold);
        outOfStockCount = inventory.Count(i => i.CurrentStock == 0);
        totalInventoryValue = inventory.Sum(i => i.CurrentStock * i.Price);
    }

    private void OnSearchChanged(ChangeEventArgs e)
    {
        searchTerm = e.Value?.ToString() ?? "";
        FilterInventory();
    }

    private void FilterByStock(string filter)
    {
        selectedFilter = filter;
        FilterInventory();
    }

    private void FilterInventory()
    {
        if (inventory == null) return;

        var filtered = inventory.AsEnumerable();

        // Apply search filter
        if (!string.IsNullOrEmpty(searchTerm))
        {
            filtered = filtered.Where(i => i.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                                          i.Sku.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                                          i.Category.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
        }

        // Apply stock filter
        filtered = selectedFilter switch
        {
            "Low Stock" => filtered.Where(i => i.CurrentStock > 0 && i.CurrentStock <= i.RestockThreshold),
            "Out of Stock" => filtered.Where(i => i.CurrentStock == 0),
            _ => filtered
        };

        filteredInventory = filtered.ToList();
    }

    private void ShowUpdateStockModal(InventoryItemDto item)
    {
        selectedItem = item;
        newStockQuantity = item.CurrentStock;
        updateReason = "";
        showUpdateModal = true;
    }

    private void CloseUpdateModal()
    {
        showUpdateModal = false;
        selectedItem = null;
        newStockQuantity = 0;
        updateReason = "";
    }

    private async Task UpdateStock()
    {
        if (selectedItem == null) return;

        isUpdating = true;
        
        try
        {
            // TODO: Implement API call to update stock
            await Task.Delay(500); // Simulate API call
            
            selectedItem.CurrentStock = newStockQuantity;
            CalculateStats();
            FilterInventory();
            CloseUpdateModal();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error updating stock: {ex.Message}");
        }
        finally
        {
            isUpdating = false;
        }
    }
}

<style>
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    }

    .modal-content {
        background: white;
        border-radius: 8px;
        width: 90%;
        max-width: 500px;
        max-height: 90vh;
        overflow-y: auto;
    }

    .modal-header {
        padding: 20px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-body {
        padding: 20px;
    }

    .modal-footer {
        padding: 20px;
        border-top: 1px solid #eee;
        display: flex;
        gap: 10px;
        justify-content: flex-end;
    }

    .btn-close {
        background: none;
        border: none;
        font-size: 18px;
        cursor: pointer;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
    }

    .form-control {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    .w-quarter {
        width: 24%;
    }
</style>
