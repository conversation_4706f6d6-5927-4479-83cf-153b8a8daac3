@page "/login"
@layout EmptyLayout
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IAdminAuthApi AdminAuthApi
@inject IConfiguration Configuration

<PageTitle>Login - Musewears Admin</PageTitle>

<div class="login-page">
    <div class="login-container">
        <div class="login-box">
            <div class="login-header">
                <div class="logo">
                    <img src="assets/images/logo/logo.png" alt="Musewears Admin" class="logo-img">
                </div>
                <h2 class="login-title">Admin Login</h2>
                <p class="login-subtitle">Sign in to your admin account</p>
            </div>
            
            <EditForm Model="@loginModel" OnValidSubmit="@HandleLogin" class="login-form">
                <DataAnnotationsValidator />
                
                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <div class="alert alert-danger" role="alert">
                        @errorMessage
                    </div>
                }
                
                <div class="form-group">
                    <label for="email" class="form-label">Email Address</label>
                    <div class="input-group">
                        <span class="input-icon">
                            <i class="icon-mail"></i>
                        </span>
                        <InputText @bind-Value="loginModel.Email" class="form-control" id="email" placeholder="Enter your email" />
                    </div>
                    <ValidationMessage For="@(() => loginModel.Email)" class="text-danger" />
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">Password</label>
                    <div class="input-group">
                        <span class="input-icon">
                            <i class="icon-lock"></i>
                        </span>
                        <InputText @bind-Value="loginModel.Password" type="@(showPassword ? "text" : "password")" class="form-control" id="password" placeholder="Enter your password" />
                        <button type="button" class="password-toggle" @onclick="TogglePasswordVisibility">
                            <i class="@(showPassword ? "icon-eye-off" : "icon-eye")"></i>
                        </button>
                    </div>
                    <ValidationMessage For="@(() => loginModel.Password)" class="text-danger" />
                </div>
                
                <div class="form-options">
                    <div class="remember-me">
                        <InputCheckbox @bind-Value="loginModel.RememberMe" id="remember" />
                        <label for="remember">Remember me</label>
                    </div>
                    <a href="/forgot-password" class="forgot-password">Forgot password?</a>
                </div>
                
                <button type="submit" class="btn-login" disabled="@isLoading">
                    @if (isLoading)
                    {
                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        <span>Signing in...</span>
                    }
                    else
                    {
                        <span>Sign In</span>
                        <i class="icon-arrow-right"></i>
                    }
                </button>
            </EditForm>
            
            <div class="login-footer">
                <p>Don't have an admin account? <a href="/contact">Contact support</a></p>
            </div>
        </div>
    </div>
</div>

<style>
    .login-page {
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
    }

    .login-container {
        width: 100%;
        max-width: 400px;
    }

    .login-box {
        background: white;
        border-radius: 12px;
        padding: 40px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .login-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .logo-img {
        height: 50px;
        margin-bottom: 20px;
    }

    .login-title {
        font-size: 24px;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
    }

    .login-subtitle {
        color: #666;
        margin-bottom: 0;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
    }

    .input-group {
        position: relative;
        display: flex;
        align-items: center;
    }

    .input-icon {
        position: absolute;
        left: 12px;
        color: #666;
        z-index: 2;
    }

    .form-control {
        width: 100%;
        padding: 12px 12px 12px 40px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 14px;
        transition: border-color 0.3s;
    }

    .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .password-toggle {
        position: absolute;
        right: 12px;
        background: none;
        border: none;
        color: #666;
        cursor: pointer;
        z-index: 2;
    }

    .form-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
    }

    .remember-me {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .forgot-password {
        color: #667eea;
        text-decoration: none;
        font-size: 14px;
    }

    .forgot-password:hover {
        text-decoration: underline;
    }

    .btn-login {
        width: 100%;
        padding: 12px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: transform 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .btn-login:hover:not(:disabled) {
        transform: translateY(-1px);
    }

    .btn-login:disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }

    .login-footer {
        text-align: center;
        margin-top: 25px;
        color: #666;
        font-size: 14px;
    }

    .login-footer a {
        color: #667eea;
        text-decoration: none;
    }

    .login-footer a:hover {
        text-decoration: underline;
    }

    .alert {
        padding: 12px;
        border-radius: 6px;
        margin-bottom: 20px;
    }

    .alert-danger {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }

    .text-danger {
        color: #dc3545;
        font-size: 12px;
        margin-top: 4px;
    }
</style>

@code {
    private AdminLoginRequest loginModel = new();
    private bool isLoading = false;
    private bool showPassword = false;
    private string errorMessage = "";

    private void TogglePasswordVisibility()
    {
        showPassword = !showPassword;
    }

    private async Task HandleLogin()
    {
        isLoading = true;
        errorMessage = "";

        try
        {
            // Use Refit API interface with the loginModel directly
            var result = await AdminAuthApi.LoginAsync(loginModel);

            if (result?.Success == true && !string.IsNullOrEmpty(result.Token))
            {
                // Store token in localStorage
                await JSRuntime.InvokeVoidAsync("localStorage.setItem", "authToken", result.Token);

                // Notify authentication state provider with the token
                if (AuthenticationStateProvider is Admin.Authentication.AdminAuthenticationStateProvider provider)
                {
                    provider.NotifyUserAuthentication(result.Token);
                }

                // Wait a moment for the authentication state to be updated
                await Task.Delay(100);

                // Redirect to dashboard
                Navigation.NavigateTo("/", true);
            }
            else
            {
                errorMessage = result?.Message ?? "Login failed";
            }
        }
        catch (Refit.ApiException apiEx)
        {
            if (apiEx.StatusCode == System.Net.HttpStatusCode.Unauthorized)
            {
                errorMessage = "Invalid email or password";
            }
            else
            {
                errorMessage = "Unable to connect to the server. Please try again later.";
                
                Console.WriteLine($"API error: {apiEx.Message}");
            }
        }
        catch (HttpRequestException ex)
        {
            errorMessage = "Unable to connect to the server. Please try again later.";
            
            Console.WriteLine($"HTTP error: {ex.Message}");
        }
        catch (Exception ex)
        {
            errorMessage = "An error occurred during login. Please try again.";
            Console.WriteLine($"Login error: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }
}
