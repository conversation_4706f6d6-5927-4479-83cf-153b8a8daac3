@page "/products"
@layout AdminLayout
@attribute [Authorize(Policy = "AdminOnly")]
@using Microsoft.AspNetCore.Authorization
@using Admin.Models
@using Admin.Services
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@inject RefitApiService ApiService

<PageTitle>Product List - Musewears Admin</PageTitle>

@if (!string.IsNullOrEmpty(errorMessage))
{
    <div class="alert alert-danger" role="alert">
        <strong>Error:</strong> @errorMessage
    </div>
}

<!-- Breadcrumb -->
<div class="flex items-center flex-wrap justify-between gap20 mb-27">
    <h3>Product List</h3>
    <ul class="breadcrumbs flex items-center flex-wrap justify-start gap10">
        <li>
            <a href="/"><div class="text-tiny">Dashboard</div></a>
        </li>
        <li>
            <i class="icon-chevron-right"></i>
        </li>
        <li>
            <a href="#"><div class="text-tiny">Ecommerce</div></a>
        </li>
        <li>
            <i class="icon-chevron-right"></i>
        </li>
        <li>
            <div class="text-tiny">Product List</div>
        </li>
    </ul>
</div>
<!-- product-list -->
<div class="wg-box">
    <div class="title-box">
        <i class="icon-coffee"></i>
        <div class="body-text">Tip search by Product ID: Each product is provided with a unique ID, which you can rely on to find the exact product you need.</div>
    </div>
    <div class="flex items-center justify-between gap10 flex-wrap">
        <div class="wg-filter flex-grow">
            <div class="show">
                <div class="text-tiny">Showing</div>
                <div class="select">
                    <select class="">
                        <option>10</option>
                        <option>20</option>
                        <option>30</option>
                    </select>
                </div>
                <div class="text-tiny">entries</div>
            </div>
            <form class="form-search" @onsubmit="HandleSearch" @onsubmit:preventDefault="true">
                <fieldset class="name">
                    <input type="text" placeholder="Search here..." @bind="searchTerm" @bind:event="oninput" name="name" tabindex="2" aria-required="true">
                </fieldset>
                <div class="button-submit">
                    <button type="submit"><i class="icon-search"></i></button>
                </div>
            </form>
        </div>
        <a class="tf-button style-1 w208" href="/products/add"><i class="icon-plus"></i>Add new</a>
    </div>
        
    <div class="wg-table table-product-list">
            <ul class="table-title flex gap20 mb-14">
                <li>
                    <div class="body-title">Product</div>
                </li>
                <li>
                    <div class="body-title">Product ID</div>
                </li>
                <li>
                    <div class="body-title">Price</div>
                </li>
                <li>
                    <div class="body-title">Quantity</div>
                </li>
                <li>
                    <div class="body-title">Sale</div>
                </li>
                <li>
                    <div class="body-title">Stock</div>
                </li>
                <li>
                    <div class="body-title">Start date</div>
                </li>
                <li>
                    <div class="body-title">Action</div>
                </li>
            </ul>
            
        <ul class="flex flex-column">
            @if (isLoading)
            {
                <li class="text-center p-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </li>
            }
            else if (products.Any())
            {
                @foreach (var product in products)
                {
                    <li class="product-item gap14">
                        <div class="image no-bg">
                            <img src="@(product.PictureUri ?? "assets/images/products/41.png")" alt="@product.Name">
                        </div>
                        <div class="flex items-center justify-between gap20 flex-grow">
                            <div class="name">
                                <a href="/products/@product.Id" class="body-title-2">@product.Name</a>
                            </div>
                            <div class="body-text">#@product.Id</div>
                            <div class="body-text">₦@product.Price.ToString("N2")</div>
                            <div class="body-text">@product.AvailableStock</div>
                            <div class="body-text">0</div>
                            <div>
                                @if (product.AvailableStock > 0)
                                {
                                    <div class="block-available">In stock</div>
                                }
                                else
                                {
                                    <div class="block-not-available">Out of stock</div>
                                }
                            </div>
                            <div class="body-text">@DateTime.Now.ToString("dd MMM yyyy")</div>
                            <div class="list-icon-function">
                                <div class="item eye">
                                    <a href="/products/@product.Id"><i class="icon-eye"></i></a>
                                </div>
                                <div class="item edit">
                                    <a href="/products/@product.Id/edit"><i class="icon-edit-3"></i></a>
                                </div>
                                <div class="item trash">
                                    <button @onclick="() => DeleteProduct(product.Id)" class="btn-link"><i class="icon-trash-2"></i></button>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li class="divider"></li>
                }
            }
            else
            {
                <li class="text-center p-4">
                    <div class="body-text">No products found.</div>
                </li>
            }
        </ul>
    </div>

    <!-- Pagination -->
    @if (totalPages > 1)
    {
        <div class="divider"></div>
        <div class="flex items-center justify-between flex-wrap gap10">
            <div class="text-tiny">Showing @((currentPage - 1) * pageSize + 1) to @Math.Min(currentPage * pageSize, totalItems) of @totalItems entries</div>
            <ul class="wg-pagination">
                <li class="@(currentPage == 1 ? "disabled" : "")">
                    <a href="#" @onclick="() => ChangePage(currentPage - 1)" @onclick:preventDefault="true">Previous</a>
                </li>
                @for (int i = 1; i <= totalPages; i++)
                {
                    <li class="@(i == currentPage ? "active" : "")">
                        <a href="#" @onclick="() => ChangePage(i)" @onclick:preventDefault="true">@i</a>
                    </li>
                }
                <li class="@(currentPage == totalPages ? "disabled" : "")">
                    <a href="#" @onclick="() => ChangePage(currentPage + 1)" @onclick:preventDefault="true">Next</a>
                </li>
            </ul>
        </div>
    }
</div>

@code {
    private List<CatalogItem> products = new();
    private bool isLoading = true;
    private string? errorMessage;
    private string searchTerm = "";
    private int pageSize = 10;
    private int currentPage = 1;
    private int totalItems = 0;
    private int totalPages = 0;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await LoadProducts();
        }
    }

    private async Task LoadProducts()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            var result = await ApiService.GetCatalogItemsAsync(
                pageIndex: currentPage - 1,
                pageSize: pageSize,
                name: string.IsNullOrEmpty(searchTerm) ? null : searchTerm);

            products = result.Data.ToList();
            totalItems = (int)result.Count;
            totalPages = (int)Math.Ceiling((double)totalItems / pageSize);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading products: {ex.Message}");
            errorMessage = $"Failed to load products: {ex.Message}";
            products = new List<CatalogItem>();
            totalItems = 0;
            totalPages = 0;
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task HandleSearch()
    {
        currentPage = 1;
        await LoadProducts();
    }

    private async Task ChangePage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            await LoadProducts();
        }
    }

    private async Task DeleteProduct(int productId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this product?"))
        {
            try
            {
                await ApiService.DeleteCatalogItemAsync(productId);
                await LoadProducts();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting product: {ex.Message}");
                await JSRuntime.InvokeVoidAsync("alert", "Failed to delete product. Please try again.");
            }
        }
    }
}
