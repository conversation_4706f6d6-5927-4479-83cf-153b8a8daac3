@using Microsoft.AspNetCore.Components.Web
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <base href="/"/>

    <!-- Template CSS -->
    <link rel="stylesheet" href="assets/css/animate.min.css"/>
    <link rel="stylesheet" href="assets/css/animation.css"/>
    <link rel="stylesheet" href="assets/css/bootstrap.css"/>
    <link rel="stylesheet" href="assets/css/bootstrap-select.min.css"/>
    <link rel="stylesheet" href="assets/css/style.css"/>

    <!-- Font -->
    <link rel="stylesheet" href="assets/font/fonts.css"/>

    <!-- Icon -->
    <link rel="stylesheet" href="assets/icon/style.css"/>

    <!-- Custom Admin CSS -->
    @* <link rel="stylesheet" href="admin-custom.css"/> *@

    <!-- Blazor CSS -->
    <link rel="stylesheet" href="@Assets["Admin.styles.css"]"/>
    <ImportMap/>
    <!-- Favicon and Touch Icons -->
    <link rel="shortcut icon" href="assets/images/favicon.png"/>
    <link rel="apple-touch-icon-precomposed" href="assets/images/favicon.png"/>
    <HeadOutlet @rendermode="@(new InteractiveServerRenderMode(prerender: false))"/>
</head>

<body class="body">
    <Routes @rendermode="@(new InteractiveServerRenderMode(prerender: false))"/>

    <!-- Template JavaScript -->
    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
    <script src="assets/js/bootstrap-select.min.js"></script>
    <script src="assets/js/zoom.js"></script>
    <script src="assets/js/apexcharts/apexcharts.js"></script>
    <script src="assets/js/apexcharts/line-chart-1.js"></script>
    <script src="assets/js/apexcharts/line-chart-2.js"></script>
    <script src="assets/js/apexcharts/line-chart-3.js"></script>
    <script src="assets/js/apexcharts/line-chart-4.js"></script>
    <script src="assets/js/apexcharts/line-chart-5.js"></script>
    <script src="assets/js/apexcharts/line-chart-6.js"></script>
    <script src="assets/js/theme-settings.js"></script>
    <script src="assets/js/main.js"></script>

    <!-- Blazor JavaScript -->
    <script src="_framework/blazor.web.js"></script>
</body>

</html>