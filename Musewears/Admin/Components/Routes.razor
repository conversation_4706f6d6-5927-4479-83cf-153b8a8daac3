<Router AppAssembly="typeof(Program).Assembly">
    <Found Context="routeData">
        <RouteView RouteData="routeData" DefaultLayout="typeof(Layout.AdminLayout)" />
        <FocusOnNavigate RouteData="routeData" Selector="h1"/>
    </Found>
    <NotFound>
        <PageTitle>Not found</PageTitle>
        <LayoutView Layout="typeof(Layout.AdminLayout)">
            <div class="alert alert-warning" role="alert">
                Sorry, there's nothing at this address.
            </div>
        </LayoutView>
    </NotFound>
</Router>

@code {
    private class RedirectToLogin : ComponentBase
    {
        [Inject] private NavigationManager Navigation { get; set; } = default!;

        protected override void OnInitialized()
        {
            Navigation.NavigateTo("/login", true);
        }
    }
}