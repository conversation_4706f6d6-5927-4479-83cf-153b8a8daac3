@inherits LayoutComponentBase
@using Microsoft.AspNetCore.Components.Authorization
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider

<CascadingAuthenticationState>
    <!-- #wrapper -->
    <div id="wrapper">
        <!-- #page -->
        <div id="page" class="">
            <!-- layout-wrap -->
            <div class="layout-wrap">
                <!-- preload -->
                <div id="preload" class="preload-container">
                    <div class="preloading">
                        <span></span>
                    </div>
                </div>
                <!-- /preload -->

                <!-- Sidebar -->
                <AdminSidebar />

                <!-- Main Content -->
                <div class="section-content-right">
                    <!-- Header -->
                    <AdminHeader />

                    <!-- Main Content Area -->
                    <div class="main-content">
                        <div class="main-content-inner">
                            <div class="main-content-wrap">
                                @Body
                            </div>
                        </div>
                        <!-- bottom-page -->
                        <div class="bottom-page">
                            <div class="body-text">Copyright © 2024 Remos. Design with</div>
                            <i class="icon-heart"></i>
                            <div class="body-text">by <a href="https://themeforest.net/user/themesflat/portfolio">Themesflat</a> All rights reserved.</div>
                        </div>
                        <!-- /bottom-page -->
                    </div>
                </div>
            </div>
            <!-- /layout-wrap -->
        </div>
        <!-- /#page -->
    </div>
    <!-- /#wrapper -->
</CascadingAuthenticationState>

<div id="blazor-error-ui" data-nosnippet>
    An unhandled error has occurred.
    <a href="." class="reload">Reload</a>
    <span class="dismiss">🗙</span>
</div>

<style>
    #blazor-error-ui {
        background: lightyellow;
        bottom: 0;
        box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
        display: none;
        left: 0;
        padding: 0.6rem 1.25rem 0.7rem 1.25rem;
        position: fixed;
        width: 100%;
        z-index: 1000;
    }

    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }
</style>

@code {
    private bool _hasInitializedAuth = false;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Initialize authentication state from localStorage after first render
            // This prevents JavaScript interop issues during prerendering
            if (!_hasInitializedAuth && AuthenticationStateProvider is Admin.Authentication.AdminAuthenticationStateProvider provider)
            {
                _hasInitializedAuth = true;
                await provider.InitializeAuthenticationAsync();
            }

            // Ensure preloader is hidden after component renders
            await JSRuntime.InvokeVoidAsync("eval", @"
                if (typeof $ !== 'undefined') {
                    setTimeout(function() {
                        $('#preload').fadeOut('slow', function() {
                            $(this).remove();
                        });
                    }, 500);
                }
            ");
        }
    }
}
