@using Microsoft.AspNetCore.Components.Authorization
@inject IJSRuntime JSRuntime

<div class="section-menu-left">
    <div class="box-logo">
        <a href="/" id="site-logo-inner">
            <img class="" id="logo_header" alt="" src="assets/images/logo/logo.png"
                 data-light="assets/images/logo/logo.png" data-dark="assets/images/logo/logo-dark.png">
        </a>
        <div class="button-show-hide">
            <i class="icon-menu-left"></i>
        </div>
    </div>
    <div class="section-menu-left-wrap">
        <div class="center">
            <div class="center-item">
                <div class="center-heading">Main Home</div>
                <ul class="menu-list">
                    <li class="menu-item has-children active">
                        <a href="javascript:void(0);" class="menu-item-button">
                            <div class="icon"><i class="icon-grid"></i></div>
                            <div class="text">Dashboard</div>
                        </a>
                        <ul class="sub-menu" style="display: block;">
                            <li class="sub-menu-item">
                                <NavLink href="/" class="" Match="NavLinkMatch.All">
                                    <div class="text">Home 01</div>
                                </NavLink>
                            </li>
                            <li class="sub-menu-item">
                                <NavLink href="/" class="active">
                                    <div class="text">Home 02</div>
                                </NavLink>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
            <div class="center-item">
                <div class="center-heading">All page</div>
                <ul class="menu-list">
                    <li class="menu-item has-children">
                        <a href="javascript:void(0);" class="menu-item-button">
                            <div class="icon"><i class="icon-shopping-cart"></i></div>
                            <div class="text">Ecommerce</div>
                        </a>
                        <ul class="sub-menu">
                            <li class="sub-menu-item">
                                <NavLink href="/products/add" class="">
                                    <div class="text">Add Product</div>
                                </NavLink>
                            </li>
                            <li class="sub-menu-item">
                                <NavLink href="/products" class="">
                                    <div class="text">Product List</div>
                                </NavLink>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item has-children">
                        <a href="javascript:void(0);" class="menu-item-button">
                            <div class="icon"><i class="icon-layers"></i></div>
                            <div class="text">Category</div>
                        </a>
                        <ul class="sub-menu">
                            <li class="sub-menu-item">
                                <NavLink href="/categories" class="">
                                    <div class="text">Category list</div>
                                </NavLink>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item has-children">
                        <a href="javascript:void(0);" class="menu-item-button">
                            <div class="icon"><i class="icon-file-plus"></i></div>
                            <div class="text">Order</div>
                        </a>
                        <ul class="sub-menu">
                            <li class="sub-menu-item">
                                <NavLink href="/orders" class="">
                                    <div class="text">Order list</div>
                                </NavLink>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item has-children">
                        <a href="javascript:void(0);" class="menu-item-button">
                            <div class="icon"><i class="icon-user"></i></div>
                            <div class="text">User</div>
                        </a>
                        <ul class="sub-menu">
                            <li class="sub-menu-item">
                                <NavLink href="/users" class="">
                                    <div class="text">All user</div>
                                </NavLink>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item">
                        <NavLink href="/gallery" class="">
                            <div class="icon"><i class="icon-image"></i></div>
                            <div class="text">Gallery</div>
                        </NavLink>
                    </li>
                    <li class="menu-item">
                        <NavLink href="/report" class="">
                            <div class="icon"><i class="icon-pie-chart"></i></div>
                            <div class="text">Report</div>
                        </NavLink>
                    </li>
                </ul>
            </div>
            <div class="center-item">
                <div class="center-heading">Setting</div>
                <ul class="menu-list">
                    <li class="menu-item">
                        <NavLink href="/settings" class="">
                            <div class="icon"><i class="icon-settings"></i></div>
                            <div class="text">Setting</div>
                        </NavLink>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

@code {
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Initialize the menu functionality after the component is rendered
            await JSRuntime.InvokeVoidAsync("eval", @"
                if (typeof $ !== 'undefined' && $('.section-menu-left').length > 0) {
                    var bt = $('.section-menu-left').find('.has-children');
                    bt.off('click').on('click', function () {
                        var args = { duration: 200 };
                        if ($(this).hasClass('active')) {
                            $(this).children('.sub-menu').slideUp(args);
                            $(this).removeClass('active');
                        } else {
                            $('.sub-menu').slideUp(args);
                            $(this).children('.sub-menu').slideDown(args);
                            $('.menu-item.has-children').removeClass('active');
                            $(this).addClass('active');
                        }
                    });
                    $('.sub-menu-item').off('click').on('click', function(event){
                        event.stopPropagation();
                    });
                }
            ");
        }
    }
}
