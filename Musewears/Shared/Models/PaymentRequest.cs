using System.Runtime.Serialization;

namespace Musewears.Shared.Models;

public enum CurrencyCode
{
    USD = 840,

    NGN = 566
}

public class PaymentRequest
{
    public string merchant_code { get; set; }
    public string pay_item_id { get; set; }
    public string pay_item_name { get; set; }
    public string txn_ref { get; set; }
    public string site_redirect_url { get; set; }
    public decimal amount { get; set; }
    public int currency { get; set; }
    public string mode { get; set; }

    public string secret { get; set; }
    public string client_id { get; set; }

    // Customer information fields
    public string? cust_name { get; set; }
    public string? cust_email { get; set; }
    public required string cust_id { get; set; }
    public string? cust_mobile_no { get; set; }
}