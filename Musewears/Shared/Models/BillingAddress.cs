using System.ComponentModel.DataAnnotations;

namespace Musewears.Shared.Models;

public class BillingAddress
{
    [Key]
    public string Id { get; set; }

    [Required]
    public string UserId { get; set; }

    [Required]
    [StringLength(100)]
    public string? Name { get; set; }

    [Required]
    [EmailAddress]
    [StringLength(255)]
    public string? Email { get; set; }

    [Required]
    public string? Address { get; set; }

    [Required]
    public string? City { get; set; }

    [Required]
    public string? State { get; set; }

    [Required]
    public string? Country { get; set; }

    [Required]
    [PostalCodeValidation] // Custom validation attribute for zip codes
    public string? ZipCode { get; set; }

    [Required]
    [Phone]
    public string? Phone { get; set; }

    public bool IsDefault { get; set; } = true;
}