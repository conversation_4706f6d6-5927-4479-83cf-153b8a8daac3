using System;
using System.ComponentModel.DataAnnotations;
namespace Musewears.Shared
{
	public class Wear
	{
		[Key]
		public string Id { get; set; }

		public string Name { get; set; }

		public string Photo { get; set; }

		public decimal Price { get; set; }

		public decimal CostPrice { get; set; }

		public string Currency { get; set; }

		public string Description { get; set; } = "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod kon tempor incididunt ut labore.";

		public string Features { get; set; } = "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiuski smod.";

		public bool IsNew { get; set; } = true;

		public float Rating { get; set; }
	}
}