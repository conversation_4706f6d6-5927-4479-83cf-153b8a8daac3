using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Musewears.Shared;
using Musewears.Shared.Models;

namespace Musewears.Shared.Models;

public class CartItem
{
    [Key]
    [Column(Order = 1)]
    public int Id { get; set; } // Primary key for the rating entry

    [ForeignKey("Wear")]
    public string WearId { get; set; }
    public virtual Wear Wear { get; set; }

    [Key]
    [Column(Order = 2)]
    [ForeignKey("AspNetUser")]
    public string UserId { get; set; }
    // public virtual ApplicationUser User { get; set; }

    public int Quantity { get; set; } // Number of items in the cart

    public string Name { get; set; }

    public string Photo { get; set; }
}