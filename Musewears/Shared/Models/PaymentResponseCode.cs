namespace Musewears.Shared.Models;

public struct PaymentResponseCode
{
    public const string ApprovedVIP = "11";
    public const string ApprovedPartial = "10";
    public const string Approved = "00";
    public const string InProgress = "09";
    public const string AccountErrorContactBank = "X00";
    public const string AmountAboveLimitContactBank = "X03";
    public const string AmountTooLow = "X04";
    public const string InvalidCardNumber = "X05";
    public const string IncorrectSecurityDetailsPinTriesExceeded = "14";
    public const string IncorrectCardDetailsVerifyExpiryDate = "56";
    public const string BankPreventedTransaction1 = "57";
    public const string BankPreventedTransaction2 = "61";
    public const string ReferToFinancialInstitution1 = "01";
    public const string ReferToFinancialInstitution2 = "02";
    public const string InvalidMerchant = "03";
    public const string PickUpCard = "04";
    public const string DoNotHonor = "05";
    public const string Error = "06";
    public const string PickUpCardSpecialCondition = "07";
    public const string HonorWithIdentification = "08";
    public const string RequestInProgress = "09";
    public const string InvalidTransaction = "12";
    public const string NoSuchFinancialInstitution = "13";
    public const string UpdateTrack3 = "15";
    public const string CustomerCancellation = "16";
    public const string CustomerDispute = "17";
    public const string ReenterTransaction = "18";
    public const string InvalidResponse = "19";
    public const string NoActionTaken = "20";
    public const string SuspectedMalfunction = "21";
    public const string UnacceptableTransactionFee = "22";
    public const string FileUpdateNotSupported = "23";
    public const string DuplicateRecord = "24";
    public const string FileUpdateEditError = "26";
    public const string FileUpdateLocked = "27";
    public const string FileUpdateFailed = "28";
    public const string FormatError = "29";
    public const string BankNotSupported = "30";
    public const string CompletedPartially = "31";
    public const string InstitutionExpiredCardPickUp = "32";
    public const string SuspectedFraudPickUp = "33";
    public const string ContactAcquirerPickUp = "34";
    public const string RestrictedCardPickUp = "35";
    public const string CallAcquirerSecurityPickUp = "36";
    public const string PINTriesExceededPickUp = "37";
    public const string NoCreditAccount = "38";
    public const string FunctionNotSupported = "39";
    public const string LostCardPickUp = "40";
    public const string NoUniversalAccount = "41";
    public const string NoInvestmentAccount = "42";
    public const string InsufficientFunds = "51";
    public const string NoCheckAccount = "52";
    public const string NoSavingsAccount = "53";
    public const string ExpiredCard = "54";
    public const string IncorrectPIN = "55";
    public const string NoCardRecord = "56";
    public const string SuspectedFraud = "59";
    public const string ContactAcquirer = "60";
    public const string RestrictedCard = "62";
    public const string SecurityViolation = "63";
    public const string OriginalAmountIncorrect = "64";
    public const string ExceedsWithdrawalFrequency = "65";
    public const string HardCapture = "66";
    public const string ResponseReceivedTooLate = "67";
    public const string PINtriesExceeded = "68";
    public const string InterveneBankApprovalRequired1 = "75";
    public const string InterveneBankApprovalRequired2 = "77";
    public const string CutoffInProgress = "78";
    public const string IssuerSwitchInoperative = "90";
    public const string RoutingError = "91";
    public const string ViolationOfLaw = "92";
    public const string DuplicateTransaction = "93";
    public const string ReconcileError = "94";
    public const string SystemMalfunction = "95";
    public const string ExceedsCashLimit = "96";
    public const string UnexpectedError = "98";
    public const string TransactionNotPermittedToCardHolder1 = "A0";
    public const string TransactionNotPermittedToCardHolder2 = "A4";
    public const string TransactionError = "Z1";
    public const string BankAccountError = "Z2";
    public const string BankCollectionsAccountError = "Z3";
    public const string InterfaceIntegrationError = "Z4";
    public const string DuplicateReferenceError = "Z5";
    public const string IncompleteTransaction = "Z6";
    public const string TransactionSplitPreprocessingError = "Z7";
    public const string InvalidCardNumberChannels = "Z7";
    public const string TransactionNotPermittedChannels = "Z8";
    public const string TransactionNotFound = "Z9";
    public const string PaymentRequiresToken = "Z25";
    public const string RequestToGenerateTokenSuccessful = "Z61";
    public const string TokenNotGeneratedCustomerNotRegistered = "Z62";
    public const string ErrorCouldNotGenerateToken = "Z63";
    public const string PaymentRequiresTokenAuthorization = "Z64";
    public const string TokenAuthorizationSuccessful = "Z65";
    public const string TokenAuthorizationNotSuccessful = "Z66";
    public const string ErrorAuthenticateToken = "Z67";
    public const string CustomerCancellationSecure3D = "Z68";
    public const string CardinalAuthenticationRequired = "Z69";
    public const string CardinalLookupSuccessful = "Z70";
    public const string CardinalLookupFailed = "Z71";
    public const string CardinalAuthenticateSuccessful = "Z72";
    public const string CardinalAuthenticateFailed = "Z73";
    public const string ErrorCallingCybersourceService = "Z74";
    public const string BinNotConfigured = "Z80";
    public const string MerchantNotConfiguredForBin = "Z81";
    public const string PendingTransactionStatusUnconfirmed = "Z82";
    public const string Pending = "Z0";

    // Response Descriptions
    public const string ApprovedVIPDescription = "Approved by Financial Institution, VIP";
    public const string ApprovedPartialDescription = "Approved by Financial Institution, Partial";
    public const string ApprovedDescription = "Approved by Financial Institution";
    public const string InProgressDescription = "Transaction In Progress";
    public const string AccountErrorContactBankDescription = "Account error, please contact your bank";
    public const string AmountAboveLimitContactBankDescription = "The amount requested is above the limit permitted by your bank, please contact your bank";
    public const string AmountTooLowDescription = "The amount requested is too low";
    public const string InvalidCardNumberDescription = "The card number inputted is invalid, please re-try with a valid card number";
    public const string IncorrectSecurityDetailsPinTriesExceededDescription = "Incorrect Security details provided. Pin tries exceeded.";
    public const string IncorrectCardDetailsVerifyExpiryDateDescription = "Incorrect card details, please verify that the expiry date inputted is correct.";
    public const string BankPreventedTransaction1Description = "Your bank has prevented your card from carrying out this transaction, please contact your bank";
    public const string BankPreventedTransaction2Description = "Your bank has prevented your card from carrying out this transaction, please contact your bank";
    public const string ReferToFinancialInstitution1Description = "Refer to Financial Institution";
    public const string ReferToFinancialInstitution2Description = "Refer to Financial Institution, Special Condition";
    public const string InvalidMerchantDescription = "Invalid Merchant";
    public const string PickUpCardDescription = "Pick-up Card";
    public const string DoNotHonorDescription = "Do not honor";
    public const string ErrorDescription = "Error";
    public const string PickUpCardSpecialConditionDescription = "Pick-up Card, Special Condition";
    public const string HonorWithIdentificationDescription = "Honor with identification";
    public const string RequestInProgressDescription = "Request in Progress";
    public const string InvalidTransactionDescription = "Invalid Transaction";
    public const string NoSuchFinancialInstitutionDescription = "No Such Financial Institution";
    public const string UpdateTrack3Description = "Approved by Financial Institution, Update Track 3";
    public const string CustomerCancellationDescription = "Customer Cancellation";
    public const string CustomerDisputeDescription = "Customer Dispute";
    public const string ReenterTransactionDescription = "Re-enter Transaction";
    public const string InvalidResponseDescription = "Invalid Response from Financial Institution";
    public const string NoActionTakenDescription = "No Action Taken by Financial Institution";
    public const string SuspectedMalfunctionDescription = "Suspected Malfunction";
    public const string UnacceptableTransactionFeeDescription = "Unacceptable Transaction Fee";
    public const string FileUpdateNotSupportedDescription = "File Update not Supported";
    public const string DuplicateRecordDescription = "Duplicate Record";
    public const string FileUpdateEditErrorDescription = "File Update File Edit Error";
    public const string FileUpdateLockedDescription = "File Update File Locked";
    public const string FileUpdateFailedDescription = "File Update Failed";
    public const string FormatErrorDescription = "Format Error";
    public const string BankNotSupportedDescription = "Bank Not Supported";
    public const string CompletedPartiallyDescription = "Completed Partially by Financial";
    public const string InstitutionExpiredCardPickUpDescription = "Institution Expired Card, Pick-Up";
    public const string SuspectedFraudPickUpDescription = "Suspected Fraud, Pick-Up";
    public const string ContactAcquirerPickUpDescription = "Contact Acquirer, Pick-Up";
    public const string RestrictedCardPickUpDescription = "Restricted Card, Pick-Up";
    public const string CallAcquirerSecurityPickUpDescription = "Call Acquirer Security, Pick-Up";
    public const string PINTriesExceededPickUpDescription = "PIN Tries Exceeded, Pick-Up";
    public const string NoCreditAccountDescription = "No Credit Account";
    public const string FunctionNotSupportedDescription = "Function not supported";
    public const string LostCardPickUpDescription = "Lost Card, Pick-Up";
    public const string NoUniversalAccountDescription = "No Universal Account";
    public const string NoInvestmentAccountDescription = "No Investment Account";
    public const string InsufficientFundsDescription = "Insufficient Funds";
    public const string NoCheckAccountDescription = "No Check Account";
    public const string NoSavingsAccountDescription = "No Savings Account";
    public const string ExpiredCardDescription = "Expired Card";
    public const string IncorrectPINDescription = "Incorrect PIN";
    public const string NoCardRecordDescription = "No Card Record";
    public const string SuspectedFraudDescription = "Suspected Fraud";
    public const string ContactAcquirerDescription = "Contact Acquirer";
    public const string RestrictedCardDescription = "Restricted Card";
    public const string SecurityViolationDescription = "Security Violation";
    public const string OriginalAmountIncorrectDescription = "Original Amount Incorrect";
    public const string ExceedsWithdrawalFrequencyDescription = "Exceeds withdrawal frequency";
    public const string HardCaptureDescription = "Hard Capture";
    public const string ResponseReceivedTooLateDescription = "Response Received Too Late";
    public const string PINtriesExceededDescription = "PIN tries exceeded";
    public const string InterveneBankApprovalRequired1Description = "Intervene, Bank Approval Required";
    public const string InterveneBankApprovalRequired2Description = "Intervene, Bank Approval Required for Partial Amount";
    public const string CutoffInProgressDescription = "Cut-off in Progress";
    public const string IssuerSwitchInoperativeDescription = "Issuer or Switch Inoperative";
    public const string RoutingErrorDescription = "Routing Error";
    public const string ViolationOfLawDescription = "Violation of law";
    public const string DuplicateTransactionDescription = "Duplicate Transaction";
    public const string ReconcileErrorDescription = "Reconcile Error";
    public const string SystemMalfunctionDescription = "System Malfunction";
    public const string ExceedsCashLimitDescription = "Exceeds Cash Limit";
    public const string UnexpectedErrorDescription = "Unexpected error";
    public const string TransactionNotPermittedToCardHolder1Description = "Transaction not permitted to card holder, via channels";
    public const string TransactionNotPermittedToCardHolder2Description = "Transaction not permitted to card holder, via channels";
    public const string TransactionErrorDescription = "Transaction Error";
    public const string BankAccountErrorDescription = "Bank account error";
    public const string BankCollectionsAccountErrorDescription = "Bank collections account error";
    public const string InterfaceIntegrationErrorDescription = "Interface Integration Error";
    public const string DuplicateReferenceErrorDescription = "Duplicate Reference Error";
    public const string IncompleteTransactionDescription = "Incomplete Transaction";
    public const string TransactionSplitPreprocessingErrorDescription = "Transaction Split Pre-processing Error";
    public const string InvalidCardNumberChannelsDescription = "Invalid Card Number, via channels";
    public const string TransactionNotPermittedChannelsDescription = "Transaction not permitted to card holder, via channels";
    public const string TransactionNotFoundDescription = "Transaction not found";
    public const string PaymentRequiresTokenDescription = "Payment Requires Token";
    public const string RequestToGenerateTokenSuccessfulDescription = "Request to Generate Token is Successful";
    public const string TokenNotGeneratedCustomerNotRegisteredDescription = "Token Not Generated. Customer Not Registered on Token Platform";
    public const string ErrorCouldNotGenerateTokenDescription = "Error Occurred. Could Not Generate Token";
    public const string PaymentRequiresTokenAuthorizationDescription = "Payment Requires Token Authorization";
    public const string TokenAuthorizationSuccessfulDescription = "Token Authorization Successful";
    public const string TokenAuthorizationNotSuccessfulDescription = "Token Authorization Not Successful. Incorrect Token Supplied";
    public const string ErrorAuthenticateTokenDescription = "Error Occurred. Could Not Authenticate Token";
    public const string CustomerCancellationSecure3DDescription = "Customer Cancellation Secure3D";
    public const string CardinalAuthenticationRequiredDescription = "Cardinal Authentication Required";
    public const string CardinalLookupSuccessfulDescription = "Cardinal Lookup Successful";
    public const string CardinalLookupFailedDescription = "Cardinal Lookup Failed";
    public const string CardinalAuthenticateSuccessfulDescription = "Cardinal Authenticate Successful";
    public const string CardinalAuthenticateFailedDescription = "Cardinal Authenticate Failed";
    public const string ErrorCallingCybersourceServiceDescription = "Error calling Cybersource Service";
    public const string BinNotConfiguredDescription = "Bin has not been configured";
    public const string MerchantNotConfiguredForBinDescription = "Merchant not configured for bin";
    public const string PendingTransactionStatusUnconfirmedDescription = "Pending Transaction Status Unconfirmed";
    public const string PendingDescription = "Pending";

    public static PaymentStatus GetPaymentStatus(string code)
    {
        var status = code switch
        {
            Approved or ApprovedVIP or ApprovedPartial => PaymentStatus.completed,
            Pending or PendingTransactionStatusUnconfirmed or InProgress => PaymentStatus.pending,
            _ => PaymentStatus.failed
        };

        return status;
    }
}
