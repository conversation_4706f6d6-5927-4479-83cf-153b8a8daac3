// using System.ComponentModel.DataAnnotations;
// using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.Serialization;
//
namespace Musewears.Shared.Models;

//
//     public enum OrderStatus
//     {
//         [EnumMember(Value = "incomplete")]
//         incomplete,
//
//         [EnumMember(Value = "pending")]
//         pending,
//
//         [EnumMember(Value = "confirmed")]
//         confirmed,
//
//         [EnumMember(Value = "processing")]
//         processing,
//
//         [EnumMember(Value = "shipped")]
//         shipped,
//
//         [EnumMember(Value = "completed")]
//         completed,
//
//         [EnumMember(Value = "cancelled")]
//         cancelled
//     }
//
//     public class Order
//     {
//         [Required]
//         public required string Id { get; set; }
//
//         [Required]
//         public required string UserId { get; set; } // Assuming the user is identified by a string id
//
//         [Required]
//         public required DateTime CreatedAt { get; set; }
//
//         public DateTime UpdatedAt { get; set; }
//
//         [Required]
//         public required List<OrderItem> OrderItems { get; set; } = [];
//
//         // Enum stored as string
//         [Required]
//         [EnumDataType(typeof(PaymentStatus))]
//         [Column(TypeName = "varchar(24)")]
//         public required string PaymentStatus { get; set; }
//
//         // Enum stored as string
//         [Required]
//         // [EnumDataType(typeof(OrderStatus))]
//         [Column(TypeName = "varchar(24)")]
//         public required string Status { get; set; }
//
//         [Required]
//         public required string BillingAddressId { get; set; }
//
//         [Required]
//         public required string ShippingAddressId { get; set; }
//
//         [Required]
//         // Navigation property for ShippingAddress
//         [ForeignKey("ShippingAddressId")]
//
//         public virtual ShippingAddress ShippingAddress { get; set; }
//
//         [Required]
//         // Navigation property for BillingAddress
//         [ForeignKey("BillingAddressId")]
//         public virtual BillingAddress BillingAddress { get; set; }
//
//
//         [Required]
//         public required string PaymentReference { get; set; }
//
//         // Navigation property for Payment - only available in Server/Ordering context
//         // This property is excluded from Client builds via conditional compilation
// #if !CLIENT
//         [ForeignKey("PaymentReference")]
//         public virtual object? Payment { get; set; }
// #endif
//
//         public decimal TotalAmount
//         {
//             get
//             {
//                 //decimal total = 0m;
//                 //foreach (var item in OrderItems)
//                 //{
//                 //    total += item.Quantity * item.UnitPrice;
//                 //}
//                 return OrderItems?.Sum((item) => item.Quantity * item.Price) ?? 0m;
//             }
//         }
//     }
