using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Musewears.Shared.Models;

namespace Musewears.Shared;

public class Rating
{

    [Key]
    [Column(Order = 1)]
    public string Id { get; set; } // Primary key for the rating entry

    [ForeignKey("Wear")]
    public string WearId { get; set; }
    public virtual Wear Wear { get; set; }

    [Key]
    [Column(Order = 2)]
    [ForeignKey("AspNetUser")]
    public string UserId { get; set; }

    // Assuming you have an AspNetUser class
    // public virtual ApplicationUser User { get; set; }

    [Range(0, 5, ErrorMessage = "The value of Stars must be between 0 and 5.")]
    public float Stars { get; set; }
}