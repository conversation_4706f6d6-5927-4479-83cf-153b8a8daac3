using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace Musewears.Shared.Models
{
    public class PostalCodeValidation : ValidationAttribute
    {

        protected override ValidationResult IsValid(object? value, ValidationContext validationContext)
        {
            var postalCode = value as string;

            // Define the regex pattern for U.S. zip codes
            // var usRegex = new Regex(@"^\d{5}(-\d{4})?$");

            // Define the regex pattern for Nigerian postal codes (6 digits)
            // var regex = new Regex(@"^\d{6}$");

            int code = 0;

            // Add your postal code validation logic here
            bool isValid = !string.IsNullOrEmpty(postalCode) && value is string && int.TryParse(postalCode, out code); // Replace with actual validation

            if (!isValid || code <= 0)
            {
                return new ValidationResult("Invalid postal code.");
            }

            return ValidationResult.Success!;
        }
    }
}