using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Newtonsoft.Json;

namespace Musewears.Shared.Models;

public class TransactionEvent
{
    [JsonProperty("event")]
    public string? Event { get; set; }

    [JsonProperty("uuid")]
    public string? Uuid { get; set; }

    [JsonProperty("timestamp")]
    public long Timestamp { get; set; }

    [JsonProperty("data")]
    public TransactionEventData? Data { get; set; }
}

// TransactionEventData is now in a separate file: TransactionEventData.cs
