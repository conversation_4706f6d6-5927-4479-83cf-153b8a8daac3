using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Newtonsoft.Json;

namespace Musewears.Shared.Models;

public class TransactionEventData
{
    [Key]
    [JsonProperty("paymentReference")]
    public string? PaymentReference { get; set; }

    [JsonProperty("paymentId")]
    public int PaymentId { get; set; }

    [JsonProperty("remittanceAmount")]
    public decimal RemittanceAmount { get; set; }

    public required string? UserId { get; set; }

    public string? OrderId { get; set; }

    [JsonProperty("amount")]
    public decimal Amount { get; set; }

    [JsonProperty("responseCode")]
    public string? ResponseCode { get; set; }

    [JsonProperty("responseDescription")]
    public string? ResponseDescription { get; set; }

    [JsonProperty("cardNumber")]
    public string? CardNumber { get; set; }

    [JsonProperty("merchantReference")]
    public string? MerchantReference { get; set; }

    [JsonProperty("retrievalReferenceNumber")]
    public string? RetrievalReferenceNumber { get; set; }

    [NotMapped]
    [JsonProperty("splitAccounts")]
    public List<string>? SplitAccounts { get; set; } // Assuming splitAccounts is a list of objects

    [JsonProperty("transactionDate")]
    public long TransactionDate { get; set; }

    [JsonProperty("accountNumber")]
    public string? AccountNumber { get; set; }

    [JsonProperty("bankCode")]
    public string? BankCode { get; set; }

    [JsonProperty("token")]
    public string? Token { get; set; }

    [JsonProperty("currencyCode")]
    public string? CurrencyCode { get; set; }

    [JsonProperty("channel")]
    public string? Channel { get; set; }

    [JsonProperty("merchantCustomerId")]
    public string? MerchantCustomerId { get; set; }

    [JsonProperty("merchantCustomerName")]
    public string? MerchantCustomerName { get; set; }

    [JsonProperty("escrow")]
    public bool Escrow { get; set; }

    [JsonProperty("nonCardProviderId")]
    public string? NonCardProviderId { get; set; }

    [JsonProperty("payableCode")]
    public string? PayableCode { get; set; }

    [Required]
    public required DateTime CreatedAt { get; set; }

    public DateTime UpdatedAt { get; set; }
}
