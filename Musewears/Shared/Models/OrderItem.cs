using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.Serialization;

namespace Musewears.Shared.Models
{
    public enum ShippingStatus
    {
        [EnumMember(Value = "pending")]
        pending,

        [EnumMember(Value = "shipped")]
        shipped,

        [EnumMember(Value = "delivered")]
        delivered,

        [EnumMember(Value = "cancelled")]
        cancelled,

        [EnumMember(Value = "returned")]
        returned
    }

    public class OrderItem
    {
        [Required]
        public required int Id { get; set; }

        [Required]
        public required string UserId { get; set; }

        [Required]
        public required string OrderId { get; set; }

        [Required]
        public required string WearId { get; set; }

        [Required]
        public required DateTime CreatedAt { get; set; }

        public DateTime UpdatedAt { get; set; }

        [Required]
        public required int Quantity { get; set; }

        [Required]
        public required decimal Price { get; set; }

        // Enum stored as string
        [Required]
        // [EnumDataType(typeof(ShippingStatus))]
        [Column(TypeName = "varchar(24)")]
        public required string ShippingStatus { get; set; }
    }
}