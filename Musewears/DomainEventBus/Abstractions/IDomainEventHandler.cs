using MediatR;

namespace DomainEventBus.Abstractions;

/// <summary>
/// Domain event handler interface that extends MediatR's INotificationHandler
/// </summary>
/// <typeparam name="TDomainEvent">The domain event type</typeparam>
public interface IDomainEventHandler<in TDomainEvent> : INotificationHandler<TDomainEvent>
    where TDomainEvent : INotification
{
    // Inherits Handle method from INotificationHandler<TDomainEvent>
}

/// <summary>
/// Non-generic domain event handler interface
/// </summary>
public interface IDomainEventHandler
{
    Task Handle(INotification domainEvent, CancellationToken cancellationToken = default);
}
