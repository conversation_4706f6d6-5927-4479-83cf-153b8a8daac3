using DomainEventBus.Abstractions;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace DomainEventBus.Extensions;

public static class DomainEventBusExtensions
{
    /// <summary>
    /// Adds the in-memory domain event bus to the service collection
    /// </summary>
    public static IServiceCollection AddDomainEventBus(this IServiceCollection services)
    {
        services.AddScoped<IDomainEventBus, InMemoryDomainEventBus>();
        return services;
    }

    /// <summary>
    /// Adds the in-memory domain event bus to the host application builder
    /// </summary>
    public static IHostApplicationBuilder AddDomainEventBus(this IHostApplicationBuilder builder)
    {
        builder.Services.AddDomainEventBus();
        return builder;
    }

    /// <summary>
    /// Registers a domain event handler
    /// </summary>
    public static IServiceCollection AddDomainEventHandler<TEvent, THandler>(this IServiceCollection services)
        where TEvent : class, INotification
        where THandler : class, INotificationHandler<TEvent>, IDomainEventHandler<TEvent>
    {
        services.AddScoped<INotificationHandler<TEvent>, THandler>();
        services.AddScoped<IDomainEventHandler<TEvent>, THandler>();
        return services;
    }

    /// <summary>
    /// Registers multiple domain event handlers from an assembly
    /// </summary>
    public static IServiceCollection AddDomainEventHandlers(this IServiceCollection services, params Type[] assemblyMarkerTypes)
    {
        foreach (var markerType in assemblyMarkerTypes)
        {
            services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(markerType.Assembly));
        }
        return services;
    }
}
