<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="MediatR" Version="12.4.1" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5" />
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.5" />
        <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.5" />
        <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.5" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\EventBus\EventBus.csproj" />
        <ProjectReference Include="..\DomainEventLogEF\DomainEventLogEF.csproj" />
    </ItemGroup>

</Project>
