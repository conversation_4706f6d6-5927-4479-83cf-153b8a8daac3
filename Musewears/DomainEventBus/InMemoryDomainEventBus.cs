using DomainEventBus.Abstractions;
using DomainEventLogEF.Services;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace DomainEventBus;

/// <summary>
/// In-memory domain event bus implementation for monolithic architecture
/// </summary>
public class InMemoryDomainEventBus : IDomainEventBus
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<InMemoryDomainEventBus> _logger;
    private readonly IDomainEventLogService? _eventLogService;

    public InMemoryDomainEventBus(
        IServiceProvider serviceProvider,
        ILogger<InMemoryDomainEventBus> logger,
        IDomainEventLogService? eventLogService = null)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _eventLogService = eventLogService;
    }

    public async Task PublishAsync(INotification domainEvent, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Publishing domain event: {EventType}", domainEvent.GetType().Name);

        try
        {
            // Use MediatR to publish the domain event to all registered handlers
            using var scope = _serviceProvider.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
            
            await mediator.Publish(domainEvent, cancellationToken);
            
            _logger.LogInformation("Successfully published domain event: {EventType}", domainEvent.GetType().Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing domain event: {EventType}", domainEvent.GetType().Name);
            throw;
        }
    }

    public async Task PublishAsync<T>(T domainEvent, CancellationToken cancellationToken = default) where T : INotification
    {
        await PublishAsync((INotification)domainEvent, cancellationToken);
    }
}
