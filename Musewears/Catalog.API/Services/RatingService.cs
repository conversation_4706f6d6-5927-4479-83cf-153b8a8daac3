namespace Catalog.API.Services;

public class RatingService : IRatingService
{
    private readonly CatalogContext _context;
    private readonly ILogger<RatingService> _logger;

    public RatingService(CatalogContext context, ILogger<RatingService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<Rating> AddOrUpdateRatingAsync(int catalogItemId, string userId, decimal ratingValue, string? reviewText = null)
    {
        if (ratingValue < 0 || ratingValue > 5)
        {
            throw new ArgumentException("Rating value must be between 0 and 5", nameof(ratingValue));
        }

        if (string.IsNullOrWhiteSpace(userId))
        {
            throw new ArgumentException("User ID cannot be null or empty", nameof(userId));
        }

        // Check if catalog item exists
        var catalogItemExists = await _context.CatalogItems
            .AnyAsync(c => c.Id == catalogItemId);
            
        if (!catalogItemExists)
        {
            throw new ArgumentException($"Catalog item with ID {catalogItemId} does not exist", nameof(catalogItemId));
        }

        // Check if user already has a rating for this item
        var existingRating = await _context.Ratings
            .FirstOrDefaultAsync(r => r.CatalogItemId == catalogItemId && r.UserId == userId);

        if (existingRating != null)
        {
            // Update existing rating
            existingRating.RatingValue = ratingValue;
            existingRating.ReviewText = reviewText;
            existingRating.UpdatedAt = DateTime.UtcNow;
            
            _logger.LogInformation("Updated rating for user {UserId} on item {CatalogItemId}: {RatingValue}", 
                userId, catalogItemId, ratingValue);
        }
        else
        {
            // Create new rating
            existingRating = new Rating
            {
                CatalogItemId = catalogItemId,
                UserId = userId,
                RatingValue = ratingValue,
                ReviewText = reviewText,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
            
            _context.Ratings.Add(existingRating);
            
            _logger.LogInformation("Added new rating for user {UserId} on item {CatalogItemId}: {RatingValue}", 
                userId, catalogItemId, ratingValue);
        }

        await _context.SaveChangesAsync();
        
        // The trigger will automatically update the CatalogItem.Rating
        return existingRating;
    }

    public async Task<bool> DeleteRatingAsync(int catalogItemId, string userId)
    {
        var rating = await _context.Ratings
            .FirstOrDefaultAsync(r => r.CatalogItemId == catalogItemId && r.UserId == userId);

        if (rating == null)
        {
            return false;
        }

        _context.Ratings.Remove(rating);
        await _context.SaveChangesAsync();
        
        _logger.LogInformation("Deleted rating for user {UserId} on item {CatalogItemId}", userId, catalogItemId);
        
        // The trigger will automatically update the CatalogItem.Rating
        return true;
    }

    public async Task<Rating?> GetUserRatingAsync(int catalogItemId, string userId)
    {
        return await _context.Ratings
            .FirstOrDefaultAsync(r => r.CatalogItemId == catalogItemId && r.UserId == userId);
    }

    public async Task<IEnumerable<Rating>> GetItemRatingsAsync(int catalogItemId, int page = 1, int pageSize = 10)
    {
        return await _context.Ratings
            .Where(r => r.CatalogItemId == catalogItemId)
            .OrderByDescending(r => r.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    public async Task<decimal?> GetAverageRatingAsync(int catalogItemId)
    {
        var catalogItem = await _context.CatalogItems
            .AsNoTracking()
            .FirstOrDefaultAsync(c => c.Id == catalogItemId);
            
        return catalogItem?.Rating;
    }

    public async Task<(decimal Average, int Count)> GetRatingStatsAsync(int catalogItemId)
    {
        var ratings = await _context.Ratings
            .Where(r => r.CatalogItemId == catalogItemId)
            .Select(r => r.RatingValue)
            .ToListAsync();

        if (!ratings.Any())
        {
            return (0, 0);
        }

        return (ratings.Average(), ratings.Count);
    }
}