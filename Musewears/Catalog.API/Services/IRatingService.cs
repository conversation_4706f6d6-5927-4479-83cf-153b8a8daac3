namespace Catalog.API.Services;

public interface IRatingService
{
    Task<Rating> AddOrUpdateRatingAsync(int catalogItemId, string userId, decimal ratingValue, string? reviewText = null);
    Task<bool> DeleteRatingAsync(int catalogItemId, string userId);
    Task<Rating?> GetUserRatingAsync(int catalogItemId, string userId);
    Task<IEnumerable<Rating>> GetItemRatingsAsync(int catalogItemId, int page = 1, int pageSize = 10);
    Task<decimal?> GetAverageRatingAsync(int catalogItemId);
    Task<(decimal Average, int Count)> GetRatingStatsAsync(int catalogItemId);
}