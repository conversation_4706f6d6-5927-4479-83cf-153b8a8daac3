using System.Diagnostics;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.AI;
using Pgvector;

namespace Catalog.API.Services;

public sealed class CatalogAi(
    IWebHostEnvironment environment,
    ILogger<CatalogAi> logger,
    IEmbeddingGenerator<string, Embedding<float>>? embeddingGenerator = null)
    : ICatalogAi
{
    private const int EmbeddingDimensions = 384;

    /// <summary>The web host environment.</summary>
    private readonly IWebHostEnvironment _environment = environment;
    /// <summary>Logger for use in AI operations.</summary>
    private readonly ILogger _logger = logger;

    /// <inheritdoc/>
    public bool IsEnabled => embeddingGenerator is not null;

    /// <inheritdoc/>
    public ValueTask<Vector?> GetEmbeddingAsync(CatalogItem item) =>
        IsEnabled ?
            GetEmbeddingAsync(CatalogItemToString(item)) :
            ValueTask.FromResult<Vector?>(null);

    /// <inheritdoc/>
    public async ValueTask<IReadOnlyList<Vector>?> GetEmbeddingsAsync(IEnumerable<CatalogItem> items)
    {
        if (!IsEnabled) return null;
        
        var timestamp = Stopwatch.GetTimestamp();

        if (embeddingGenerator != null)
        {
            var embeddings = await embeddingGenerator.GenerateAsync(items.Select(CatalogItemToString));
            var results = embeddings.Select(m => new Vector(m.Vector[0..EmbeddingDimensions])).ToList();

            if (_logger.IsEnabled(LogLevel.Trace))
            {
                _logger.LogTrace("Generated {EmbeddingsCount} embeddings in {ElapsedMilliseconds}s", results.Count, Stopwatch.GetElapsedTime(timestamp).TotalSeconds);
            }

            return results;
        }
        
        return null;
    }

    /// <inheritdoc/>
    public async ValueTask<Vector?> GetEmbeddingAsync(string text)
    {
        if (!IsEnabled) return null;
        var timestamp = Stopwatch.GetTimestamp();

        if (embeddingGenerator == null) return null;    
        
        var embedding = await embeddingGenerator.GenerateVectorAsync(text);
        embedding = embedding[0..EmbeddingDimensions];

        if (_logger.IsEnabled(LogLevel.Trace))
        {
            _logger.LogTrace("Generated embedding in {ElapsedMilliseconds}s: '{Text}'", Stopwatch.GetElapsedTime(timestamp).TotalSeconds, text);
        }

        return new Vector(embedding);

    }

    private static string CatalogItemToString(CatalogItem item) => $"{item.Name} {item.Description}";
}
