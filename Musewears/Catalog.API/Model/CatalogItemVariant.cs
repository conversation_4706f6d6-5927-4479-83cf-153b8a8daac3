using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Catalog.API.Infrastructure.Exceptions;

namespace Catalog.API.Model;

public class CatalogItemVariant
{
    public int Id { get; set; }

    [Required]
    public required int CatalogItemId { get; set; }
    
    [JsonIgnore]
    public CatalogItem? CatalogItem { get; set; }

    public int? CatalogItemColorId { get; set; }
    public CatalogItemColor? CatalogItemColor { get; set; }

    public int? CatalogItemSizeId { get; set; }
    public CatalogItemSize? CatalogItemSize { get; set; }

    public decimal Price { get; set; }

    public string SKU { get; set; }

    public int AvailableStock { get; set; }

    public int RestockThreshold { get; set; }

    public int MaxStockThreshold { get; set; }

    public bool OnReorder { get; set; }
    
    public List<CatalogItemImage> Images { get; set; } = new();

    // Methods for managing stock
    public int RemoveStock(int quantityDesired)
    {
        if (AvailableStock == 0)
        {
            throw new CatalogDomainException($"Empty stock, product variant {SKU} is sold out");
        }

        if (quantityDesired <= 0)
        {
            throw new CatalogDomainException($"Item units desired should be greater than zero");
        }

        var removed = Math.Min(quantityDesired, this.AvailableStock);
        AvailableStock -= removed;
        return removed;
    }

    public int AddStock(int quantity)
    {
        int original = this.AvailableStock;

        if ((this.AvailableStock + quantity) > this.MaxStockThreshold)
        {
            this.AvailableStock = this.MaxStockThreshold;
        }
        else
        {
            this.AvailableStock += quantity;
        }

        this.OnReorder = false;
        return this.AvailableStock - original;
    }
}