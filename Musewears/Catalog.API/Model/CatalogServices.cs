using Catalog.API.Services;
using Microsoft.AspNetCore.Mvc;

namespace Catalog.API.Model;

public class CatalogServices(
    CatalogContext context,
    [FromServices] ICatalogAi catalogAi,
    IOptions<CatalogOptions> options,
    ILogger<CatalogServices> logger
    // [FromServices] ICatalogIntegrationEventService eventService
    )
{
    public CatalogContext Context { get; } = context;
    public ICatalogAi CatalogAi { get; } = catalogAi;
    
    public IOptions<CatalogOptions> Options { get; } = options;
    public ILogger<CatalogServices> Logger { get; } = logger;
    
    // public ICatalogIntegrationEventService EventService { get; } = eventService;
};