using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Catalog.API.Model;

public class CatalogItemImage
{
    public int Id { get; set; }
    
    [Required]
    public int CatalogItemId { get; set; }          // For product-level images
    
    [JsonIgnore]
    public CatalogItem? CatalogItem { get; set; }
    
    public int? CatalogItemColorId { get; set; } // For color-specific images
    
    public CatalogItemColor? CatalogItemColor { get; set; }
    
    // Optional: Associate image with specific size too
    public int? CatalogItemSizeId { get; set; }
    
    public CatalogItemSize? CatalogItemSize { get; set; }
    
    public int? CatalogItemVariantId { get; set; }    // For variant-specific images
    public CatalogItemVariant? CatalogItemVariant { get; set; }
    
    [Required]
    public required string ImageUrl { get; set; }
    
    // Order for displaying images
    [Required]
    public required int DisplayOrder { get; set; }
    
    // Is this the primary image for this color/size combination?
    public bool IsPrimary { get; set; } = false;
}