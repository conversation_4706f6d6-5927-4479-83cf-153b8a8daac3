using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Catalog.API.Model;

public class CatalogItemColor
{
    public int Id { get; set; }

    [Required]
    public int CatalogItemId { get; set; }
    
    [JsonIgnore]
    public CatalogItem? CatalogItem { get; set; }

    [Required]
    public required string ColorName { get; set; } // e.g., "Black", "White", "Blue"
    
    [Required]
    public required string ColorCode { get; set; }

    // Navigation property for images
    // public List<CatalogItemImage>? Images { get; set; }
}