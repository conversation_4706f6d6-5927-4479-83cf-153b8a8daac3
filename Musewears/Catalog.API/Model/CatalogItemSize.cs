using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Catalog.API.Model;

public class CatalogItemSize
{
    public int Id { get; set; }
    
    [Required]
    public int CatalogItemId { get; set; }
    
    [JsonIgnore]
    public CatalogItem? CatalogItem { get; set; }
    
    [Required]
    public required string SizeName { get; set; } // e.g., "Small", "Medium", "Large", "Extra Large"
    
    // ADD THIS: Price modifier for this size (can be positive, negative, or zero)
    // public decimal PriceModifier { get; set; } = 0;
}