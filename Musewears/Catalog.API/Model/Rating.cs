using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Catalog.API.Model;

// Rating entity for individual user ratings
public class Rating
{
    public int Id { get; set; }
    
    [Required]
    public int CatalogItemId { get; set; }
    
    [Required]
    public string UserId { get; set; }
    
    [Required]
    [Range(0, 5, ErrorMessage = "Rating value must be between 0 and 5")]
    public decimal RatingValue { get; set; }
    
    [MaxLength(1000)]
    public string? ReviewText { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    // Navigation property
    [JsonIgnore]
    public CatalogItem? CatalogItem { get; set; }
}