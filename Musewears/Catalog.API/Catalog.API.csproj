<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Asp.Versioning.Http" Version="8.1.0" />
        <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.6" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
        <PackageReference Include="CommunityToolkit.Aspire.OllamaSharp" Version="9.5.1-beta.305" />
        <PackageReference Include="EntityFrameworkCore.Triggered" Version="3.2.2" />
        <PackageReference Include="EntityFrameworkCore.Triggered.Extensions" Version="3.2.2" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
        <PackageReference Include="Aspire.Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.3.1" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

    <!-- AI -->
    <ItemGroup>
        <!--        <PackageReference Include="Aspire.Azure.AI.OpenAI" Version="9.3.0-preview.1.25265.20" />-->
        <PackageReference Include="Microsoft.Extensions.AI.Ollama" Version="9.5.0-preview.1.25265.7" />
        <!--        <PackageReference Include="Microsoft.Extensions.AI" />-->
        <!--        <PackageReference Include="Microsoft.Extensions.AI.OpenAI" />-->
        <PackageReference Include="Pgvector" Version="0.3.2" />
        <PackageReference Include="Pgvector.EntityFrameworkCore" Version="0.2.2" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Infrastructure\BackgroundTasks\" />
      <Folder Include="IntegrationEvents\EventHandling\" />
      <Folder Include="IntegrationEvents\Events\" />
      <Folder Include="Migrations\" />
    </ItemGroup>

    <ItemGroup>
      <None Update="Properties\launchSettings.json">
        <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        <CopyToPublishDirectory>Never</CopyToPublishDirectory>
        <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      </None>
    </ItemGroup>



    <ItemGroup>
        <Compile Include="..\Shared\MigrateDbContextExtensions.cs" Link="Extensions\MigrateDbContextExtensions.cs" />
        <Compile Include="..\Shared\ActivityExtensions.cs" Link="Extensions\ActivityExtensions.cs" />
        <Compile Remove="IntegrationEvents\EventHandling\OrderStatusChangedToAwaitingValidationIntegrationEventHandler.cs" />
        <Compile Remove="IntegrationEvents\Events\OrderStatusChangedToPaidIntegrationEvent.cs" />
        <Compile Remove="IntegrationEvents\Events\ConfirmedOrderStockItem.cs" />
        <Compile Remove="IntegrationEvents\Events\OrderStockConfirmedIntegrationEvent.cs" />
        <Compile Remove="IntegrationEvents\Events\OrderStockItem.cs" />
        <Compile Remove="IntegrationEvents\Events\OrderStockRejectedIntegrationEvent.cs" />
        <Compile Remove="IntegrationEvents\ICatalogIntegrationEventService.cs" />
    </ItemGroup>


    <ItemGroup>
        <Content Include="Pics\*.webp" CopyToOutputDirectory="PreserveNewest" />
        <Content Include="Setup\catalog.json" CopyToOutputDirectory="PreserveNewest" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\DomainEventLogEF\DomainEventLogEF.csproj" />
        <ProjectReference Include="..\ServiceDefaults\ServiceDefaults.csproj" />
    </ItemGroup>

</Project>
