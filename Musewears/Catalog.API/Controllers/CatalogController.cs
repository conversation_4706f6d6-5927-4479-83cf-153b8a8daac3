using Catalog.API.Apis;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;

namespace Catalog.API.Controllers;

[ApiController]
[Route("api/admin/[controller]")]
[Authorize(Policy = "AdminOnly")]
public class CatalogController(CatalogServices catalogServices) : ControllerBase
{
    /// <summary>
    /// Converts minimal API Ok results to MVC ActionResults
    /// </summary>
    private ActionResult<T> ConvertOkResult<T>(Ok<T> result)
    {
        return Ok(result.Value);
    }

    /// <summary>
    /// Converts minimal API Results union types to MVC ActionResults
    /// </summary>
    private ActionResult ConvertResultsToActionResult(IResult result)
    {
        return result switch
        {
            Created created => Created(created.Location, null),
            Microsoft.AspNetCore.Http.HttpResults.NoContent => NoContent(),
            Microsoft.AspNetCore.Http.HttpResults.NotFound => NotFound(),
            BadRequest<ProblemDetails> badRequest => BadRequest(badRequest.Value),
            _ => throw new InvalidOperationException($"Unsupported result type: {result.GetType()}")
        };
    }

    [HttpGet("items")]
    public async Task<ActionResult<PaginatedItems<CatalogItem>>> GetItems(
        [AsParameters] PaginationRequest paginationRequest,
        [FromQuery] string? name = null,
        [FromQuery] int? type = null,
        [FromQuery] int? brand = null)
    {
        // Reuse existing CatalogApi logic
        var result = await CatalogApi.GetAllItems(paginationRequest, catalogServices, name, type, brand);
        return ConvertOkResult(result);
    }

    [HttpPost("items")]
    public async Task<ActionResult> CreateItem([FromBody] CreateCatalogItemRequest request)
    {
        var item = new CatalogItem
        {
            VendorId = request.VendorId,
            Name = request.Name,
            Description = request.Description,
            Price = request.Price,
            PictureFileName = request.PictureFileName,
            CatalogTypeId = request.CatalogTypeId,
            CatalogBrandId = request.CatalogBrandId,
            AvailableStock = request.AvailableStock,
            RestockThreshold = request.RestockThreshold,
            MaxStockThreshold = request.MaxStockThreshold
        };

        // Reuse existing CatalogApi logic
        var result = await CatalogApi.CreateItem(catalogServices, item);
        return ConvertCreatedResult(result);
    }

    /// <summary>
    /// Converts minimal API Created results to MVC ActionResults
    /// </summary>
    private ActionResult ConvertCreatedResult(Created result)
    {
        return Created(result.Location, null);
    }

    [HttpPut("items/{id}")]
    public async Task<ActionResult> UpdateItem(int id, [FromBody] CatalogItem item)
    {
        // Reuse existing CatalogApi logic
        var result = await CatalogApi.UpdateItem(HttpContext, id, catalogServices, item);
        return ConvertResultsToActionResult(result);
    }

    [HttpDelete("items/{id}")]
    public async Task<ActionResult> DeleteItem(int id)
    {
        // Reuse existing CatalogApi logic
        var result = await CatalogApi.DeleteItemById(catalogServices, id);
        return ConvertResultsToActionResult(result);
    }

    [HttpGet("analytics")]
    public async Task<ActionResult<CatalogAnalytics>> GetAnalytics()
    {
        var totalItems = await catalogServices.Context.CatalogItems.CountAsync();
        var totalBrands = await catalogServices.Context.CatalogBrands.CountAsync();
        var totalTypes = await catalogServices.Context.CatalogTypes.CountAsync();
        var lowStockItems = await catalogServices.Context.CatalogItems
            .Where(i => i.AvailableStock <= i.RestockThreshold)
            .CountAsync();

        return Ok(new CatalogAnalytics
        {
            TotalItems = totalItems,
            TotalBrands = totalBrands,
            TotalTypes = totalTypes,
            LowStockItems = lowStockItems
        });
    }
}
