using System.ComponentModel.DataAnnotations;
using Catalog.API.Services;
using Microsoft.AspNetCore.Mvc;

namespace Catalog.API.Controllers;

[ApiController]
[Route("api/[controller]/items/{catalogItemId}/ratings")]
public class RatingController(IRatingService ratingService) : ControllerBase
{
    [HttpPost]
    public async Task<ActionResult<Rating>> AddOrUpdateRating(
        int catalogItemId, 
        [FromBody] AddRatingRequest request)
    {
        try
        {
            var userId = User.Identity?.Name ?? throw new UnauthorizedAccessException("User must be authenticated");
            
            var rating = await ratingService.AddOrUpdateRatingAsync(
                catalogItemId, 
                userId, 
                request.RatingValue, 
                request.ReviewText);
                
            return Ok(rating);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpDelete]
    public async Task<IActionResult> DeleteRating(int catalogItemId)
    {
        var userId = User.Identity?.Name ?? throw new UnauthorizedAccessException("User must be authenticated");
        
        var deleted = await ratingService.DeleteRatingAsync(catalogItemId, userId);
        
        if (!deleted)
        {
            return NotFound("Rating not found");
        }
        
        return NoContent();
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<Rating>>> GetItemRatings(
        int catalogItemId, 
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 10)
    {
        var ratings = await ratingService.GetItemRatingsAsync(catalogItemId, page, pageSize);
        return Ok(ratings);
    }

    [HttpGet("stats")]
    public async Task<ActionResult<object>> GetRatingStats(int catalogItemId)
    {
        var (average, count) = await ratingService.GetRatingStatsAsync(catalogItemId);
        return Ok(new { Average = average, Count = count });
    }
}

public class AddRatingRequest
{
    [Required]
    [Range(0, 5, ErrorMessage = "Rating must be between 0 and 5")]
    public decimal RatingValue { get; set; }
    
    [MaxLength(1000)]
    public string? ReviewText { get; set; }
}