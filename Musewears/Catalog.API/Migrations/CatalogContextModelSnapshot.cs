// <auto-generated />
using System;
using Catalog.API.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using Pgvector;

#nullable disable

namespace Catalog.API.Migrations
{
    [DbContext(typeof(CatalogContext))]
    partial class CatalogContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "vector");
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Catalog.API.Model.CatalogBrand", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Brand")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.ToTable("CatalogBrand", (string)null);
                });

            modelBuilder.Entity("Catalog.API.Model.CatalogItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AvailableStock")
                        .HasColumnType("integer");

                    b.Property<int>("CatalogBrandId")
                        .HasColumnType("integer");

                    b.Property<int>("CatalogTypeId")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<Vector>("Embedding")
                        .HasColumnType("vector(384)");

                    b.Property<int>("MaxStockThreshold")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("OnReorder")
                        .HasColumnType("boolean");

                    b.Property<string>("PictureFileName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Rating")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasPrecision(3, 2)
                        .HasColumnType("decimal(3,2)")
                        .HasDefaultValue(0m)
                        .HasColumnName("rating");

                    b.Property<int>("RestockThreshold")
                        .HasColumnType("integer");

                    b.Property<int>("ReviewCount")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("review_count");

                    b.Property<string>("VendorId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.HasKey("Id");

                    b.HasIndex("CatalogBrandId");

                    b.HasIndex("CatalogTypeId");

                    b.HasIndex("Name");

                    b.HasIndex("VendorId");

                    b.ToTable("Catalog", (string)null);
                });

            modelBuilder.Entity("Catalog.API.Model.CatalogItemColor", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("CatalogItemId")
                        .HasColumnType("integer");

                    b.Property<string>("ColorCode")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("ColorName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("CatalogItemId");

                    b.ToTable("CatalogItemColor", (string)null);
                });

            modelBuilder.Entity("Catalog.API.Model.CatalogItemImage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("CatalogItemColorId")
                        .HasColumnType("integer");

                    b.Property<int>("CatalogItemId")
                        .HasColumnType("integer");

                    b.Property<int?>("CatalogItemSizeId")
                        .HasColumnType("integer");

                    b.Property<int?>("CatalogItemVariantId")
                        .HasColumnType("integer");

                    b.Property<int?>("CatalogItemVariantId1")
                        .HasColumnType("integer");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<string>("ImageUrl")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsPrimary")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("CatalogItemColorId");

                    b.HasIndex("CatalogItemId");

                    b.HasIndex("CatalogItemSizeId");

                    b.HasIndex("CatalogItemVariantId");

                    b.HasIndex("CatalogItemVariantId1");

                    b.ToTable("CatalogItemImage", (string)null);
                });

            modelBuilder.Entity("Catalog.API.Model.CatalogItemSize", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("CatalogItemId")
                        .HasColumnType("integer");

                    b.Property<string>("SizeName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("CatalogItemId");

                    b.ToTable("CatalogItemSize", (string)null);
                });

            modelBuilder.Entity("Catalog.API.Model.CatalogItemVariant", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AvailableStock")
                        .HasColumnType("integer");

                    b.Property<int?>("CatalogItemColorId")
                        .HasColumnType("integer");

                    b.Property<int>("CatalogItemId")
                        .HasColumnType("integer");

                    b.Property<int?>("CatalogItemSizeId")
                        .HasColumnType("integer");

                    b.Property<int>("MaxStockThreshold")
                        .HasColumnType("integer");

                    b.Property<bool>("OnReorder")
                        .HasColumnType("boolean");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("RestockThreshold")
                        .HasColumnType("integer");

                    b.Property<string>("SKU")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("CatalogItemColorId");

                    b.HasIndex("CatalogItemId");

                    b.HasIndex("CatalogItemSizeId");

                    b.ToTable("CatalogItemVariant", (string)null);
                });

            modelBuilder.Entity("Catalog.API.Model.CatalogType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.ToTable("CatalogType", (string)null);
                });

            modelBuilder.Entity("Catalog.API.Model.Rating", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("CatalogItemId")
                        .HasColumnType("integer")
                        .HasColumnName("catalog_item_id");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<decimal>("RatingValue")
                        .HasColumnType("decimal(2,1)")
                        .HasColumnName("rating_value");

                    b.Property<string>("ReviewText")
                        .HasMaxLength(1000)
                        .HasColumnType("text")
                        .HasColumnName("review_text");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("CatalogItemId", "UserId")
                        .IsUnique()
                        .HasDatabaseName("uk_ratings_user_item");

                    b.ToTable("ratings", (string)null);
                });

            modelBuilder.Entity("DomainEventLogEF.DomainEventLogEntry", b =>
                {
                    b.Property<Guid>("EventId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("EventTypeName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("State")
                        .HasColumnType("integer");

                    b.Property<int>("TimesSent")
                        .HasColumnType("integer");

                    b.Property<Guid>("TransactionId")
                        .HasColumnType("uuid");

                    b.HasKey("EventId");

                    b.HasIndex("CreationTime");

                    b.HasIndex("State");

                    b.HasIndex("TransactionId");

                    b.ToTable("DomainEventLog", (string)null);
                });

            modelBuilder.Entity("Catalog.API.Model.CatalogItem", b =>
                {
                    b.HasOne("Catalog.API.Model.CatalogBrand", "CatalogBrand")
                        .WithMany()
                        .HasForeignKey("CatalogBrandId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Catalog.API.Model.CatalogType", "CatalogType")
                        .WithMany()
                        .HasForeignKey("CatalogTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CatalogBrand");

                    b.Navigation("CatalogType");
                });

            modelBuilder.Entity("Catalog.API.Model.CatalogItemColor", b =>
                {
                    b.HasOne("Catalog.API.Model.CatalogItem", "CatalogItem")
                        .WithMany("Colors")
                        .HasForeignKey("CatalogItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CatalogItem");
                });

            modelBuilder.Entity("Catalog.API.Model.CatalogItemImage", b =>
                {
                    b.HasOne("Catalog.API.Model.CatalogItemColor", "CatalogItemColor")
                        .WithMany()
                        .HasForeignKey("CatalogItemColorId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Catalog.API.Model.CatalogItem", "CatalogItem")
                        .WithMany("Images")
                        .HasForeignKey("CatalogItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Catalog.API.Model.CatalogItemSize", "CatalogItemSize")
                        .WithMany()
                        .HasForeignKey("CatalogItemSizeId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Catalog.API.Model.CatalogItemVariant", "CatalogItemVariant")
                        .WithMany()
                        .HasForeignKey("CatalogItemVariantId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Catalog.API.Model.CatalogItemVariant", null)
                        .WithMany("Images")
                        .HasForeignKey("CatalogItemVariantId1");

                    b.Navigation("CatalogItem");

                    b.Navigation("CatalogItemColor");

                    b.Navigation("CatalogItemSize");

                    b.Navigation("CatalogItemVariant");
                });

            modelBuilder.Entity("Catalog.API.Model.CatalogItemSize", b =>
                {
                    b.HasOne("Catalog.API.Model.CatalogItem", "CatalogItem")
                        .WithMany("Sizes")
                        .HasForeignKey("CatalogItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CatalogItem");
                });

            modelBuilder.Entity("Catalog.API.Model.CatalogItemVariant", b =>
                {
                    b.HasOne("Catalog.API.Model.CatalogItemColor", "CatalogItemColor")
                        .WithMany()
                        .HasForeignKey("CatalogItemColorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Catalog.API.Model.CatalogItem", "CatalogItem")
                        .WithMany("Variants")
                        .HasForeignKey("CatalogItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Catalog.API.Model.CatalogItemSize", "CatalogItemSize")
                        .WithMany()
                        .HasForeignKey("CatalogItemSizeId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CatalogItem");

                    b.Navigation("CatalogItemColor");

                    b.Navigation("CatalogItemSize");
                });

            modelBuilder.Entity("Catalog.API.Model.Rating", b =>
                {
                    b.HasOne("Catalog.API.Model.CatalogItem", "CatalogItem")
                        .WithMany("Ratings")
                        .HasForeignKey("CatalogItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_ratings_catalog_item");

                    b.Navigation("CatalogItem");
                });

            modelBuilder.Entity("Catalog.API.Model.CatalogItem", b =>
                {
                    b.Navigation("Colors");

                    b.Navigation("Images");

                    b.Navigation("Ratings");

                    b.Navigation("Sizes");

                    b.Navigation("Variants");
                });

            modelBuilder.Entity("Catalog.API.Model.CatalogItemVariant", b =>
                {
                    b.Navigation("Images");
                });
#pragma warning restore 612, 618
        }
    }
}
