namespace Catalog.API.Infrastructure.EntityConfigurations;

internal class CatalogItemRatingEntityTypeConfiguration
    : IEntityTypeConfiguration<Rating>
{
    public void Configure(EntityTypeBuilder<Rating> entity)
    {
        entity.ToTable("ratings");
            
        entity.HasKey(r => r.Id);
            
        entity.Property(r => r.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();
                
        entity.Property(r => r.CatalogItemId)
            .HasColumnName("catalog_item_id")
            .IsRequired();
                
        entity.Property(r => r.UserId)
            .HasColumnName("user_id")
            .HasMaxLength(255)
            .IsRequired();
                
        entity.Property(r => r.RatingValue)
            .HasColumnName("rating_value")
            .HasColumnType("decimal(2,1)")
            .IsRequired();
                
        entity.Property(r => r.ReviewText)
            .HasColumnName("review_text")
            .HasColumnType("text");
                
        entity.Property(r => r.CreatedAt)
            .HasColumnName("created_at")
            .HasColumnType("timestamp with time zone")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");
                
        entity.Property(r => r.UpdatedAt)
            .HasColumnName("updated_at")
            .HasColumnType("timestamp with time zone")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        // Unique constraint: one rating per user per item
        entity.HasIndex(r => new { r.CatalogItemId, r.UserId })
            .IsUnique()
            .HasDatabaseName("uk_ratings_user_item");
                
        // Foreign key relationship
        entity.HasOne(r => r.CatalogItem)
            .WithMany(c => c.Ratings)
            .HasForeignKey(r => r.CatalogItemId)
            .OnDelete(DeleteBehavior.Cascade)
            .HasConstraintName("fk_ratings_catalog_item");
    }
}
