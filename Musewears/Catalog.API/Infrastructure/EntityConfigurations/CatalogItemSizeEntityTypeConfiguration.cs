namespace Catalog.API.Infrastructure.EntityConfigurations;

internal class CatalogItemSizeEntityTypeConfiguration : IEntityTypeConfiguration<CatalogItemSize>
{
    public void Configure(EntityTypeBuilder<CatalogItemSize> builder)
    {
        builder.ToTable("CatalogItemSize");

        builder.HasKey(ci => ci.Id);

        builder.Property(ci => ci.SizeName)
            .HasMaxLength(50)
            .IsRequired();

        // Relationship: CatalogItemSize -> CatalogItem (many-to-one)
        builder.HasOne(ci => ci.CatalogItem)
            .WithMany(i => i.Sizes)
            .HasForeignKey(ci => ci.CatalogItemId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}