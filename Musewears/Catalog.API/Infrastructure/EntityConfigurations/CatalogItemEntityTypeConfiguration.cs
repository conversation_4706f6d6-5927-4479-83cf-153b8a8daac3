namespace Catalog.API.Infrastructure.EntityConfigurations;

internal class CatalogItemEntityTypeConfiguration : IEntityTypeConfiguration<CatalogItem>
{
    public void Configure(EntityTypeBuilder<CatalogItem> builder)
    {
        builder.ToTable("Catalog");
        builder.<PERSON>Key(ci => ci.Id);

        builder.Property(ci => ci.VendorId)
            .IsRequired()
            .HasMaxLength(450);
        builder.HasIndex(ci => ci.VendorId);

        builder.Property(ci => ci.Name)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(ci => ci.Description)
            .HasMaxLength(500);
        
        builder.Property(ci => ci.Price)
            .IsRequired()
            .HasColumnType("decimal(18,2)");

        builder.Property(ci => ci.Embedding)
            .HasColumnType("vector(384)");

        builder.HasOne(ci => ci.CatalogBrand)
            .WithMany()
            .HasForeignKey(ci => ci.CatalogBrandId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(ci => ci.CatalogType)
            .WithMany()
            .HasForeignKey(ci => ci.CatalogTypeId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(ci => ci.Colors)
            .WithOne(c => c.CatalogItem)
            .HasForeignKey(c => c.CatalogItemId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(ci => ci.Sizes)
            .WithOne(s => s.CatalogItem)
            .HasForeignKey(s => s.CatalogItemId)
            .OnDelete(DeleteBehavior.Cascade);
        
        builder.HasMany(ci => ci.Images)
            .WithOne(img => img.CatalogItem)
            .HasForeignKey(img => img.CatalogItemId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(ci => ci.Variants)
            .WithOne(v => v.CatalogItem)
            .HasForeignKey(v => v.CatalogItemId)
            .OnDelete(DeleteBehavior.Cascade);
        
        builder.Property(c => c.Rating)
            .HasColumnName("rating")
            .HasColumnType("decimal(3,2)")
            .HasPrecision(3, 2)
            .HasDefaultValue(0m) 
            .ValueGeneratedOnAddOrUpdate(); // This makes it read-only in EF
        
        // Add configuration for ReviewCount
        builder.Property(c => c.ReviewCount)
            .HasColumnName("review_count")
            .HasDefaultValue(0)
            .ValueGeneratedOnAddOrUpdate();

        builder.HasIndex(ci => ci.Name);
    }
}