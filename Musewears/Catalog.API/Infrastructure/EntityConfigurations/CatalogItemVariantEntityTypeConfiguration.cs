namespace Catalog.API.Infrastructure.EntityConfigurations;

internal class CatalogItemVariantEntityTypeConfiguration : IEntityTypeConfiguration<CatalogItemVariant>
{
    public void Configure(EntityTypeBuilder<CatalogItemVariant> builder)
    {
        builder.ToTable("CatalogItemVariant");

        builder.HasKey(v => v.Id);

        builder.Property(v => v.Price)
            .IsRequired()
            .HasColumnType("decimal(18,2)");

        builder.Property(v => v.SKU)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(v => v.AvailableStock)
            .IsRequired();

        builder.Property(v => v.RestockThreshold)
            .IsRequired();

        builder.Property(v => v.MaxStockThreshold)
            .IsRequired();

        builder.Property(v => v.OnReorder)
            .IsRequired();

        // Relationships
        builder.HasOne(v => v.CatalogItem)
            .WithMany(i => i.Variants)
            .HasForeignKey(v => v.CatalogItemId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(v => v.CatalogItemColor)
            .WithMany()
            .HasForeignKey(v => v.CatalogItemColorId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(v => v.CatalogItemSize)
            .WithMany()
            .HasForeignKey(v => v.CatalogItemSizeId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);
    }
}