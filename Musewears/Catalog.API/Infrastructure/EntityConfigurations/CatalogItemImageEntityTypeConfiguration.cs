namespace Catalog.API.Infrastructure.EntityConfigurations;

internal class CatalogItemImageEntityTypeConfiguration : IEntityTypeConfiguration<CatalogItemImage>
{
    public void Configure(EntityTypeBuilder<CatalogItemImage> builder)
    {
        builder.ToTable("CatalogItemImage");

        builder.HasKey(img => img.Id);

        builder.Property(img => img.ImageUrl)
            .IsRequired();
            // .HasMaxLength(200);
        
        // Optional relationships
        builder.HasOne(ci => ci.CatalogItem)
            .WithMany(i => i.Images)
            .HasForeignKey(ci => ci.CatalogItemId)
            // .IsRequired(false)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(img => img.CatalogItemColor)
            .WithMany()
            .HasForeignKey(img => img.CatalogItemColorId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Cascade);
        
        builder.HasOne(ci => ci.CatalogItemSize)
            .WithMany()
            .HasForeignKey(ci => ci.CatalogItemSizeId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Cascade);
        
        builder.HasOne(ci => ci.CatalogItemVariant)
            .WithMany()
            .HasForeignKey(ci => ci.CatalogItemVariantId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Cascade);
        
        // Indexes for faster queries
        builder.HasIndex(ci => ci.CatalogItemId);
        builder.HasIndex(ci => ci.CatalogItemColorId);
        builder.HasIndex(ci => ci.CatalogItemVariantId);
    }
}