namespace Catalog.API.Infrastructure.EntityConfigurations;

internal class CatalogItemColorEntityTypeConfiguration : IEntityTypeConfiguration<CatalogItemColor>
{
    public void Configure(EntityTypeBuilder<CatalogItemColor> builder)
    {
        builder.ToTable("CatalogItemColor");

        builder.HasKey(ci => ci.Id);

        builder.Property(ci => ci.ColorName)
            .HasMaxLength(50)
            .IsRequired();
        
        builder.Property(ci => ci.ColorCode)
            .HasMaxLength(10)
            .IsRequired();

        // Relationship: CatalogItemColor -> CatalogItem (many-to-one)
        builder.HasOne(ci => ci.CatalogItem)
            .WithMany(i => i.Colors)
            .HasForeignKey(ci => ci.CatalogItemId)
            .OnDelete(DeleteBehavior.Cascade);

        // Relationship: CatalogItemColor -> Images (one-to-many)
        // builder.HasMany(ci => ci.Images)
        //     .WithOne(img => img.CatalogItemColor)
        //     .<PERSON><PERSON>ore<PERSON><PERSON>(img => img.CatalogItemColorId)
        //     .OnDelete(DeleteBehavior.Cascade);
    }
}