using Catalog.API.Infrastructure.EntityConfigurations;
using DomainEventLogEF;
// using IntegrationEventLogEF;
using Microsoft.Extensions.Configuration;

namespace Catalog.API.Infrastructure;

/// <remarks>
/// Add migrations using the following command inside the 'Catalog.APIs' project directory:
///
/// dotnet ef migrations add --context CatalogContext [migration-name]
/// </remarks>
public class CatalogContext : DbContext
{
    public CatalogContext(DbContextOptions<CatalogContext> options, IConfiguration configuration) : base(options)
    {
    }

    public DbSet<CatalogItem> CatalogItems { get; set; }
    public DbSet<CatalogBrand> CatalogBrands { get; set; }
    public DbSet<CatalogType> CatalogTypes { get; set; }
    
    public DbSet<CatalogItemColor> CatalogItemColors { get; set; }
    
    public DbSet<CatalogItemImage> CatalogItemImages { get; set; }
    
    public DbSet<CatalogItemSize> CatalogItemSizes { get; set; }
    
    public DbSet<CatalogItemVariant> CatalogItemVariants { get; set; }
    
    public DbSet<Rating> Ratings { get; set; }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        builder.HasPostgresExtension("vector");
        builder.ApplyConfiguration(new CatalogBrandEntityTypeConfiguration());
        builder.ApplyConfiguration(new CatalogTypeEntityTypeConfiguration());
        builder.ApplyConfiguration(new CatalogItemEntityTypeConfiguration());
        builder.ApplyConfiguration(new CatalogItemRatingEntityTypeConfiguration());
        builder.ApplyConfiguration(new CatalogItemColorEntityTypeConfiguration());
        builder.ApplyConfiguration(new CatalogItemImageEntityTypeConfiguration());
        builder.ApplyConfiguration(new CatalogItemSizeEntityTypeConfiguration());
        builder.ApplyConfiguration(new CatalogItemVariantEntityTypeConfiguration());

        // Add the outbox table to this context
        builder.UseDomainEventLogs();
    }
}
