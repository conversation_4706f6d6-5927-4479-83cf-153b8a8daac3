using EntityFrameworkCore.Triggered;

namespace Catalog.API.Infrastructure.Triggers;

public class CatalogItemRatingGuardTrigger(ILogger<CatalogItemRatingGuardTrigger> logger) : IBeforeSaveTrigger<CatalogItem>
{
    public Task BeforeSave(ITriggerContext<CatalogItem> context, 
        CancellationToken cancellationToken)
    {
        
        // Only check modified entities
        if (context.ChangeType != ChangeType.Modified) 
            return Task.CompletedTask;

        // Handle detached entities scenario
        var originalRating = context.UnmodifiedEntity?.Rating;
        
        // Skip if Rating hasn't changed
        if (context.Entity.Rating == originalRating) 
            return Task.CompletedTask;
        
        // Block unauthorized changes
        if (!context.Entity.RatingUpdatedFromTrigger)
        {
            throw new InvalidOperationException(
                "Direct updates to Rating are not allowed. " +
                "Modify via Ratings table.");
        }
        
        logger.LogWarning(
            $"Blocked direct rating update for item {context.Entity.Id}. " +
            // $"User: {_currentUserService.UserId}, " +
            $"From: {originalRating} => {context.Entity.Rating}"
        );
        
        // Reset flag for future updates
        context.Entity.RatingUpdatedFromTrigger = false;
        return Task.CompletedTask;
    }
}