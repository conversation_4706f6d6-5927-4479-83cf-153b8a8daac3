using EntityFrameworkCore.Triggered;
using EntityFrameworkCore.Triggered.Lifecycles;

namespace Catalog.API.Infrastructure.Triggers;

public class RatingChangeTrigger(CatalogContext context) : IAfterSaveTrigger<Rating>, IAfterSaveCompletedTrigger
{
    private readonly HashSet<int> _dirtyIds = [];

    public Task AfterSave(ITriggerContext<Rating> triggerContext, CancellationToken ct)
    {
        _dirtyIds.Add(triggerContext.Entity.CatalogItemId);
        return Task.CompletedTask;
    }

    public async Task AfterSaveCompleted(CancellationToken ct)
    {
        switch (_dirtyIds.Count)
        {
            case 0: // No updates
                return;
            
            case 1: // Optimized path for single update
                await UpdateSingleItem(_dirtyIds.First(), ct);
                break;
            
            default: // Bulk path for multiple updates
                await UpdateBulk(ct);
                break;
        }

        await context.SaveChangesAsync(ct);
        _dirtyIds.Clear();
    }

    private async Task UpdateSingleItem(int itemId, CancellationToken ct)
    {
        // Combined query gets both values at once
        var stats = await context.Ratings
            .Where(r => r.CatalogItemId == itemId)
            .GroupBy(r => r.CatalogItemId)
            .Select(g => new {
                Avg = Math.Round(g.Average(r => r.RatingValue), 2),
                Count = g.Count()
            })
            .FirstAsync(ct);

        var item = await context.CatalogItems.FindAsync([itemId], cancellationToken: ct);
        if (item != null)
        {
            item.Rating = stats.Avg;
            item.ReviewCount = stats.Count;
            item.RatingUpdatedFromTrigger = true;
        }
    }

    private async Task UpdateBulk(CancellationToken ct)
    {
        var aggregates = await context.Ratings
            .Where(r => _dirtyIds.Contains(r.CatalogItemId))
            .GroupBy(r => r.CatalogItemId)
            .Select(g => new {
                Id = g.Key,
                Avg = Math.Round(g.Average(r => r.RatingValue), 2),
                Count = g.Count()
            })
            .ToListAsync(ct);

        var items = await context.CatalogItems
            .Where(c => _dirtyIds.Contains(c.Id))
            .ToDictionaryAsync(c => c.Id, ct);

        foreach (var agg in aggregates)
        {
            if (!items.TryGetValue(agg.Id, out var item)) continue;
            
            item.Rating = agg.Avg;
            item.ReviewCount = agg.Count;
            item.RatingUpdatedFromTrigger = true;
        }
    }
}