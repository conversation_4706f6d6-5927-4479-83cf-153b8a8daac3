using System.Data.Common;
using Microsoft.EntityFrameworkCore.Diagnostics;

namespace Catalog.API.Infrastructure.Interceptors;

public class QueryTimeoutInterceptor(int timeoutSeconds) : DbCommandInterceptor
{
    public override InterceptionResult<DbDataReader> ReaderExecuting(
        DbCommand command,
        CommandEventData eventData,
        InterceptionResult<DbDataReader> result)
    {
        command.CommandTimeout = timeoutSeconds;
        return result;
    }

    public override ValueTask<InterceptionResult<DbDataReader>> ReaderExecutingAsync(
        DbCommand command,
        CommandEventData eventData,
        InterceptionResult<DbDataReader> result,
        CancellationToken cancellationToken = default)
    {
        command.CommandTimeout = timeoutSeconds;
        return new ValueTask<InterceptionResult<DbDataReader>>(result);
    }
}