using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using Npgsql.EntityFrameworkCore.PostgreSQL;

namespace Catalog.API.Infrastructure;

public class CatalogContextDesignTimeFactory : IDesignTimeDbContextFactory<CatalogContext>
{
    public CatalogContext CreateDbContext(string[] args)
    {
        var optionsBuilder = new DbContextOptionsBuilder<CatalogContext>();
        
        // Use a default connection string for design time
        optionsBuilder.UseNpgsql("Host=localhost;port=5432;Database=catalog;Username=root;Password=********************************", 
            npgsqlOptions => npgsqlOptions.UseVector());
        
        // Create a minimal configuration for design time
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection()
            .Build();
        
        return new CatalogContext(optionsBuilder.Options, configuration);
    }
}
