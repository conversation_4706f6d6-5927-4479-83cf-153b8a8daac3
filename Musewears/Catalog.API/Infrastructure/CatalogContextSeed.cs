using System.Reflection;
using System.Text.Json;
using Catalog.API.Services;
using Microsoft.AspNetCore.Hosting;

namespace Catalog.API.Infrastructure;

public class CatalogContextSeed(
    IWebHostEnvironment env,
    IOptions<CatalogOptions> settings,
    ICatalogAi catalogAi,
    ILogger<CatalogContextSeed> logger) : IDbSeeder<CatalogContext>
{
    public async Task SeedAsync(CatalogContext context)
    {
        // var useCustomizationData = settings.Value.UseCustomizationData;
        // var contentRootPath = env.ContentRootPath;
        // var picturePath = env.WebRootPath;

        // Workaround from https://github.com/npgsql/efcore.pg/issues/292#issuecomment-388608426
        await context.Database.OpenConnectionAsync();
        await ((NpgsqlConnection)context.Database.GetDbConnection()).ReloadTypesAsync();

        if (!context.CatalogItems.Any())
        {
            // var sourcePath = Path.Combine(contentRootPath, "..", "Catalog.API", "Setup", "catalog.json");
                           // …/Musewears/Server
                           
            var assemblyLocation = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
            var sourcePath = Path.GetFullPath(
                Path.Combine(assemblyLocation!, "Setup", "catalog.json")
            );

            var sourceJson = await File.ReadAllTextAsync(sourcePath);
            var sourceItems = JsonSerializer.Deserialize<CatalogSourceEntry[]>(sourceJson);

            context.CatalogBrands.RemoveRange(context.CatalogBrands);
            await context.CatalogBrands.AddRangeAsync(sourceItems!.Select(x => x.Brand).Distinct()
                .Select(brandName => new CatalogBrand { Brand = brandName }));
            logger.LogInformation("Seeded catalog with {NumBrands} brands", context.CatalogBrands.Count());

            context.CatalogTypes.RemoveRange(context.CatalogTypes);
            await context.CatalogTypes.AddRangeAsync(sourceItems.Select(x => x.Type).Distinct()
                .Select(typeName => new CatalogType { Type = typeName }));
            logger.LogInformation("Seeded catalog with {NumTypes} types", context.CatalogTypes.Count());

            await context.SaveChangesAsync();

            var brandIdsByName = await context.CatalogBrands.ToDictionaryAsync(x => x.Brand, x => x.Id);
            var typeIdsByName = await context.CatalogTypes.ToDictionaryAsync(x => x.Type, x => x.Id);

            // Define vendor IDs for testing
            var vendorIds = new List<string> { "vendor1", "vendor2", "vendor3" };

            
            var catalogItems = new List<CatalogItem>();
            
            // Seed CatalogItems for each vendor
            foreach (var vendorId in vendorIds)
            {
                if (context.CatalogItems.Any(c => c.VendorId == vendorId)) continue;
                
                var vendorItems = sourceItems.Select(source => 
                {
                    // Create color first
                    var colors = new List<CatalogItemColor> {
                        new CatalogItemColor
                        {
                            ColorName = "Yellow",
                            ColorCode = "#FFFF00"
                        },
                        new CatalogItemColor
                        {
                            ColorName = "Brown",
                            ColorCode = "#A52A2A"
                        },
                        new CatalogItemColor
                        {
                            ColorName = "Red",
                            ColorCode = "#FF0000"
                        },
                        new CatalogItemColor
                        {
                            ColorName = "Blue",
                            ColorCode = "#0000FF"
                        },
                        new CatalogItemColor
                        {
                            ColorName = "White",
                            ColorCode = "#FFFFFF"
                        },
                        new CatalogItemColor
                        {
                            ColorName = "Gray",
                            ColorCode = "#808080"
                        }
                    };

                    // Create images associated with the color
                    var images = new List<CatalogItemImage>
                    {
                        new CatalogItemImage
                        {
                            ImageUrl = $"{source.Id}.webp",
                            DisplayOrder = 0,
                            CatalogItemColor = colors[0],  // Associate with color
                            IsPrimary = true
                        },
                        new CatalogItemImage
                        {
                            ImageUrl = $"{source.Id}.webp",
                            DisplayOrder = 1,
                            CatalogItemColor = colors[1]  // Associate with color
                        },
                        new CatalogItemImage
                        {
                            ImageUrl = $"{source.Id}.webp",
                            DisplayOrder = 2,
                            CatalogItemColor = colors[2],  // Associate with color
                        },
                        new CatalogItemImage
                        {
                            ImageUrl = $"{source.Id}.webp",
                            DisplayOrder = 3,
                            CatalogItemColor = colors[3],  // Associate with color
                        },
                        new CatalogItemImage
                        {
                            ImageUrl = $"{source.Id}.webp",
                            DisplayOrder = 4,
                            CatalogItemColor = colors[4],  // Associate with color
                        },
                        new CatalogItemImage
                        {
                            ImageUrl = $"{source.Id}.webp",
                            DisplayOrder = 5,
                            CatalogItemColor = colors[5],  // Associate with color
                        }
                    };

                    // Create sizes
                    var sizes = new List<CatalogItemSize>
                    {
                        new CatalogItemSize { SizeName = "Small" },
                        new CatalogItemSize { SizeName = "Medium" },
                        new CatalogItemSize { SizeName = "Large" },
                        new CatalogItemSize { SizeName = "Extra Large" },
                    };

                    return new CatalogItem
                    {
                        VendorId = vendorId,
                        Name = source.Name,
                        Description = source.Description,
                        Price = source.Price,
                        CatalogBrandId = brandIdsByName[source.Brand],
                        CatalogTypeId = typeIdsByName[source.Type],
                        AvailableStock = 100,
                        MaxStockThreshold = 200,
                        RestockThreshold = 10,
                        PictureFileName = $"{source.Id}.webp",
                        OnReorder = false,
                        // Rating = 0m,
                        Colors = colors,
                        Sizes = sizes,
                        Images = images
                    };
                }).ToArray();
                    
                if (catalogAi.IsEnabled)
                {
                    logger.LogInformation("Generating {NumItems} embeddings", vendorItems.Length);
                    var embeddings = await catalogAi.GetEmbeddingsAsync(vendorItems);
                    for (var i = 0; i < vendorItems.Length; i++)
                    {
                        vendorItems[i].Embedding = embeddings?[i];
                    }
                }
                
                catalogItems.AddRange(vendorItems);

                logger.LogInformation("Seeded catalog with {NumItems} items for vendor {VendorId}", vendorItems.Length, vendorId);
            }
            
            // var catalogItems = sourceItems.Select(source => new CatalogItem
            // {
            //     Id = source.Id,
            //     Name = source.Name,
            //     Description = source.Description,
            //     Price = source.Price,
            //     CatalogBrandId = brandIdsByName[source.Brand],
            //     CatalogTypeId = typeIdsByName[source.Type],
            //     AvailableStock = 100,
            //     MaxStockThreshold = 200,
            //     RestockThreshold = 10,
            //     PictureFileName = $"{source.Id}.webp",
            // }).ToArray();

            // if (catalogAi.IsEnabled)
            // {
            //     logger.LogInformation("Generating {NumItems} embeddings", catalogItems.Length);
            //     var embeddings = await catalogAi.GetEmbeddingsAsync(catalogItems);
            //     for (var i = 0; i < catalogItems.Length; i++)
            //     {
            //         catalogItems[i].Embedding = embeddings?[i];
            //     }
            // }

            await context.CatalogItems.AddRangeAsync(catalogItems);
            // logger.LogInformation("Seeded catalog with {NumItems} items", context.CatalogItems.Count());
            await context.SaveChangesAsync();
        }
    }

    private class CatalogSourceEntry
    {
        public int Id { get; set; }
        public string Type { get; set; }
        public string Brand { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public decimal Price { get; set; }
    }
}
