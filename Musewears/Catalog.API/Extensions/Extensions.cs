using Catalog.API.Apis;
using Catalog.API.Infrastructure.Interceptors;
using Catalog.API.Infrastructure.Triggers;
using Catalog.API.Services;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using ServiceDefaults;

namespace Catalog.API.Extensions;

public static class Extensions
{
    public static void AddCatalogServices(this IHostApplicationBuilder builder)
    {
        // Avoid loading full database config and migrations if startup
        // is being invoked from build-time OpenAPI generation
        // if (builder.Environment.IsBuild())
        // {
        //     builder.Services.AddDbContext<CatalogContext>();
        //     return\
        
        // }
        
        // Only use Aspire in Development when AppHost is running
        if (builder.Environment.IsDevelopment())
        {
            builder.AddNpgsqlDbContext<CatalogContext>("catalog", configureDbContextOptions: dbContextOptionsBuilder =>
            {
                dbContextOptionsBuilder.UseNpgsql(npgsqlDbContextOptionsBuilder =>
                    {
                        npgsqlDbContextOptionsBuilder.UseVector();
                    })
                    .UseTriggers(triggerOptions =>
                    {
                        triggerOptions.AddTrigger<RatingChangeTrigger>();
                        triggerOptions.AddTrigger<CatalogItemRatingGuardTrigger>();
                        triggerOptions.AddAssemblyTriggers();
                    })
                    .AddInterceptors(new QueryTimeoutInterceptor(5));
            });
            
            builder.Services.AddTriggeredDbContextPool<CatalogContext>();
        }
        else
        {
            var connectionString = builder.Configuration.GetConnectionString("CatalogConnection");
            string npgsqlConn;

            if (connectionString!.StartsWith("postgresql://", StringComparison.OrdinalIgnoreCase))
            {
                var uri = new Uri(connectionString);
                var parts = uri.UserInfo.Split(':', 2);
                var bldr = new NpgsqlConnectionStringBuilder
                {
                    Host                     = uri.Host,
                    // Port                     = uri.Port,
                    Database                 = uri.AbsolutePath.TrimStart('/'),
                    Username                 = parts[0],
                    Password                 = parts.Length > 1 ? parts[1] : "",
                    SslMode                  = SslMode.Require              // or false, per your policy
                };
                npgsqlConn = bldr.ToString();
            }
            else
            {
                npgsqlConn = connectionString;
            }
            
            builder.Services.AddDbContext<CatalogContext>(dbContextOptionsBuilder =>
            {
                dbContextOptionsBuilder.UseNpgsql(npgsqlConn, npgsqlDbContextOptionsBuilder =>
                    {
                        npgsqlDbContextOptionsBuilder.UseVector();
                    })
                    .UseTriggers(triggerOptions =>
                    {
                        triggerOptions.AddTrigger<RatingChangeTrigger>();
                        triggerOptions.AddTrigger<CatalogItemRatingGuardTrigger>();
                        triggerOptions.AddAssemblyTriggers();
                    })
                    .AddInterceptors(new QueryTimeoutInterceptor(5));
            });
        }
        

        // REVIEW: This is done for development ease but shouldn't be here in production
        builder.Services.AddMigration<CatalogContext, CatalogContextSeed>();

        // Integration events removed - using domain events in monolith architecture

        builder.Services.AddOptions<CatalogOptions>()
            .BindConfiguration(nameof(CatalogOptions));

        builder.Services.AddRatingServices();

        builder.Services.AddScoped<ICatalogAi, CatalogAi>();
        
        
        builder.Services.AddProblemDetails();
        
        // var withApiVersioning = builder.Services.AddApiVersioning();
        
        // builder.AddDefaultOpenApi(withApiVersioning);
    }


    private static IServiceCollection AddRatingServices(this IServiceCollection services)
    {
        services.AddScoped<IRatingService, RatingService>();
        return services;
    }
    
    public static IApplicationBuilder UseCatalogApi(this WebApplication app)
    {
        ArgumentNullException.ThrowIfNull(app);
        
        // app.MapDefaultEndpoints();

        app.UseStatusCodePages();

        app.MapCatalogApi();

        // app.UseDefaultOpenApi();

        return app;
    }
}