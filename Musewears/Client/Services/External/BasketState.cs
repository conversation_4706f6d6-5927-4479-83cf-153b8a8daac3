using System.Security.Claims;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using WebAppComponents.Catalog;
using WebAppComponents.Services;

namespace Musewears.Client.Services.External;

public class BasketState(
    BasketService basketService,
    CatalogService catalogService,
    OrderingService orderingService,
    UserService userService,
    AuthenticationStateProvider authenticationStateProvider) : IBasketState
{
    private Task<IReadOnlyCollection<BasketItem>>? _cachedBasket;
    private readonly HashSet<BasketStateChangedSubscription> _changeSubscriptions = [];

    public Task DeleteBasketAsync()
        => basketService.DeleteBasketAsync();

    public async Task<IReadOnlyCollection<BasketItem>> GetBasketItemsAsync()
        => (await GetUserAsync()).Identity?.IsAuthenticated == true
        ? await FetchBasketItemsAsync()
        : [];

    public IDisposable NotifyOnChange(EventCallback callback)
    {
        var subscription = new BasketStateChangedSubscription(this, callback);
        _changeSubscriptions.Add(subscription);
        return subscription;
    }

    public async Task AddAsync(CatalogItem item, int quantity = 1)
    {
        var items = (await FetchBasketItemsAsync()).Select(i => new BasketQuantity(i.ProductId, i.Quantity)).ToList();
        var found = false;
        for (var i = 0; i < items.Count; i++)
        {
            var existing = items[i];
            if (existing.ProductId != item.Id) continue;
            
            items[i] = existing with { Quantity = existing.Quantity + quantity };
            found = true;
            break;
        }

        if (!found)
        {
            items.Add(new BasketQuantity(item.Id, quantity));
        }

        _cachedBasket = null;
        await basketService.UpdateBasketAsync(items);
        await NotifyChangeSubscribersAsync();
    }

    public async Task<bool> UpdateBasketAsync(IReadOnlyCollection<BasketItem> basketItems)
    {
        try
        {
            List<BasketQuantity> items = [..basketItems.Select(i => new BasketQuantity(i.ProductId, i.Quantity))];
            await basketService.UpdateBasketAsync(items);
            return true;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
        }

        return false;
    }

    public async Task SetQuantityAsync(int productId, int quantity)
    {
        var existingItems = (await FetchBasketItemsAsync()).ToList();
        if (existingItems.FirstOrDefault(item => item.ProductId == productId) is { } row)
        {
            if (quantity > 0)
            {
                row.Quantity = quantity;
            }
            else
            {
                existingItems.Remove(row);
            }

            _cachedBasket = null;
            await basketService.UpdateBasketAsync(existingItems.Select(i => new BasketQuantity(i.ProductId, i.Quantity)).ToList());
            await NotifyChangeSubscribersAsync();
        }
    }

    public async Task CheckoutAsync(BasketCheckoutInfo checkoutInfo, string? paymentReference = null)
    {
        if (checkoutInfo.RequestId == Guid.Empty)
        {
            checkoutInfo.RequestId = Guid.NewGuid();
        }

        var user = await userService.GetUserAsync();

        // persistedState.TryTakeFromJson<UserInfo>(nameof(UserInfo), out var user);

        var buyerId = user?.UserId ?? throw new InvalidOperationException("User does not have a buyer ID");
        var userName = user.UserName ?? throw new InvalidOperationException("User does not have a user name");

        // Get details for the items in the basket
        var orderItems = await FetchBasketItemsAsync();

        // Validate that we have items before creating order
        if (!orderItems.Any())
        {
            throw new InvalidOperationException("Cannot checkout with an empty basket");
        }

        // Create order immediately with "Pending" status to avoid race conditions
        // Order will be created before payment processing starts
        var request = new CreateOrderRequest(
            buyerId, // UserId
            userName, // UserName
            checkoutInfo.City!, // City
            checkoutInfo.Street!, // Street
            checkoutInfo.State!, // State
            checkoutInfo.Country!, // Country
            checkoutInfo.ZipCode!, // ZipCode
            "", // CardNumber - Not used for Interswitch payments
            "", // CardHolderName - Not used for Interswitch payments
            DateTime.MinValue, // CardExpiration - Not used for Interswitch payments
            "", // CardSecurityNumber - Not used for Interswitch payments
            0, // CardTypeId - Not used for Interswitch payments
            buyerId, // Buyer
            [.. orderItems], // Items
            null, // PaymentReference - Will be set by Interswitch webhook
            paymentReference); // MerchantReference - Our generated GUID

        // Create order immediately - this eliminates race condition with webhook
        await orderingService.CreateOrder(request, checkoutInfo.RequestId);

        // Note: Basket will be cleared after successful payment initiation
        // This ensures basket is only cleaned when payment is actually sent
    }

    private Task NotifyChangeSubscribersAsync()
        => Task.WhenAll(_changeSubscriptions.Select(s => s.NotifyAsync()));

    private async Task<ClaimsPrincipal> GetUserAsync()
        => (await authenticationStateProvider.GetAuthenticationStateAsync()).User;

    private Task<IReadOnlyCollection<BasketItem>> FetchBasketItemsAsync()
    {
        return _cachedBasket ??= FetchCoreAsync();

        async Task<IReadOnlyCollection<BasketItem>> FetchCoreAsync()
        {
            var quantities = await basketService.GetBasketAsync();
            if (quantities.Count == 0)
            {
                return [];
            }

            // Get details for the items in the basket
            var basketItems = new List<BasketItem>();
            var productIds = quantities.Select(row => row.ProductId);
            var catalogItems = (await catalogService.GetCatalogItems(productIds)).ToDictionary(k => k.Id, v => v);
            foreach (var item in quantities)
            {
                var catalogItem = catalogItems[item.ProductId];
                var orderItem = new BasketItem
                {
                    Id = Guid.NewGuid().ToString(), // TODO: this value is meaningless, use ProductId instead.
                    ProductId = catalogItem.Id,
                    ProductName = catalogItem.Name,
                    UnitPrice = catalogItem.Price,
                    Quantity = item.Quantity,
                };
                basketItems.Add(orderItem);
            }

            return basketItems;
        }
    }

    private class BasketStateChangedSubscription(BasketState Owner, EventCallback Callback) : IDisposable
    {
        public Task NotifyAsync() => Callback.InvokeAsync();
        public void Dispose() => Owner._changeSubscriptions.Remove(this);
    }
}

public record CreateOrderRequest(
    string UserId,
    string UserName,
    string City,
    string Street,
    string State,
    string Country,
    string ZipCode,
    string CardNumber,
    string CardHolderName,
    DateTime CardExpiration,
    string CardSecurityNumber,
    int CardTypeId,
    string Buyer,
    List<BasketItem> Items,
    string? PaymentReference = null,
    string? MerchantReference = null);
