using System.Net.Http.Json;

namespace Musewears.Client.Services.External;

public class OrderingService(HttpClient httpClient)
{
    private const string RemoteServiceBaseUrl = "/api/Orders/";

    public Task<OrderRecord[]> GetOrders()
    {
        return httpClient.GetFromJsonAsync<OrderRecord[]>(RemoteServiceBaseUrl)!;
    }

    public Task CreateOrder(CreateOrderRequest request, Guid requestId)
    {
        var requestMessage = new HttpRequestMessage(HttpMethod.Post, RemoteServiceBaseUrl);
        requestMessage.Headers.Add("x-requestid", requestId.ToString());
        requestMessage.Content = JsonContent.Create(request);
        return httpClient.SendAsync(requestMessage);
    }

    public Task UpdatePaymentStatusAsync(string paymentReference, string merchantReference, string paymentStatus, string? userId = null, string? userName = null, string? responseCode = null)
    {
        var request = new
        {
            PaymentReference = paymentReference,
            MerchantReference = merchantReference,
            PaymentStatus = paymentStatus,
            UserId = userId,
            UserName = userName,
            ResponseCode = responseCode
        };

        var requestMessage = new HttpRequestMessage(HttpMethod.Put, $"{RemoteServiceBaseUrl}payment-status");
        requestMessage.Headers.Add("x-requestid", Guid.NewGuid().ToString());
        requestMessage.Content = JsonContent.Create(request);
        return httpClient.SendAsync(requestMessage);
    }
}

public record OrderRecord(
    int OrderNumber,
    DateTime Date,
    string Status,
    decimal Total);
