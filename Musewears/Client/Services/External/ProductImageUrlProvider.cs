using WebAppComponents.Catalog;
using WebAppComponents.Services;

namespace Musewears.Client.Services.External;

public class ProductImageUrlProvider : IProductImageUrlProvider
{
    public List<CatalogItemImage> GetProductImages(CatalogItem item, int? colorId = null, int? sizeId = null, int? variantId = null)
    {
        var images = item.Images?.AsQueryable();

        if (variantId.HasValue)
        {
            images = images?.Where(i => i.CatalogItemVariantId == variantId);
        }
        else if (colorId.HasValue)
        {
            images = images?.Where(i => i.CatalogItemColorId == colorId && i.CatalogItemVariantId == null);
        }
        else if (sizeId.HasValue)
        {
            images = images?.Where(i => i.CatalogItemSizeId == sizeId && i.CatalogItemVariantId == null);
        }
        else
        {
            images = images?.Where(i => i.CatalogItemColorId == null && i.CatalogItemVariantId == null);
        }

        return images?.OrderBy(i => i.DisplayOrder).ToList() ?? [
            new CatalogItemImage
            {
                ImageUrl = item.PictureUrl,
                DisplayOrder = 0,
                IsPrimary = true
            }];
    }

    public string GetProductImageUrl(int productId)
        => $"/api/catalog/items/{productId}/pic?api-version=2.0";
        // => $"product-images/{productId}?api-version=2.0";

    public string GetProductImageUrl(int productId, string imageUrl)
        => GetProductImageUrl(productId);
    // => $"product-images/{productId}/{imageUrl}?api-version=2.0";
}
