using Basket.API.Grpc;
using GrpcBasketItem = Basket.API.Grpc.BasketItem;
using GrpcBasketClient = Basket.API.Grpc.Basket.BasketClient;

namespace Musewears.Client.Services.External;

public class BasketService(GrpcBasketClient basketClient)
{
    public async Task<IReadOnlyCollection<BasketQuantity>> GetBasketAsync()
    {
        var result = await basketClient.GetBasketAsync(new GetBasketRequest());
        return MapToBasket(result);
    }

    public async Task DeleteBasketAsync()
    {
        await basketClient.DeleteBasketAsync(new DeleteBasketRequest());
    }

    public async Task UpdateBasketAsync(IReadOnlyCollection<BasketQuantity> basket)
    {
        var updatePayload = new UpdateBasketRequest();

        foreach (var item in basket)
        {
            var updateItem = new GrpcBasketItem
            {
                ProductId = item.ProductId,
                Quantity = item.Quantity,
            };
            updatePayload.Items.Add(updateItem);
        }

        await basketClient.UpdateBasketAsync(updatePayload);
    }

    private static List<BasketQuantity> MapToBasket(CustomerBasketResponse response)
    {
        return response.Items.Select(item => new BasketQuantity(item.ProductId, item.Quantity)).ToList();
    }
}

public record BasketQuantity(int ProductId, int Quantity);
