

using Musewears.Client.Components;

namespace Musewears.Client.Services;

public class MusewearDialogService
{
    public event Func<DialogParameters, Task<bool>>? OnShow;

    public async Task<bool> ShowDialog(string title, string text, ModalDialog.ModalDialogType dialogType)
    {
        if (OnShow != null)
        {
            return await OnShow.Invoke(new DialogParameters
            {
                Title = title,
                Message = text,
                DialogType = dialogType
            });
        }

        return false;
    }
}

public class DialogParameters
{
    public string? Title { get; set; }

    public string? Message { get; set; }

    public ModalDialog.ModalDialogType DialogType { get; set; }
}