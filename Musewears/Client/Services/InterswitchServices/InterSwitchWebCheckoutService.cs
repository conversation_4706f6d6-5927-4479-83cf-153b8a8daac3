using System.Diagnostics.CodeAnalysis;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Musewears.Shared.Models;
using Newtonsoft.Json;

namespace Musewears.Client.Services.InterswitchServices;

using System.Text.Json;

public class InterSwitchWebCheckoutService: IDisposable
{
    private readonly HttpClient _httpClient;
    private readonly string _merchantCode;
    private readonly string _payItemId;
    private readonly string _clientId;
    private readonly string _secret;
    private readonly bool _isTestMode;
    private readonly string _baseUrl;
    private readonly IJSRuntime _jsRuntime;
    private DotNetObjectReference<InterSwitchWebCheckoutService>? _dotNetObjectReference;
    
    private readonly HashSet<PaymentStateChangedSubscription> _changeSubscriptions = [];

    public InterSwitchWebCheckoutService(HttpClient httpClient, IConfiguration configuration, IJSRuntime jsRuntime)
    {
        _httpClient = httpClient;
        _merchantCode = configuration["InterSwitch:MerchantCode"]!;
        _payItemId = configuration["InterSwitch:PayItemId"]!;
        _clientId = configuration["InterSwitch:ClientId"]!;
        _secret = configuration["InterSwitch:Secret"]!;
        _isTestMode = configuration.GetValue("InterSwitch:TestMode", true);
        _jsRuntime = jsRuntime;
        
        // Set base URL based on environment
        _baseUrl = _isTestMode 
            ? "https://qa.interswitchng.com" 
            : "https://webpay.interswitchng.com";
    }
    
    private async Task SetPaymentHandlerAsync()
    {
        if (_dotNetObjectReference != null)
        {
            return;
        }
        
        _dotNetObjectReference = DotNetObjectReference.Create(this);
        
        await _jsRuntime.InvokeVoidAsync(
            "setPaymentHandler", 
            _dotNetObjectReference
        );
    }
    
    public async Task<IDisposable> NotifyOnChange(EventCallback<PaymentResponse> callback)
    {
        await SetPaymentHandlerAsync();
        
        var subscription = new PaymentStateChangedSubscription(this, callback);
        _changeSubscriptions.Add(subscription);
        return subscription;
    }
    
    private Task NotifyChangeSubscribersAsync(PaymentResponse response)
        => Task.WhenAll(_changeSubscriptions.Select(s => s.NotifyAsync(response)));

    [JSInvokable]
    public async Task OnPaymentResponse(object json, string userId)
    {
        var response = JsonConvert.DeserializeObject<PaymentResponse>($"{json}");

        await NotifyChangeSubscribersAsync(response!);
    }

    public async Task InitiateInlineCheckout(PaymentRequest request, string userId)
    {
        var inlineRequest = CreateInlineCheckoutRequest(request);
        
        // Prepare payment request object for JavaScript
        var jsPaymentRequest = new
        {
            merchant_code = inlineRequest.MerchantCode,
            pay_item_id = inlineRequest.PayItemId,
            pay_item_name = inlineRequest.PayItemName,
            txn_ref = inlineRequest.TransactionReference,
            site_redirect_url = inlineRequest.SiteRedirectUrl,
            amount = inlineRequest.Amount,
            currency = inlineRequest.Currency,
            cust_name = inlineRequest.CustomerName,
            cust_email = inlineRequest.CustomerEmail,
            cust_id = inlineRequest.CustomerId,
            cust_mobile_no = inlineRequest.CustomerMobileNo,
            tokenise_card = inlineRequest.TokeniseCard,
            mode = inlineRequest.Mode,
            secret = _secret,
            client_id = _clientId,
            onComplete = "paymentCallback"
        };
        
        Console.WriteLine($"User ID: {jsPaymentRequest.cust_id}");
        
        // Start inline checkout
        await _jsRuntime.InvokeVoidAsync("initiatePayment", jsPaymentRequest, userId);
    }

    // Generate payment request for Web Redirect
    public PaymentRedirectRequest CreatePaymentRedirectRequest(PaymentRequest request)
    {
        return new PaymentRedirectRequest
        {
            MerchantCode = _merchantCode,
            PayItemId = _payItemId,
            TransactionReference = request.txn_ref,
            Amount = (request.amount * 100).ToString("F0"), // Convert to kobo
            Currency = "566", // Nigerian Naira ISO code
            // CustomerName = request.CustomerName,
            // CustomerEmail = request.CustomerEmail,
            // CustomerId = request.CustomerId,
            // PayItemName = request.Description,
            SiteRedirectUrl = request.site_redirect_url,
            PostUrl = _isTestMode 
                ? "https://newwebpay.qa.interswitchng.com/collections/w/pay"
                : "https://newwebpay.interswitchng.com/collections/w/pay"
        };
    }

    // Generate payment request for Inline Checkout
    private InlineCheckoutRequest CreateInlineCheckoutRequest(PaymentRequest request)
    {
        return new InlineCheckoutRequest
        {
            MerchantCode = _merchantCode,
            PayItemId = _payItemId,
            PayItemName = request.pay_item_name ?? "Musewears Purchase",
            TransactionReference = request.txn_ref,
            Amount = request.amount, // Convert to kobo
            Currency = request.currency, // Nigerian Naira ISO code
            CustomerName = request.cust_name,
            CustomerEmail = request.cust_email,
            CustomerId = request.cust_id,
            CustomerMobileNo = request.cust_mobile_no ?? "",
            SiteRedirectUrl = request.site_redirect_url,
            TokeniseCard = "false",
            Secret = request.secret,
            ClientId = request.client_id,
            Mode = _isTestMode ? "TEST" : "LIVE",
            InlineCheckoutScript = _isTestMode
                ? "https://newwebpay.qa.interswitchng.com/inline-checkout.js"
                : "https://newwebpay.interswitchng.com/inline-checkout.js"
        };
    }

    // Verify transaction status
    public async Task<TransactionStatusResponse?> VerifyTransactionAsync(string transactionReference, decimal originalAmount)
    {
        try
        {
            var amountInKobo = (originalAmount * 100).ToString("F0");
            var url = $"{_baseUrl}/collections/api/v1/gettransaction.json" +
                     $"?merchantcode={_merchantCode}" +
                     $"&transactionreference={transactionReference}" +
                     $"&amount={amountInKobo}";

            var response = await _httpClient.GetAsync(url);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
                throw new Exception($"Transaction verification failed: {responseContent}");
            
            var transactionStatus = JsonSerializer.Deserialize<TransactionStatusResponse>(responseContent, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            return transactionStatus;

        }
        catch (Exception ex)
        {
            throw new Exception($"Error verifying transaction: {ex.Message}");
        }
    }

    // Check if transaction was successful
    public bool IsTransactionSuccessful(TransactionStatusResponse response)
    {
        return response.ResponseCode == "00";
    }
    
    private class PaymentStateChangedSubscription(InterSwitchWebCheckoutService owner, EventCallback<PaymentResponse> callback) : IDisposable
    {
        public Task NotifyAsync(PaymentResponse response) => callback.InvokeAsync(response);
        public void Dispose() => owner._changeSubscriptions.Remove(this);
    }

    public void Dispose()
    {
        _dotNetObjectReference = null;
    }
}

// Request DTOs
// public class PaymentRequest
// {
//     public decimal Amount { get; set; }
//     public string TransactionReference { get; set; }
//     public string Description { get; set; }
//     public string CustomerName { get; set; }
//     public string CustomerEmail { get; set; }
//     public string CustomerId { get; set; }
//     public string CustomerPhone { get; set; }
//     public string RedirectUrl { get; set; }
//     
//     public string Secret { get; set; }
//     public string ClientId { get; set; }
// }

public class PaymentRedirectRequest
{
    public string MerchantCode { get; set; }
    public string PayItemId { get; set; }
    public string TransactionReference { get; set; }
    public string Amount { get; set; }
    public string Currency { get; set; }
    public string CustomerName { get; set; }
    public string CustomerEmail { get; set; }
    public string CustomerId { get; set; }
    public string PayItemName { get; set; }
    public string SiteRedirectUrl { get; set; }
    public string PostUrl { get; set; }
}

public class InlineCheckoutRequest
{
    public string MerchantCode { get; set; }
    public string PayItemId { get; set; }
    public string PayItemName { get; set; }
    public string TransactionReference { get; set; }
    public decimal Amount { get; set; }
    public int Currency { get; set; }
    public string? CustomerName { get; set; }
    public string? CustomerEmail { get; set; }
    public string CustomerId { get; set; }
    public string CustomerMobileNo { get; set; }
    public string SiteRedirectUrl { get; set; }
    public string TokeniseCard { get; set; }
    public string Mode { get; set; }
    public string Secret { get; set; }
    public string ClientId { get; set; }
    public string InlineCheckoutScript { get; set; }
}

// Response DTOs
public class TransactionStatusResponse
{
    public long Amount { get; set; }
    public string CardNumber { get; set; }
    public string MerchantReference { get; set; }
    public string PaymentReference { get; set; }
    public string RetrievalReferenceNumber { get; set; }
    public List<object> SplitAccounts { get; set; }
    public DateTime TransactionDate { get; set; }
    public string ResponseCode { get; set; }
    public string ResponseDescription { get; set; }
    public string AccountNumber { get; set; }
}

public class PaymentCallbackResponse
{
    public string Status { get; set; }
    public string TxnRef { get; set; }
    public string PayRef { get; set; }
    public string Amount { get; set; }
    public string ApprovalCode { get; set; }
    public string Rrn { get; set; }
}