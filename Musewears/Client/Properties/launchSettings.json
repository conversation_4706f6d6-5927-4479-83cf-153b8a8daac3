{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:44705", "sslPort": 44350}}, "profiles": {"Musewears": {"commandName": "Project", "launchBrowser": true, "inspectUri": "{wsProtocol}://{url.hostname}:{url.port}/_framework/debug/ws-proxy?browser={browserInspectUri}", "applicationUrl": "https://localhost:7135;http://localhost:5099", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "inspectUri": "{wsProtocol}://{url.hostname}:{url.port}/_framework/debug/ws-proxy?browser={browserInspectUri}", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Musewears.Client": {"commandName": "Project", "launchBrowser": true, "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}}}}