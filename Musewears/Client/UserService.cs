using System.Net.Http.Json;
using System.Security.Claims;
using System.Text.Json;
using Microsoft.AspNetCore.Components.Authorization;

namespace Musewears.Client;

// You can place this in a file like Claims.cs or in a 'Constants' folder
public static class Claims
{
    public const string Subject = "sub";  // Typically the user ID in OpenID Connect
    public const string Email = "email";  // Standard email claim
    public const string Role = "role";    // Role claim, often "role" in tokens
    // Add more claim types as needed
}

public class UserService(AuthenticationStateProvider authStateProvider, 
    HttpClient client, ILogger<UserService> logger)
{
    // private readonly AuthenticationStateProvider _authStateProvider = authStateProvider ?? throw new ArgumentNullException(nameof(authStateProvider));

    public async Task<UserInfo?> GetUserAsync()
    {
        // Get the current authentication state
        var authState = await authStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        // Check if the user is authenticated
        if (user.Identity?.IsAuthenticated != true)
        {
            return null;
        }

        if (user.Identity is not ClaimsIdentity identity)
        {
            return null;
        }
        
        logger.LogInformation("All claims:");
        foreach (var claim in user.Claims)
        {
            logger.LogInformation($"  {claim.Type}: {claim.Value}");
        }

        // Create and populate the UserInfo object
        var userInfo = new UserInfo
        {
            // IsAuthenticated = true,
            // NameClaimType = identity.NameClaimType,
            // RoleClaimType = identity.RoleClaimType,
            // Claims = user.Claims.Select(c => new ClaimValue(c.Type, c.Value)).ToList(),
            UserId = user.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? string.Empty,
            Email = user.FindFirst(ClaimTypes.Email)?.Value ?? string.Empty,
            Role = user.FindFirst(ClaimTypes.Role)?.Value,
            UserName = user.FindFirst(ClaimTypes.Name)?.Value
            // FullName = user.FindFirst(OpenIddictConstants.Claims.Name)?.Value,
            // FirstName = user.FindFirst(OpenIddictConstants.Claims.GivenName)?.Value,
            // LastName = user.FindFirst(OpenIddictConstants.Claims.FamilyName)?.Value,
            // PhoneNumber = user.FindFirst(OpenIddictConstants.Claims.PhoneNumber)?.Value
        };

        return userInfo;
    }
}