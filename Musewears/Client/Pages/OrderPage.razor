@page "/orders"
@using Musewears.Client.Services.External
@using Musewears.Shared.Models

@inject AuthenticationStateProvider AuthenticationStateProvider
@* @inject IWardrobe Wardrobe *@
@inject OrderingService Ordering

@if (_orders == null)
{
    <!-- Preloader, similar to the cart page -->
    <div id="preloader">
    <div class="jumper">
        <div></div>
        <div></div>
        <div></div>
    </div>
</div>
}
else
{
    <!-- Main Orders Area Start -->

    <div class="page-heading" id="top">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="orders-table-wrap">
                    <table class="orders-table">
                        <thead class="orders-table-head">
                            <tr class="table-head-row">
                                <th class="order-number">Order #</th>
                                <th class="order-date">Date</th>
                                <th class="order-total">Total</th>
                                <th class="order-status">Status</th>
                                <th class="order-details">Details</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var order in _orders)
                            {
                                <tr class="table-body-row">
                                    <td class="order-number">@order.OrderNumber</td>
                                    <td class="order-date">@order.Date.ToString("MM/dd/yyyy")</td>
                                    <td class="order-total">₦@(order.Total.ToString("N2"))</td>
                                    <td class="order-status">@order.Status</td>
                                    <td class="order-details"><a href="/order-details/@order.OrderNumber">View</a></td>
                                </tr>
                            }
                        </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Orders Area End -->
}

@code {
    private OrderRecord[]? _orders;

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userId = authState.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
        if (userId != null)
        {
            _orders = await Ordering.GetOrders();
            @* StateHasChanged(); *@
        }

    }
}