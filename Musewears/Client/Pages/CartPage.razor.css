/* Cart Page Styles */
.cart-container {
    width: 90%;
    max-width: 1200px;
    margin: 60px auto;
}

.cart-header {
    display: flex;
    justify-content: space-between;
    border-bottom: 2px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.cart-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.cart-image {
    width: 80px;
    height: 80px;
    overflow: hidden;
    margin-right: 20px;
}

.cart-image img {
    width: 100%;
    height: auto;
}

.cart-details {
    flex-grow: 1;
}

.cart-quantity {
    display: flex;
    align-items: center;
}

.cart-quantity input {
    width: 50px;
    text-align: center;
    margin: 0 10px;
    border: 1px solid #ccc;
}

.cart-total {
    font-weight: bold;
}

.cart-checkout {
    text-align: right;
    margin-top: 20px;
}

.cart-checkout button {
    padding: 10px 20px;
    background-color: #2a2a2a;
    color: #fff;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.cart-checkout button:hover {
    background-color: #444;
}

#total .main-border-button a {
    font-size: 13px;
    border: 1px solid #2a2a2a;
    padding: 12px 25px;
    display: inline-block;
    font-weight: 500;
    transition: all .3s;
    border-radius: 10px !important;
    background-color: #fff;
    color: #2a2a2a;
    /* Adding !important to ensure it takes effect */
}

#total .main-border-button a:hover {
    color: #fff;
    background-color: #2a2a2a;
    border-color: #fff;
}

.main-border-button input {
    font-size: 13px;
    border: 1px solid #fff;
    padding: 12px 25px;
    display: inline-block;
    font-weight: 500;
    transition: all .3s;
    border-radius: 10px !important;
    color: #fff;
    background-color: #2a2a2a;
    /* Adding !important to ensure it takes effect */
}

.main-border-button input:hover {
    background-color: #fff;
    border-color: #2a2a2a;
    color: #2a2a2a;
}