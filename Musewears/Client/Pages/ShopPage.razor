@page "/shop"
@* @rendermode InteractiveAuto *@

<Musewears.Client.Shared.Banner />


<!-- Page Banner Section Start-->
<!--<div class="page-banner-section section" style="background-image: url(img/bg/page-banner.jpg)">
    <div class="container">
        <div class="row">-->
<!-- Page Title Start -->
<!--<div class="page-title text-center col">
    <h1>shop page</h1>
</div>-->
<!-- Page Title End -->
<!--</div>
    </div>
</div>-->
<!-- Page Banner Section End-->

<ProductsSection />

<!-- jquery -->
<script src="assets/js/jquery-1.11.3.min.js"></script>
<!-- bootstrap -->
<script src="assets/bootstrap/js/bootstrap.min.js"></script>
<!-- count down -->
<script src="assets/js/jquery.countdown.js"></script>
<!-- isotope -->
<script src="assets/js/jquery.isotope-3.0.6.min.js"></script>
<!-- waypoints -->
<script src="assets/js/waypoints.js"></script>
<!-- owl carousel -->
<script src="assets/js/owl.carousel.min.js"></script>
<!-- magnific popup -->
<script src="assets/js/jquery.magnific-popup.min.js"></script>
<!-- mean menu -->
<script src="assets/js/jquery.meanmenu.min.js"></script>
<!-- sticker js -->
<script src="assets/js/sticker.js"></script>
<!-- main js -->
<script src="assets/js/main.js"></script>