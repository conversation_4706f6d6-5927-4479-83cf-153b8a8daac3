@page "/products"

@using Musewears.Shared
@using WebAppComponents.Catalog
@using WebAppComponents.Services
@* @rendermode RenderMode.InteractiveServer *@

@inject IJSRuntime JsRuntime
@* @inject IWardrobe wardrobe *@
@* @inject SquareHelper squareHelper *@
@inject CatalogService Catalog
@inject IProductImageUrlProvider ProductImages

@inject AuthenticationStateProvider AuthenticationStateProvider

@if (wears == null)
{
    @* <!-- ***** Preloader Start ***** --> *@
    <div id="preloader">
        <div class="jumper">
            <div></div>
            <div></div>
            <div></div>
        </div>
    </div>
    @* <!-- ***** Preloader End ***** --> *@
}
else
{
    <!-- ***** Main Banner Area Start ***** -->
    <div class="page-heading" id="top">
        <div class="container">
            @* <div class="row">
        <div class="col-lg-12">
        <div class="inner-content">
        <h2>Check Our Products</h2>
        <span>Awesome &amp; Creative HTML CSS layout by TemplateMo</span>
        </div>
        </div>
        </div> *@
        </div>
    </div>
    <!-- ***** Main Banner Area End ***** -->


    <!-- ***** Products Area Starts ***** -->
    <section class="section" id="products">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="section-heading">
                    <h2>Our Latest Products</h2>
                    <span>Check out all of our products.</span>
                </div>
            </div>
        </div>
    </div>
    <div class="container">
        <div class="row">
            @foreach (var wear in wears)
                {

                    <div class="col-lg-4">
                        <div class="item">
                            <div class="thumb">
                                <div class="hover-content">
                                    <ul>
                                        <li><a href=/product/@wear.Id><i class="fa fa-eye"></i></a></li>
                                        @* <li><a href=/product/@wear.Id><i class="fa fa-star"></i></a></li> *@
                                        <li><a href=/cart><i class="fa fa-shopping-cart"></i></a></li>
                                    </ul>
                                </div>
                                <img src="@ProductImages.GetProductImageUrl(wear)" alt="">
                            </div>
                            <div class="down-content">
                                <h4>@wear.Name</h4>
                                <span>@<EMAIL>("N2")</span>
                                <ul class="stars">
                                    @for (int i = 1; i <= 5; i++)
                                    {
                                        <li>
                                            @if (i <= (int)wear.Rating)
                                            {
                                                <i class="fa fa-star"></i> <!-- Full star -->
                                            }
                                            else if (i <= wear.Rating + 0.5m)
                                            {
                                                <i class="fa fa-star-half-o" style="margin-left: 5px;"></i> <!-- Half star -->
                                            }
                                            else
                                            {
                                                <i class=" fa fa-star-o" style="margin-left: 5px;"></i> <!-- Empty star -->
                                            }
                                        </li>
                                    }
                                    @* <li><i class="fa fa-star"></i></li>
                            <li><i class="fa fa-star"></i></li>
                            <li><i class="fa fa-star"></i></li>
                            <li><i class="fa fa-star"></i></li>
                            <li><i class="fa fa-star"></i></li> *@
                                </ul>
                            </div>
                        </div>
                    </div>
                }

                <div class="col-lg-12">
                    <div class="pagination">

                        @* <ul>
                    @for (int page = 1; page <= totalPages; page++)
                    {
                    <li class="@((page == currentPage) ? "active" : "")">
                    <a href="javascript:void(0)" @onclick="() => SelectPage(page)">page</a>
                    </li>
                    }
                    @if (currentPage < totalPages)
                    {
                    <li>
                    <a href="javascript:void(0)" @onclick="() => SelectPage(currentPage + 1)">></a>
                    </li>
                    }
                    </ul> *@
                        <ul>

                            @if (currentPage > 1)
                            {
                                <li>
                                    <a href="javascript:void(0)" @onclick="() => LoadPage(currentPage - 1)">&lt;</a>
                                </li>
                            }
                            <li class="@((currentPage == 1) ? "active" : "")">
                                <a href="javascript:void(0)" @onclick="() => LoadPage(1)">1</a>
                            </li>
                            <li class="@((currentPage == 2) ? "active" : "")">
                                <a href="javascript:void(0)" @onclick="() => LoadPage(2)">2</a>
                            </li>
                            <li class="@((currentPage == 3) ? "active" : "")">
                                <a href="javascript:void(0)" @onclick="() => LoadPage(3)">3</a>
                            </li>


                            <li>...</li>


                            <li class="">
                                <a href="javascript:void(0)" @onclick="() => LoadPage(currentPage + 10)">@(currentPage +
                                10)</a>
                            </li>

                            @* @if (currentPage > 3)
                        {
                        <li>...</li>


                        <li class="active">
                        <a href="javascript:void(0)" @onclick="() => LoadPage(currentPage)">@currentPage</a>
                        </li>
                        } *@
                            @* Assuming you have a way to determine if there is a next page *@
                            @if (hasMoreItems)
                            {
                                <li>
                                    <a href="javascript:void(0)" @onclick="() => LoadPage(currentPage + 1)">&gt;</a>
                                </li>
                            }
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- ***** Products Area Ends ***** -->

}


@code {
    private List<CatalogItem>? wears;

    private int currentPage = 1; // Current page number
    private int pageSize = 21; // Number of items per page

    private int totalPages = 100; // Number of items per page
    private bool hasMoreItems = true; // Flag to check if there are more items to load

    //protected override void OnInitialized()
    //{
    // base.OnInitialized();
    //}

    @* protected override Task OnInitializedAsync()
{
//await jsRuntime.InvokeAsync<string>("console.log", "OnInitializedAsync");

return LoadPage(currentPage);
} *@

    protected override async Task OnInitializedAsync()
    {
        
        var items = Catalog.GetCatalogItems(pageIndex: 0, pageSize: 12, brand: null, type: null);
        // var itemTypesTask = Catalog.GetTypes();
        await Task.WhenAll(items);
        
        wears = items.Result.Data;
    }


    @* protected override Task OnAfterRenderAsync(bool firstRender) *@
    @* { *@
    @*     if (firstRender) *@
    @*     { *@
    @*         $1$ await JSRuntime.InvokeVoidAsync("owl-item"); #1# *@
    @* *@
    @*         return LoadPage(currentPage); *@
    @* *@
    @*     } *@
    @* *@
    @*     return Task.CompletedTask; *@
    @* } *@


    private Task SelectPage(int page)
    {
        if (page < 1 || page > totalPages)
            return Task.CompletedTask;

        return LoadPage(page);
    }

    private async Task LoadPage(int page)
    {
        currentPage = page;
        var skip = (currentPage - 1) * pageSize;
        var newItems = await Catalog.GetCatalogItems(pageIndex: skip, pageSize: pageSize, brand: null, type: null);


        if (newItems.Count == 0)
        {
            return;
        }

        hasMoreItems = newItems.Count >= pageSize;
        
        wears = newItems.Data;

        @* wears.AddRange(newItems); *@
        StateHasChanged();


        // Scroll to the top of the page
        await JsRuntime.InvokeVoidAsync("window.scrollTo", 0, 0);
    }


    private async Task LoadMoreItems()
    {
        if (hasMoreItems)
        {
            await LoadPage(currentPage);
        }
    }

}