@page "/product/{WearId:int?}"

@using Musewears.Client.Services.External
@using Musewears.Shared
@using WebAppComponents.Catalog
@using WebAppComponents.Services
@using Musewears.Client.Components.Product

@* @inject IWardrobe wardrobe *@

@inject AuthenticationStateProvider AuthenticationStateProvider

@inject IToastService Toast
@inject BasketState Basket
@inject CatalogService Catalog
@inject IProductImageUrlProvider ProductImages

@* @rendermode RenderMode.InteractiveServer *@

@* @model Wear *@

<!-- ***** Main Banner Area Start ***** -->
<div class="page-heading" id="top">
    <div class="container">
        @* <div class="row">
            <div class="col-lg-12">
            <div class="inner-content">
            <h2>Single Product Page</h2>
            <span>Awesome &amp; Creative HTML CSS layout by TemplateMo</span>
            </div>
            </div>
            </div> *@
    </div>
</div>
<!-- ***** Main Banner Area End ***** -->
<!-- ***** Product Area Starts ***** -->
<section class="section" id="product">
    <div class="container">
        @if (Item == null)
        {
            @* <!-- ***** Preloader Start ***** --> *@
            <div id="preloader">
                <div class="jumper">
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
            </div>
            @* <!-- ***** Preloader End ***** --> *@
        }
        else
        {
            <div class="row">
                <div class="col-lg-8">
                    <div class="left-images">
                        <div class="carousel-wrapper" style="height: 650px; width: 100%; max-width:800px; margin:auto;">
                            <MudCarousel 
                                @ref="_carouselRef"
                                TData="CatalogItemImage"
                                AutoCycle="false"
                                ShowArrows="false"
                                ShowBullets="true"
                                EnableSwipeGesture="true"
                                BulletsColor="Color.Dark"
                                @bind-SelectedIndex="_currentIndex"
                                Style="height:100%; margin: auto;">
                                @foreach (var image in Item?.Images ?? [])
                                {
                                    <MudCarouselItem>
                                        <img src="@ProductImages.GetProductImageUrl(image)"
                                             alt="@image.ImageUrl"
                                             style="max-width: 100%; max-height: 100%; object-fit: contain; object-position: top center; display:block;" />
                                    </MudCarouselItem>
                                }
                            </MudCarousel>
                        
                            <!-- Custom Left Button -->
                            <button class="carousel-btn left" @onclick="() => _carouselRef?.Previous()">
                                <MudIcon Icon="@Icons.Material.Filled.ChevronLeft" />
                            </button>

                            <!-- Custom Right Button -->
                            <button class="carousel-btn right" @onclick="() => _carouselRef?.Next()">
                                <MudIcon Icon="@Icons.Material.Filled.ChevronRight" />
                            </button>
                            
                            <!-- ADDED: Thumbnail container -->
                            <div class="thumbnails-container">
                                @if (Item?.Images != null)
                                {
                                    @foreach (var (image, index) in Item.Images.Select((img, idx) => (img, idx)))
                                    {
                                        <div class="thumbnail @(index == _currentIndex ? "active" : "")" 
                                             @onclick="() => SelectImage(index)">
                                            <img src="@ProductImages.GetProductImageUrl(image)" 
                                                 alt="Thumbnail @index" />
                                        </div>
                                    }
                                }
                            </div>
                            
                            
                        </div>

                        
                        <!-- Owl carousel wrapper: -->
                        @* <div id="productImagesCarousel" class="owl-carousel owl-theme"> *@
                        @*     @foreach (var image in Item.Images ?? []) *@
                        @*     { *@
                        @*         <div class="item"> *@
                        @*             <img src="@ProductImages.GetProductImageUrl(image)" *@
                        @*                  alt="@image.ImageUrl" /> *@
                        @*         </div> *@
                        @*     } *@
                        @* </div> *@
                        @* <script> *@
                        @*     window.setTimeout(function() { *@
                        @*         if (window.initOwlCarousel) window.initOwlCarousel(); *@
                        @*     }, 100); *@
                        @* </script> *@
                        @* <img src=@ProductImages.GetProductImageUrl(Item!.Images!.First()) alt="@Item.Name"> *@
                        @* <img src="assets/images/single-product-01.jpg" alt="">
                            <img src="assets/images/single-product-02.jpg" alt=""> *@
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="right-content">
                        <h4>@Item.Name</h4>
                        <span class="price">@Item?.Currency@Item?.Price.ToString("N2")</span>
                        <ul class="stars">
                            @for (var i = 1; i <= 5; i++)
                            {
                                <li>
                                    @if (i <= Item!.Rating)
                                    {
                                        <i class="fa fa-star"></i> <!-- Full star -->
                                    }
                                    else if (i <= Item.Rating + 0.5m)
                                    {
                                        <i class="fa fa-star-half-o" style="margin-left: 5px;"></i> <!-- Half star -->
                                    }
                                    else
                                    {
                                        <i class=" fa fa-star-o" style="margin-left: 5px;"></i> <!-- Empty star -->
                                    }
                                </li>
                            }
                        </ul>
                        <span>@Item?.Description</span>
                        <div class="quote">
                            <i class="fa fa-quote-left"></i>
                            <p>@Item?.Description</p>
                        </div>

                        <div class="quantity-content">
                            <div class="left-content">
                                <h6>No. of Orders</h6>
                            </div>
                            <div class="right-content">
                                <div class="quantity buttons_added">
                                    <input type="button" value="-" class="minus" @onclick=@DecreaseQuantity>
                                    <input type="number" step="1" min="1" max="" name="quantity" @bind="@Quantity" @bind:event="oninput"
                                           @onchange="@(e => SetQuantity(e))"
                                           title="Qty"
                                           class="input-text qty text" size="4" pattern="" inputmode="">
                                    <input type="button"
                                           value="+" class="plus" @onclick=@IncreaseQuantity>
                                </div>
                            </div>
                        </div>
                        @* <div class="quantity-content"> *@
                        @*     <div class="left-content"> *@
                        @*         <h6>Colors</h6> *@
                        @*     </div> *@
                        @*     <div class="right-content"> *@
                        @*         <div class="quantity buttons_added"> *@
                        @*             @foreach (var color in Item?.Colors ?? []) *@
                        @*             { *@
                        @*                 <button style="background-color: @color.ColorCode;"> *@
                        @*                 </button> *@
                        @*             } *@
                        @*         </div> *@
                        @*     </div> *@
                        @* </div> *@
                        
                        <VariantSelector Colors=@Item?.Colors
                                         Sizes=@Item?.Sizes
                                         Variants=@Item?.Variants
                                         OnColorSelected=@OnColorSelected
                                         OnSizeSelected=@OnSizeSelected
                                         OnVariantSelected=@OnVariantSelected />
                        <div class="total">
                            <h4>Total: @Total</h4>
                            
                            <div class="main-border-button"><a @onclick=@AddToCart @onclick:preventDefault>Add To Cart</a></div>
                            
                            @* @if (cart != null && cart.Quantity == Quantity) *@
                            @* { *@
                            @*     <div class="main-border-button"><a href="/cart">View Cart</a></div> *@
                            @* } *@
                            @* else *@
                            @* { *@
                            @*     <div class="main-border-button"><a @onclick=@AddToCart @onclick:preventDefault>Add To Cart</a></div> *@
                            @* } *@
                        </div>




                    </div>
                </div>
            </div>
        }

    </div>
</section>
<!-- ***** Product Area Ends ***** -->

<BlazoredToasts Position="ToastPosition.BottomRight" Timeout="5" ShowProgressBar="false" /> 

@code {

    //[Inject]
    //protected NotificationService NotificationService { get; set; }

    [Parameter] public int? WearId { get; set; }

    private CatalogItem? Item { get; set; }
    
    private CatalogItemColor? SelectedColor { get; set; }
    private CatalogItemSize? SelectedSize { get; set; }
    private CatalogItemVariant? SelectedVariant { get; set; }

    // public BasketItem? cart { get; set; }

    //private int quantity;

    private int Quantity { get; set; } = 1;

    private string Total
    {
        get
        {
            var total = Quantity * (Item?.Price ?? 0);

            return $"{Item?.Currency}{total:N2}";
        }
    }
    
    private MudCarousel<CatalogItemImage>? _carouselRef;

    // ADDED: Current index tracking
    private int _currentIndex;
    
    // ADDED: Image selection method
    private void SelectImage(int index)
    {
        _currentIndex = index;
        _carouselRef?.MoveTo(index);
    }
    
    protected override async Task OnInitializedAsync()
    {

        if (WearId != null)
        {

            try
            {
                Item = await Catalog.GetCatalogItem((int)WearId);


                // var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
                // var userId = authState.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                //
                // if (!string.IsNullOrEmpty(userId))
                // {
                //     // get product cart for this user
                //     cart = await Basket.GetCartItem(WearId, userId);
                //
                //
                // }

                // Quantity = cart?.Quantity ?? 1;



                @* Console.WriteLine($"Got it"); *@

                StateHasChanged();

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }




        }
    }


    private void IncreaseQuantity()
    {
        @* Console.WriteLine($"Quantity: {Quantity}"); *@
        Quantity += 1;
        StateHasChanged();
    }

    private void SetQuantity(ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out var newQuantity) && newQuantity > 0)
        {
            Quantity = newQuantity;
            // Perform additional actions here if necessary
            StateHasChanged();
        }
        else
        {
            // Handle the error case
        }
    }


    private void DecreaseQuantity()
    {
        if (Quantity > 1)
        {
            Quantity -= 1;
            StateHasChanged();
        }
    }
    
    
    // Check stock before adding to cart
    private bool CanAddToCart() => 
        // SelectedVariant != null && 
        !(Item?.Colors?.Any() == true && SelectedColor == null) &&
        !(Item?.Sizes?.Any() == true && SelectedSize == null) &&
        Quantity > 0 &&
        Quantity <= CurrentAvailableStock();
    // quantity <= SelectedVariant?.AvailableStock;
    
    private int CurrentAvailableStock() => SelectedVariant?.AvailableStock ?? Item?.AvailableStock ?? 0;
    
    private async Task AddToCart()
    {
        if (Item == null)
        {
            Toast.ShowWarning("Item not found");
            return;
        }
        
        if (!CanAddToCart())
        {
            Toast.ShowWarning("Select a color and size");
            return;
        }

        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userId = authState.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
        if (!string.IsNullOrEmpty(userId))
        {
            try
            {
                await Basket.AddAsync(Item, quantity: Quantity);

                Toast.ShowSuccess("Successfully added to cart");
            }
            catch (Exception e)
            {
                Toast.ShowSuccess($"Failed to add to cart :: {e.Message}");
            }
        }
        else
        {
            Toast.ShowWarning("Login to add items to cart");

            //NotificationService.Notify(new NotificationMessage { Severity = NotificationSeverity.Warning, Summary = "Not logged in", Detail = "Login to add items to cart", Duration = 3000 });
        }

        @* if (Quantity > 0 && WearId != null) *@
        @* { *@
        @*     var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync(); *@
        @*     var userId = authState.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value; *@
        @*     if (!string.IsNullOrEmpty(userId)) *@
        @*     { *@
        @*         await Basket.AddAsync(Item); *@
        @* *@
        @*         $1$ if (await wardrobe.AddOrUpdateToUserCart(WearId, userId, Photo: Item?.Photo, Name: Item?.Name, Quantity: Quantity)) #1# *@
        @*         $1$ { #1# *@
        @*         $1$     cart = new CartItem #1# *@
        @*         $1$     { #1# *@
        @*         $1$         UserId = userId, #1# *@
        @*         $1$         WearId = WearId, #1# *@
        @*         $1$         Quantity = Quantity, #1# *@
        @*         $1$         Name = Item?.Name, #1# *@
        @*         $1$         Photo = Item?.Photo, #1# *@
        @*         $1$     }; #1# *@
        @*         $1$ #1# *@
        @*         $1$     $2$ quantity = 0; #2# #1# *@
        @*         $1$ } #1# *@
        @*     } *@
        @*     else *@
        @*     { *@
        @*         Toast.ShowWarning("Login to add items to cart"); *@
        @* *@
        @*         //NotificationService.Notify(new NotificationMessage { Severity = NotificationSeverity.Warning, Summary = "Not logged in", Detail = "Login to add items to cart", Duration = 3000 }); *@
        @*     } *@
        @* } *@
        @* else if (Quantity > 0 && WearId != null) *@
        @* { *@
        @*     // cart.Quantity = Quantity; *@
        @*     $1$ quantity = 0; #1# *@
        @*     await wardrobe.AddOrUpdateToUserCart(WearId, cart.UserId, Photo: Item?.Photo, Name: Item?.Name, Quantity); *@
        @* } *@
        @* else *@
        @* { *@
        @* *@
        @*     Toast.ShowInfo("Login to add items to cart"); *@
        @* *@
        @*     //NotificationService.Notify(new NotificationMessage { Severity = NotificationSeverity.Warning, Summary = "Not logged in", Detail = "Login to add items to cart", Duration = 3000 }); *@
        @* } *@

    }

    private void OnColorSelected(CatalogItemColor? color)
    {
        SelectedColor = color;
    }

    private void OnSizeSelected(CatalogItemSize? size)
    {
        SelectedSize = size;
    }

    private void OnVariantSelected(CatalogItemVariant variant)
    {
        SelectedVariant = variant;
    }

}