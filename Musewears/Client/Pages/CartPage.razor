@page "/cart"
@* @rendermode InteractiveServer *@

@using Blazored.Toast.Services
@using Musewears.Shared.Models
@using WebAppComponents.Services
@using Musewears.Client.Components
@using Musewears.Client.Services
@using Musewears.Client.Services.External

@inject AuthenticationStateProvider AuthenticationStateProvider
@* @inject IWardrobe wardrobe *@
@* @inject CatalogService Catalog *@
@inject MusewearDialogService DialogService
@inject BasketState Basket

@inject IToastService Toast
@inject IProductImageUrlProvider ProductImages

@*@inject IToastService ToastService*@

@* Cart page adapted from the home page design *@

@if (_basketItems == null)
{
    <!-- Preloader, similar to the home page -->
    <div id="preloader">
    <div class="jumper">
        <div></div>
        <div></div>
        <div></div>
    </div>
</div>
}
else
{
    <!-- Main Cart Area Start -->
    <div class="page-heading" id="top">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 col-md-12">
                <div class="cart-table-wrap">
                    <table class="cart-table">
                        <thead class="cart-table-head">
                            <tr class="table-head-row">
                                <th class="product-remove"></th>
                                <th class="product-image">Product Image</th>
                                <th class="product-name">Name</th>
                                <th class="product-price">Price</th>
                                <th class="product-quantity">Quantity</th>
                                <th class="product-total">Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var cart in _basketItems)
                                {

                                    <tr class="table-body-row">
                                        <td class="product-remove"><a @onclick=@(() => DeleteCartItem(cart!))
                                            @onclick:preventDefault><i class="fa fa-trash"></i></a></td>
                                        <td class="product-image">
                                            <img src="@ProductImages.GetProductImageUrl(cart.ProductId)" alt=""
                                                style="max-width: 180px; height: 80px; object-fit: contain;">
                                        </td>
                                        <td class=" product-name">@cart.ProductName</td>
                                        <td class="product-price">@<EMAIL>("N2")</td>
                                        <td class="product-quantity">
                                            <input type="number" min="1" @bind="@cart!.Quantity" @bind:event="oninput"
                                                placeholder="0" @onchange="@(e =>
                                        UpdateQuantity(cart!, e))">
                                        </td>
                                        <td class="product-total">@TotalCartAmount(cart)</td>
                                    </tr>
                                }
                                @*<tr class="table-body-row">
                            <td class="product-remove"><a href="#"><i class="far fa-window-close"></i></a></td>
                            <td class="product-image">
                            <img src="assets/images/single-product-01.jpg" alt=""
                            style="max-width: 180px; height: 80px; object-fit: cover;">
                            </td>
                            <td class=" product-name">Strawberry</td>
                            <td class="product-price">$85</td>
                            <td class="product-quantity"><input type="number" placeholder="0"></td>
                            <td class="product-total">1</td>
                            </tr>
                            <tr class="table-body-row">
                            <td class="product-remove"><a href="#"><i class="far fa-window-close"></i></a></td>
                            <td class="product-image">
                            <img src="assets/images/single-product-02.jpg" alt=""
                            style="max-width: 180px; height: 80px; object-fit: cover;">
                            </td>
                            <td class="product-name">Berry</td>
                            <td class="product-price">$70</td>
                            <td class="product-quantity"><input type="number" placeholder="0"></td>
                            <td class="product-total">1</td>
                            </tr>
                            <tr class="table-body-row">
                            <td class="product-remove"><a href="#"><i class="far fa-window-close"></i></a></td>
                            <td class="product-image">
                            <img src="assets/images/single-product-01.jpg" alt=""
                            style="max-width: 180px; height: 80px; object-fit: cover;">
                            </td>
                            <td class="product-name">Lemon</td>
                            <td class="product-price">$35</td>
                            <td class="product-quantity"><input type="number" placeholder="0"></td>
                            <td class="product-total">1</td>
                            </tr>*@
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="total-section" id="total">
                        <table class="total-table">
                            <thead class="total-table-head">
                                <tr class="table-total-row">
                                    <th>Total</th>
                                    <th>Price</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="total-data">
                                    <td><strong>Subtotal: </strong></td>
                                    <td>₦@Subtotal.ToString("N2")</td>
                                </tr>
                                <tr class="total-data">
                                    <td><strong>Shipping: </strong></td>
                                    <td>₦@(ShippingFee.ToString("N2"))</td>
                                </tr>
                                <tr class="total-data">
                                    <td><strong>Total: </strong></td>
                                    <td>₦@Total.ToString("N2")</td>
                                </tr>
                            </tbody>
                        </table>


                        <div class="main-border-button" style="margin-top: 30px;">
                            <a class="boxed-btn" style="margin-right: 15px;" @onclick=@(() => UpdateCartItems())
                            @onclick:preventDefault>Update Cart</a>

                            <a href="/checkout" class="boxed-btn black">Check Out</a>

                        </div>

                        @* <div class="cart-buttons">

                    <div class="main-border-button">
                    <a href="cart.html" class="boxed-btn">Update Cart</a>
                    </div>
                    <div class="main-border-button">
                    <a href="checkout.html" class="boxed-btn black">Check Out</a>
                    </div>
                    </div> *@
                    </div>

                    <div class="coupon-section">
                        <h3>Apply Coupon</h3>
                        <div class="coupon-form-wrap" style="margin-top: 12px;">
                            <form action="">
                                <p><input type="text" placeholder="Coupon"></p>

                                <div class="main-border-button" style="margin-top: 25px;">
                                    <input type="submit" value="Apply" disabled>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Main Cart Area End -->

    <ModalDialog DialogType="ModalDialog.ModalDialogType.DeleteCancel" />

    <BlazoredToasts Position="ToastPosition.BottomRight" Timeout="5" ShowProgressBar="false" />

}

@code {

    //[Inject]
    //protected static IWardrobe? wardrobe { get; set; }

    //[Inject]
    //protected static IOrderService? OrderService { get; set; }

    private IReadOnlyCollection<BasketItem>? _basketItems;

    private static decimal ShippingFee => 10000;

    private decimal Subtotal => _basketItems?.Sum(x => x.UnitPrice * x.Quantity) ?? 0;

    private decimal Total => ShippingFee + Subtotal;
    
    IDisposable? _basketStateSubscription;

    private async Task DeleteCartItem(BasketItem cart)
    {
        if (await DialogService.ShowDialog($"Delete {cart.ProductName}?", "Are you sure you want to delete this item?",
                ModalDialog.ModalDialogType.DeleteCancel))
        {
            // if (await Basket.RemoveCartItem(cart))
            // {
            //     _basketItems?.Remove(cart);
            //     StateHasChanged();
            // }
        }
    }

    private async Task UpdateCartItems()
    {
        if (_basketItems != null)
        {
            if (await Basket.UpdateBasketAsync(_basketItems))
            {
                Console.WriteLine("Cart items updated successfully");
                StateHasChanged();

                Toast.ShowWarning("Cart Updated");

            }
        }
    }

    private static string TotalCartAmount(BasketItem cart)
    {

        return $"{cart.Currency}{cart.Quantity * cart.UnitPrice:N2}";
    }

    private void UpdateQuantity(BasketItem cart, ChangeEventArgs e)
    {

        if (int.TryParse(e.Value?.ToString(), out var newQuantity))
        {
            cart.Quantity = newQuantity;
            // Perform additional actions here if necessary
            StateHasChanged();
        }
        else
        {
            // Handle the error case
        }

    }

    protected override async Task OnInitializedAsync()
    {
        // The basket contents may change during the lifetime of this component (e.g., when an item is
        // added during the current request). If this EventCallback is invoked, it will cause this
        // component to re-render with the updated data.
        _basketStateSubscription = Basket.NotifyOnChange(
            EventCallback.Factory.Create(this, UpdateBasketItemsAsync));

        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userId = authState.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
        if (userId != null)
        {
            await UpdateBasketItemsAsync();
            @* StateHasChanged(); *@
        }
    }
    
    
    private async Task UpdateBasketItemsAsync()
    {
        _basketItems = await Basket.GetBasketItemsAsync();
    }
    
    
    public void Dispose()
    {
        _basketStateSubscription?.Dispose();
    }

    @* protected override async Task OnAfterRenderAsync(bool firstRender)
{
if (firstRender)
{
// Pass a DotNetObjectReference to this component instance to JavaScript
await JSRuntime.InvokeVoidAsync("setPaymentHandler", DotNetObjectReference.Create(this));
}
} *@

    @* private async Task MakePayment()
{

var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
var userId = authState.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

if (items == null)
{

toastService.ShowWarning("You have no items in your cart");
return;
}

if (userId == null)
{

toastService.ShowWarning("Please login to proceed");
return;
}

var order = await OrderService.CreateOrder(userId, items!);

if (order == null)
{

toastService.ShowError("Unable to create order");
return;
}

var paymentRequest = new PaymentRequest
{
merchant_code = "MX190087",
pay_item_id = "6101231",
txn_ref = "sample_txn_ref_123309",
site_redirect_url = "https://musewears.onrender.com/",
amount = order.TotalAmount,
currency = (int)CurrencyCode.NGN, // ISO 4217 numeric code of the currency used
mode = "TEST"
};

await JSRuntime.InvokeVoidAsync("initiatePayment", paymentRequest, order.Id, order.UserId);
} *@

    // This method must be static and be decorated with [JSInvokable]

    /*
    [JSInvokable]
    public Task OnPaymentResponse(object json, string orderId, string userId)
    {
    var response = JsonConvert.DeserializeObject<PaymentResponse>($"{json}");
    Console.WriteLine($"Payment Response: {response?.resp}");

    // Handle the payment response here
    if (response == null)
    {

    return Task.CompletedTask;
    }


    if (response.resp == PaymentResponseCode.Approved || response.resp == PaymentResponseCode.ApprovedPartial ||
    response.resp == PaymentResponseCode.ApprovedVIP || response.resp == PaymentResponseCode.InProgress)
    {
    // If payment is approved, update the order status as completed, if not, update the order status as pending
    var status = (response.resp == PaymentResponseCode.Approved || response.resp == PaymentResponseCode.ApprovedPartial ||
    response.resp == PaymentResponseCode.ApprovedVIP) ? PaymentStatus.completed : PaymentStatus.pending;

    // Update the payment status of the order
    return OrderService.UpdateOrderPaymentStatus(orderId: orderId, userId: userId, status: status);

    }


    return Task.CompletedTask;

    // Deserialize the payment response if necessary
    @* var response = JsonSerializer.Deserialize<PaymentResponse>(paymentResponse.ToString()); *@

    // Implement the logic to update the order based on the response
    @* await OrderService.UpdateOrderStatus(response.OrderId, "Paid"); // Replace with actual order update logic *@
    }
    */

}

<!-- Page Banner Section Start-->
@* <div class="page-banner-section section" style="background-image: url(img/bg/page-banner.jpg)">
<div class="container">
<div class="row">

<!-- Page Title Start -->
<div class="page-title text-center col">
<h1>Cart</h1>
</div><!-- Page Title End -->

</div>
</div>
</div><!-- Page Banner Section End-->
<!-- Cart Section Start-->
<div class="cart-section section pt-120 pb-90">
<div class="container">
<div class="row">
<div class="col-12">

<div class="table-responsive mb-30">
<table class="table cart-table text-center">

<!-- Table Head -->
<thead>
<tr>
<th class="number">#</th>
<th class="image">image</th>
<th class="name">product name</th>
<th class="qty">quantity</th>
<th class="price">price</th>
<th class="total">totle</th>
<th class="remove">remove</th>
</tr>
</thead>

<!-- Table Body -->
<tbody>
<tr>
<td><span class="cart-number">1</span></td>
<td><a href="#" class="cart-pro-image"><img src="img/product/1.jpg" alt="" /></a></td>
<td><a href="#" class="cart-pro-title">Holiday Candle</a></td>
<td>
<div class="product-quantity">
<input type="text" value="0" name="qtybox">
</div>
</td>
<td>
<p class="cart-pro-price">$104.99</p>
</td>
<td>
<p class="cart-price-total">$104.99</p>
</td>
<td><button class="cart-pro-remove"><i class="fa fa-trash-o"></i></button></td>
</tr>
<tr>
<td><span class="cart-number">2</span></td>
<td><a href="#" class="cart-pro-image"><img src="img/product/2.jpg" alt="" /></a></td>
<td><a href="#" class="cart-pro-title">Christmas Tree</a></td>
<td>
<div class="product-quantity">
<input type="text" value="0" name="qtybox">
</div>
</td>
<td>
<p class="cart-pro-price">$85.99</p>
</td>
<td>
<p class="cart-price-total">$85.99</p>
</td>
<td><button class="cart-pro-remove"><i class="fa fa-trash-o"></i></button></td>
</tr>
</tbody>
</table>
</div>

<div class="row">

<!-- Cart Action -->
<div class="cart-action col-lg-4 col-md-6 col-12 mb-30">
<a href="#" class="button">Continiue Shopping</a>
<button class="button">update cart</button>
</div>

<!-- Cart Cuppon -->
<div class="cart-cuppon col-lg-4 col-md-6 col-12 mb-30">
<h4 class="title">Discount Code</h4>
<p>Enter your coupon code if you have</p>
<form action="#" class="cuppon-form">
<input type="text" placeholder="Cuppon Code">
<button class="button">apply coupon</button>
</form>
</div>

<!-- Cart Checkout Progress -->
<div class="cart-checkout-process col-lg-4 col-md-6 col-12 mb-30">
<h4 class="title">Process Checkout</h4>
<p><span>Subtotal</span><span>$190.98</span></p>
<h5><span>Grand total</span><span>$190.98</span></h5>
<button class="button">process to checkout</button>
</div>

</div>

</div>
</div>
</div>
</div><!-- Cart Section End--> *@
