/* ADDED: Thumbnail styles */
.thumbnails-container {
    display: flex;
    justify-content: center;
    gap: 8px;
    padding: 10px;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 10;
}

.thumbnail {
    width: 60px;
    height: 60px;
    border: 2px solid transparent;
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
}

.thumbnail:hover {
    border-color: #ccc;
}

.thumbnail.active {
    border-color: #000;
    box-shadow: 0 0 5px rgba(0,0,0,0.5);
}

.thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.carousel-wrapper {
    position: relative;
    width: 100%;
    max-width: 100%;
    /* If you push bullets below, add extra bottom padding: */
    padding-bottom: 80px; /* space for bullets */
    /* height can be fixed or responsive as before */
    height: 900px;
    margin: auto;
    overflow: visible; /* ensure bullets aren’t clipped if outside */
    bottom: 10px;
}

.carousel-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.5);
    border: none;
    color: white;
    padding: 12px;
    border-radius: 50%;
    z-index: 10;
    cursor: pointer;
}

.carousel-btn.left {
    left: 10px;
}

.carousel-btn.right {
    right: 10px;
}

.carousel-btn:hover {
    background-color: rgba(0, 0, 0, 0.8);
}

/* Position bullets container */
.carousel-wrapper .mud-carousel-bullets {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: -20px; /* adjust: negative to push fully below, or positive e.g. 10px to overlap slightly */
    display: flex;
    gap: 4px;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 4px 8px;
    border-radius: 12px;
    z-index: 10;
}

/* Optionally style individual bullet indicators */
.carousel-wrapper .mud-carousel-bullets .mud-icon-button {
    color: white;
}

.carousel-wrapper .mud-carousel-bullets .mud-icon-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}