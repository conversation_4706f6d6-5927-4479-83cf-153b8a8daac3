using System.Diagnostics.CodeAnalysis;
using Grpc.Net.Client.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Musewears.Client.Services.External;
using WebAppComponents.Services;
using BasketClient = Basket.API.Grpc.Basket.BasketClient;

namespace Musewears.Client.Extensions;

public static class Extensions
{
    public static bool IsNullOrEmpty<T>([NotNullWhen(false)]this IEnumerable<T>? source)
    {
        return source == null || !source.Any();
    }
    
    public static void AddApplicationServices(this WebAssemblyHostBuilder builder)
    {
        // builder.AddAuthenticationServices();

        // Application services
        builder.Services.AddScoped<BasketState>();
        // builder.Services.AddScoped<LogOutService>();
        builder.Services.AddSingleton<BasketService>();
        builder.Services.AddSingleton<IProductImageUrlProvider, ProductImageUrlProvider>();

        // HTTP and GRPC client registrations
        builder.Services.AddGrpcClient<BasketClient>(o => o.Address = new Uri(builder.HostEnvironment.BaseAddress))
            .AddAuthToken()
            .ConfigurePrimaryHttpMessageHandler(() => new GrpcWebHandler(GrpcWebMode.GrpcWeb, new HttpClientHandler()))

            // .AddServiceDiscovery() // Critical for Aspire integration
            // .EnableCallContextPropagation() // For auth token propagation
            ;

        builder.Services.AddHttpClient<CatalogService>(o => o.BaseAddress = new Uri(builder.HostEnvironment.BaseAddress))
            .AddApiVersion(2.0)
            .AddAuthToken()
            ;

        builder.Services.AddHttpClient<OrderingService>(o => o.BaseAddress = new Uri(builder.HostEnvironment.BaseAddress))
            .AddApiVersion(1.0)
            .AddAuthToken()
            ;
    }
}