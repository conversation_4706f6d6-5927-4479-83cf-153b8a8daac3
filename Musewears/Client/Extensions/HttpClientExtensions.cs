using System.Net.Http.Headers;
using Microsoft.AspNetCore.Components.WebAssembly.Authentication;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Musewears.Client.Extensions;

public static class HttpClientExtensions
{
    public static IHttpClientBuilder AddAuthToken(this IHttpClientBuilder builder)
    {
        // builder.Services.AddHttpContextAccessor();

        // builder.Services.TryAddTransient<HttpClientAuthorizationDelegatingHandler>();

        // builder.AddHttpMessageHandler<HttpClientAuthorizationDelegatingHandler>();
        
        
        // builder.Services.AddScoped<CustomTokenHandler>();
        //
        // builder.AddHttpMessageHandler<CustomTokenHandler>();

        // builder.AddHttpMessageHandler<BaseAddressAuthorizationMessageHandler>();

        return builder;
    }
    
internal class CustomTokenHandler(IAccessTokenProvider tokenProvider) : DelegatingHandler
    {
        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            var tokenResult = await tokenProvider.RequestAccessToken();
            if (tokenResult.TryGetToken(out var token))
            {
                // Add the token to the Authorization header
                Console.WriteLine("Token: " + token.Value);
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token.Value);
            }
            else
            {
                // Handle the case when the token is not available
                Console.WriteLine("Token not available");
            }
            return await base.SendAsync(request, cancellationToken);
        }
    }
    

    // private class HttpClientAuthorizationDelegatingHandler : DelegatingHandler
    // {
    //     private readonly IHttpContextAccessor _httpContextAccessor;
    //
    //     public HttpClientAuthorizationDelegatingHandler(IHttpContextAccessor httpContextAccessor)
    //     {
    //         _httpContextAccessor = httpContextAccessor;
    //     }
    //
    //     public HttpClientAuthorizationDelegatingHandler(IHttpContextAccessor httpContextAccessor, HttpMessageHandler innerHandler) : base(innerHandler)
    //     {
    //         _httpContextAccessor = httpContextAccessor;
    //     }
    //
    //     protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    //     {
    //         if (_httpContextAccessor.HttpContext is HttpContext context)
    //         {
    //             var accessToken = await context.GetTokenAsync("access_token");
    //
    //             if (accessToken is not null)
    //             {
    //                 request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
    //             }
    //         }
    //
    //         return await base.SendAsync(request, cancellationToken);
    //     }
    // }

}
