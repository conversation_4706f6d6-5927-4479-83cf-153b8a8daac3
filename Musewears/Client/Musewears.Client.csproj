<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ServerGarbageCollection>false</ServerGarbageCollection>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <DefineConstants>CLIENT</DefineConstants>
    <!-- <ServiceWorkerAssetsManifest>service-worker-assets.js</ServiceWorkerAssetsManifest> -->
  </PropertyGroup>

  <PropertyGroup Condition=" '$(RunConfiguration)' == 'Musewears' ">
    <ExternalConsole>true</ExternalConsole>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Asp.Versioning.Http.Client" Version="8.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="9.0.6" PrivateAssets="all" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Authentication" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.6" />
    <PackageReference Include="Blazored.Toast" Version="4.2.1" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.6" />
    <PackageReference Include="MudBlazor" Version="8.8.0" />
    <PackageReference Include="Refit" Version="8.0.0" />
    <PackageReference Include="Refit.HttpClientFactory" Version="8.0.0" />
    <PackageReference Include="Square" Version="42.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Google.Protobuf" Version="3.31.1" />
    <PackageReference Include="Grpc.Net.Client.Web" Version="2.71.0" />
    <PackageReference Include="Grpc.Net.ClientFactory" Version="2.71.0" />
    <PackageReference Include="Blazored.LocalStorage" Version="4.5.0" />
    <PackageReference Include="Grpc.Tools" Version="2.72.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <Compile Include="..\Shared\Models\*.cs" Exclude="..\Shared\Models\TransactionEvent.cs;..\Shared\Models\TransactionEventData.cs"/>
  </ItemGroup>

  <ItemGroup>
    <None Remove="Api\" />
    <None Remove="Pages\" />
    <None Remove="Layout\" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Api\" />
  </ItemGroup>
  <ItemGroup>
    <AdditionalFiles Include="Components\ModalDialog.razor" />
    <AdditionalFiles Include="Components\ProductsCarousel.razor" />
    <AdditionalFiles Include="Layout\MainLayout.razor" />
    <AdditionalFiles Include="Pages\CartPage.razor" />
    <AdditionalFiles Include="Pages\CheckoutPage.razor" />
    <AdditionalFiles Include="Pages\Home.razor" />
    <AdditionalFiles Include="Pages\OrderPage.razor" />
    <AdditionalFiles Include="Pages\ProductDisplayPage.razor" />
    <AdditionalFiles Include="Pages\ProductsSection.razor" />
    <AdditionalFiles Include="Pages\ShopPage.razor" />
    <AdditionalFiles Include="Routes.razor" />
    <AdditionalFiles Include="Shared\FooterSection.razor" />
    <AdditionalFiles Include="Shared\HeaderSection.razor" />
  </ItemGroup>
  
  <ItemGroup>
    <Protobuf Include="..\Basket.API\Protos\basket.proto" GrpcServices="Client" />
  </ItemGroup>


  <ItemGroup>
    <ProjectReference Include="..\WebAppComponents\WebAppComponents.csproj" />
  </ItemGroup>
</Project>
