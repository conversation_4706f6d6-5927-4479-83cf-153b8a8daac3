@using Blazored.Toast.Configuration
@using MudBlazor
@inherits LayoutComponentBase

@if (IsLoading)
{
    <!-- 1) Loader overlay, shown by default -->
    <div id="preloader">
        <div class="jumper">
            <div></div>
            <div></div>
            <div></div>
        </div>
    </div>
}
else
{
    <PageTitle>Musewears</PageTitle>

    <!-- Header Section Start -->
    <HeaderSection />
    <!-- Header Section End -->

    @Body

    <!-- Footer Section Start-->
    <FooterSection />
    @* <!-- Footer Section End--> *@
    
    @* Required *@
    <MudThemeProvider />
    <MudPopoverProvider />

    @* Needed for dialogs *@
    <MudDialogProvider />

    @* Needed for snackbars *@
    <MudSnackbarProvider />
    
    <BlazoredToasts Position="ToastPosition.BottomRight" Timeout="5" ShowProgressBar="false" /> 
    @* <BlazoredToasts Position="ToastPosition.BottomRight" Timeout="10" ShowProgressBar="true" /> *@

    @*<div class="page">
    <div class="sidebar">
    <NavMenu />
    </div>

    <main>
    <div class="top-row px-4 auth">
    <LoginDisplay />
    <a href="https://docs.microsoft.com/aspnet/" target="_blank">About</a>
    </div>

    <article class="content px-4">
    @Body
    </article>
    </main>
    </div>*@
}

@*@code {
protected async override Task OnAfterRenderAsync(bool firstRender)
{
await JSRuntime.InvokeAsync<IJSObjectReference>("import", "/js/main.js");
}
}*@

@code {

    private bool IsLoading { get; set; } = true;

    protected override void OnAfterRender(bool firstRender)
    {
        if (!firstRender) return;
        
        IsLoading = false;

        StateHasChanged();


    }
}