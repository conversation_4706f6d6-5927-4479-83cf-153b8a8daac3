@inherits LayoutComponentBase

@if (IsLoading)
{
    <!-- 1) Loader overlay, shown by default -->
    <div id="preloader">
        <div class="jumper">
            <div></div>
            <div></div>
            <div></div>
        </div>
    </div>
}
else
{
    @Body
}

@code {

    private bool IsLoading { get; set; } = true;

    protected override void OnAfterRender(bool firstRender)
    {
        IsLoading = false;
        
        if (firstRender)
        {
            StateHasChanged();
        }


    }
}