@using Musewears.Shared
@* @rendermode RenderMode.InteractiveAuto *@

@inject IJSRuntime jsRuntime
@inject IWearApi wearApi

@if (wears == null)
{
    <p><em>Loading...</em></p>
}
else
{
    <!-- Product Section Start-->
    <div class="product-section section pt-70 pb-60">
    <div class="container">

        <!-- Section Title Start-->
        <div class="row">
            <div class="section-title text-center col mb-60">
                <h1>Featured Products</h1>
            </div>
        </div><!-- Section Title End-->
        <!-- Product Wrapper Start-->
        <div class="row">

            <!-- Loop through wears and generate product cards -->
            @foreach (var wear in wears)
                {
                    <div class="col-lg-4 col-md-6 col-12 mb-60">
                        <div class="product">
                            <div class="image">
                                <a href="product-details.html" class="img"><img src="@wear.Photo" alt="Product"></a>
                                <a href="#" class="wishlist"><i class="fa fa-heart-o"></i></a>
                                <!-- You can conditionally display the label based on wear properties -->
                                @if (wear.IsNew)
                                {
                                    <span class="label">New</span>
                                }
                            </div>
                            <div class="content">
                                <div class="head fix">
                                    <div class="title-category float-left">
                                        <h5 class="title"><a href="product-details.html">@wear.Name</a></h5>
                                        <a href="shop.html" class="category">Catalog</a>
                                    </div>
                                    <div class="price float-right">
                                        <span class="new">@($"{wear.Currency}{wear.Price}")</span>
                                    </div>
                                </div>
                                <div class="action-button fix">
                                    <a href="#">add to cart</a>
                                </div>
                            </div>
                        </div>
                    </div>
                }

                <!-- Product Start-->
                <!--<div class="col-lg-4 col-md-6 col-12 mb-60">

                        <div class="product">-->
                <!-- Image Wrapper -->
                <!--<div class="image">-->
                <!-- Image -->
                <!--<a href="product-details.html" class="img"><img src="img/product/6.jpg" alt="Product"></a>-->
                <!-- Wishlist -->
                <!--<a href="#" class="wishlist"><i class="fa fa-heart-o"></i></a>-->
                <!-- Label -->
                <!-- <span class="label">New</span> -->
                <!--</div>-->
                <!-- Content -->
                <!--<div class="content">-->
                <!-- Head Content -->
                <!--<div class="head fix">-->
                <!-- Title & Category -->
                <!--<div class="title-category float-left">
                        <h5 class="title"><a href="product-details.html">Holiday Candle</a></h5>
                        <a href="shop.html" class="category">Catalog</a>
                    </div>-->
                <!-- Price -->
                <!--<div class="price float-right">
                        <span class="new">$38</span>-->
                <!-- Old Price Mockup If Need -->
                <!-- <span class="old">$46</span> -->
                <!--</div>

                    </div>-->
                <!-- Action Button -->
                <!--<div class="action-button fix">
                                    <a href="#">add to cart</a>
                                </div>

                            </div>

                        </div>

                    </div>-->
                <!-- Product End-->

            </div><!-- Product Wrapper End-->
            <!-- Pagination Start -->
            <div class="pagination col-12 mt-20">
                <ul>
                    <li class="active"><a href="#">1</a></li>
                    <li><a href="#">2</a></li>
                    <li><a href="#">3</a></li>
                    <li><a href="#">4</a></li>
                    <li><a href="#">5</a></li>
                    <li><a href="#">6</a></li>
                    <li class="arrows"><a href="#"><i class="fa fa-angle-right"></i></a></li>
                </ul>
            </div><!-- Pagination End -->

        </div>
    </div>

    <!-- Product Section End-->
}


@code {
    private List<Wear>? wears;

    //protected override async void OnAfterRender(bool firstRender)
    //{
    // base.OnAfterRender(firstRender);

    // wears = await wearApi.GetAll();

    //}

    //protected override async void OnInitialized()
    //{
    // base.OnInitialized();
    // wears = await wearApi.GetAll();

    //}

    protected override async Task OnInitializedAsync()
    {
        //await jsRuntime.InvokeAsync<string>("console.log", "OnInitializedAsync");

        wears = await wearApi.GetAll();

        //await jsRuntime.InvokeAsync<string>("console.log", $"wears: {wears}");
    }

    protected override async Task OnParametersSetAsync()
    {
        wears = await wearApi.GetAll();
    }
}