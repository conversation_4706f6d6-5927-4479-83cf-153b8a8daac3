@using DefaultNamespace
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.WebAssembly.Authentication
@using Musewears.Client.Layout
@using Refit
@using WebAppComponents.Services

@*@using static Microsoft.AspNetCore.Components.Web.RenderMode*@
@*@rendermode InteractiveServer*@

@inject NavigationManager Navigation
@*@inject IAccountApi accountApi*@
@*@inject SignInManager<ApplicationUser> SignInManager*@
@* @using Microsoft.AspNetCore.Identity; *@
@* @using Musewears.Server.Models *@
@* @inject UserManager<ApplicationUser> UserManager *@
@*@inject IdentityUserAccessor UserAccessor*@
@* @inject SignOutSessionStateManager SignOutManager *@

@* @inject SignInManager<ApplicationUser> SignInManager *@

@inject AuthenticationStateProvider AuthenticationStateProvider

@inject IAccountApi Account

@* @inject SignOutSessionStateManager SignOutManager *@

@* @inject CatalogService Catalog *@

@* @inject IHttpClientFactory HttpClientFactory *@


@* <!-- ***** Header Area Start ***** --> *@
<header class="header-area header-sticky">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <nav class="main-nav">
                    @* <!-- ***** Logo Start ***** --> *@
                    <a href="/" class="logo">
                        <img src="assets/images/musewears-logo.png">
                    </a>
                    @* <!-- ***** Logo End ***** --> *@
                    @* <!-- ***** Menu Start ***** --> *@
                    <ul class="nav">
                        <li class="scroll-to-section"><a href="/" class="active">Home</a></li>
                        <li class="scroll-to-section"><a href="/products">Men's</a></li>
                        <li class="scroll-to-section"><a href="/products">Women's</a></li>
                        <li class="scroll-to-section"><a href="/products">Kid's</a></li>

                        <AuthorizeView>
                            <Authorized>
                                <li class="submenu">
                                    <a href="javascript:;">Account</a>
                                    <ul>
                                        <li><a href="/Account/Manage">Profile</a></li>
                                        <li><a href="/cart">Cart</a></li>
                                        <li><a href="/orders">Orders</a></li>
                                    </ul>
                                </li>
                            </Authorized>
                        </AuthorizeView>

                        @* <li class="submenu">
                        <a href="javascript:;">Features</a>
                        <ul>
                        <li><a href="#">Features Page 1</a></li>
                        <li><a href="#">Features Page 2</a></li>
                        <li><a href="#">Features Page 3</a></li>
                        <li><a rel="nofollow" href="https://templatemo.com/page/4" target="_blank">Template
                        Page 4</a></li>
                        </ul>
                        </li> *@
                        <li class="scroll-to-section hidden-link"><a href="#explore">Explore</a></li>

                        <!-- Add the login button here -->
                        <div class="main-border-button">

                            <AuthorizeView>
                                <Authorized>
                                    @* <a href="/login">Logout</a> *@
                                    <a href="#" @onclick="Logout" @onclick:preventDefault>Logout</a>
                                    @* <form @onclick="Logout" > *@
                                    @*     <input type="submit" value="Logout" /> *@
                                    @* </form> *@
                                    @* <form id="logout_form" asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="/" method="post"> *@
                                    @*     $1$ <AntiforgeryToken /> #1# *@
                                    @*     $1$ <input type="hidden" name="returnUrl" value="@currentUrl" /> #1# *@
                                    @*     $1$<a @onclick=@(() => context.) @onclick:preventDefault>Logout</a>#1# *@
                                    @* *@
                                    @*     @* <a href="" @onclick=@(() => document.getElementById("logout_form").submit()) *@
                                    @*     @onclick:preventDefault type="submit" *@
                                    @*     onclick:preventDefault>Logout</a> *@
                                    @* *@
                                    @*     <input type="submit" value="Logout" /> *@
                                    @* </form> *@
                                </Authorized>
                                <NotAuthorized>
                                    <a href="/Account/Login">Login</a>
                                </NotAuthorized>
                            </AuthorizeView>
                        </div>
                    </ul>

                    <a class='menu-trigger'>
                        <span>Menu</span>
                    </a>
                    @* <!-- ***** Menu End ***** --> *@
                </nav>
            </div>
        </div>
    </div>
</header>
@* <!-- ***** Header Area End ***** --> *@


@code {
    //static InteractiveWebAssemblyRenderMode InteractiveWebAssemblyNoPreRender = new(false);

    //[CascadingParameter]
    //private HttpContext HttpContext { get; set; } = default!;

    // private ApplicationUser? user;

    @* [CascadingParameter] *@
    @* private HttpContext HttpContext { get; set; } = default!; *@

    string? profileImageUrl; // Set the actual path or URL of your profile image


    private string? currentUrl;
    
    private async Task Logout()
    {
        try
        {
            // "IAccountApi" is the default name for your Refit client
            // var client = HttpClientFactory.CreateClient("default");
            // var api    = RestService.For<IAccountApi>(client);
            
            // set up the sign‐out state so the logout page knows where to return
            // await SignOutManager.SetSignOutState();
            // this will redirect the browser to /authentication/logout, do a full reload,
            // and invoke the built-in Identity logout pipeline
            // Navigation.NavigateTo("authentication/logout", forceLoad: true);


            await Account.LogoutAsync(new LogoutRequest { ReturnUrl = currentUrl });
            
            Navigation.NavigateTo(currentUrl ?? "/", forceLoad: true);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
        }
        
        // This route is wired up by AddApiAuthorization()
        // Navigation.NavigateTo("authentication/logout", forceLoad: true);
    }

    protected override void OnInitialized()
    {
        currentUrl = Navigation.ToBaseRelativePath(Navigation.Uri);
        Navigation.LocationChanged += OnLocationChanged;
    }

    @* protected override async Task OnInitializedAsync()
{
var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
var user = authState.User;

if (user.Identity is not null && user.Identity.IsAuthenticated)
{
// User is logged in
}
else
{
// User is not logged in
}
} *@


    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var userId = authState.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

        if (!string.IsNullOrEmpty(userId))
        {
            // user = await UserManager.FindByIdAsync(userId);

            @* Console.WriteLine($"User: {user?.Email}"); *@
            // Now you have access to ApplicationUser data such as:
            // currentUser.Email, currentUser.PhoneNumber, etc.
        }
    }

    private void OnLocationChanged(object? sender, LocationChangedEventArgs e)
    {
        currentUrl = Navigation.ToBaseRelativePath(e.Location);
        StateHasChanged();
    }

    public void Dispose()
    {
        Navigation.LocationChanged -= OnLocationChanged;
    }

    private void NavigeteTo(String page)
    {

        Navigation.NavigateTo(page, true);
    }


    private void NavigeteToLogin()
    {

        Navigation.NavigateToLogin("authentication/login");
    }

    //protected override void OnInitialized() => Navigation.LocationChanged += (s, e) => StateHasChanged();

    bool IsActive(string href, NavLinkMatch navLinkMatch = NavLinkMatch.Prefix)
    {
        var relativePath = Navigation.ToBaseRelativePath(Navigation.Uri).ToLower();
        return navLinkMatch == NavLinkMatch.All ? relativePath == href.ToLower() : relativePath.StartsWith(href.ToLower());
    }

    string GetActive(string href, NavLinkMatch navLinkMatch = NavLinkMatch.Prefix) => IsActive(href, navLinkMatch) ?
    "active" : "";
}
