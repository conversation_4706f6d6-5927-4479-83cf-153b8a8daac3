@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.WebAssembly.Authentication

@inject NavigationManager Navigation
@* @inject SignOutSessionStateManager SignOutManager *@

    <!-- Header Top Start -->
    <div class="header-top">
        <div class="container">
            <div class="row">
                <div class="col">

                    <!-- Header Top Wrapper Start -->
                    <div class="header-top-wrapper">
                        <div class="row">

                            <!-- Header Social -->
                            <div class="header-social col-md-4 col-12">
                                <a href="#"><i class="fa fa-facebook"></i></a>
                                <a href="#"><i class="fa fa-twitter"></i></a>
                                <a href="#"><i class="fa fa-linkedin"></i></a>
                                <a href="#"><i class="fa fa-instagram"></i></a>
                                <a href="#"><i class="fa fa-pinterest-p"></i></a>
                            </div>

                            <!-- Header Logo -->
                            <div class="header-logo col-md-4 col-12">
                                <a href="/" class="logo"><img src="img/logo.png" alt="logo"></a>
                            </div>

                            <!-- Account Menu -->
                            <div class="account-menu col-md-4 col-12">
                                <ul>

                                    <AuthorizeView>
                                        <Authorized>

                                            <li><a href="" @onclick=@(() => NavigeteTo("manage_items")) @onclick:preventDefault>Manage Items</a></li>
                                            <li><a href="#">My Account</a></li>

                                            <a href="/Account/Manage/PersonalData">Hello, @context.User.Identity?.Name!</a>
                                            @* <button class="nav-link btn btn-link" @onclick="BeginSignOut">Log out</button> *@

                                            <li><a href="" @onclick=@(() => Navigation.NavigateToLogout("authentication/logout")) @onclick:preventDefault>Logout</a></li>
                                        </Authorized>
                                        <NotAuthorized>
                                            @* <a href="authentication/login">Login</a> *@
                                            <li><a href="Account/Login" @onclick:preventDefault>Login</a></li>

                                            @* <a href="authentication/register">Register</a>
                                            <a href="authentication/login">Log in</a> *@
                                        </NotAuthorized>
                                    </AuthorizeView>

                                    @* <AuthorizeView>
                                        <Authorized>
                                            <a href="authentication/profile">Hello, @context.User.Identity?.Name!</a>
                                            <button class="nav-link btn btn-link" @onclick="BeginSignOut">Log out</button>
                                        </Authorized>
                                        <NotAuthorized>
                                            <a href="authentication/register">Register</a>
                                            <a href="authentication/login">Log in</a>
                                        </NotAuthorized>
                                    </AuthorizeView> *@
                                    
                                    <li>
                                        <a href="#" data-toggle="dropdown"><i class="fa fa-shopping-cart"></i><span class="num">2</span></a>

                                        <!-- Mini Cart -->
                                        <div class="mini-cart-brief dropdown-menu text-left">
                                            <!-- Cart Products -->
                                            <div class="all-cart-product clearfix">
                                                <div class="single-cart clearfix">
                                                    <div class="cart-image">
                                                        <a href="product-details"><img src="img/cart/1.jpg" alt=""></a>
                                                    </div>
                                                    <div class="cart-info">
                                                        <h5><a href="product-details">Holiday Candle</a></h5>
                                                        <p>1 x £9.00</p>
                                                        <a href="#" class="cart-delete" title="Remove this item"><i class="fa fa-trash-o"></i></a>
                                                    </div>
                                                </div>
                                                <div class="single-cart clearfix">
                                                    <div class="cart-image">
                                                        <a href="product-details"><img src="img/cart/2.jpg" alt=""></a>
                                                    </div>
                                                    <div class="cart-info">
                                                        <h5><a href="product-details">Christmas Tree</a></h5>
                                                        <p>1 x £9.00</p>
                                                        <a href="#" class="cart-delete" title="Remove this item"><i class="fa fa-trash-o"></i></a>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- Cart Total -->
                                            <div class="cart-totals">
                                                <h5>Total <span>£12.00</span></h5>
                                            </div>
                                            <!-- Cart Button -->
                                            <div class="cart-bottom  clearfix">
                                                <a href="" @onclick=@(() => NavigeteTo("checkout")) @onclick:preventDefault>Check out</a>
                                            </div>
                                        </div>

                                    </li>
                                </ul>
                            </div>

                        </div>
                    </div><!-- Header Top Wrapper End -->

                </div>
            </div>
        </div>
    </div><!-- Header Top End -->
    <!-- Header Bottom Start -->
    <div class="header-bottom section">
        <div class="container">
            <div class="row">

                <!-- Header Bottom Wrapper Start -->
                <div class="header-bottom-wrapper text-center col">

                    <!-- Header Bottom Logo -->
                    <div class="header-bottom-logo">
                        <a href="/" class="logo"><img src="img/logo.png" alt="logo"></a>
                    </div>

                    <!-- Main Menu -->
                    <nav id="main-menu" class="main-menu">
                        <ul>
                            <li class="@GetActive("", NavLinkMatch.All)">
                            <a href="" @onclick=@(() => NavigeteTo("/")) @onclick:preventDefault>home</a>
                            </li>
                            <li class="@GetActive("shop", NavLinkMatch.All)">
                                <a href="" @onclick=@(() => NavigeteTo("shop")) @onclick:preventDefault>shop</a>
                                <ul class="sub-menu">
                                    <li><a href="shop">shop page</a></li>
                                    <li><a href="product-details">product details</a></li>
                                </ul>
                            </li>
                            <li class="@GetActive("about", NavLinkMatch.All)">
                            <a href="" @onclick=@(() => NavigeteTo("about")) @onclick:preventDefault>about</a>
                            </li>
                            <li class="@GetActive("pages", NavLinkMatch.All)">
                                <a href="#">pages</a>
                                <ul class="sub-menu">
                                    <li>
                                    <a href="" @onclick=@(() => NavigeteTo("cart")) @onclick:preventDefault>cart</a>
                                    </li>
                                    <li>
                                    <a href="" @onclick=@(() => NavigeteTo("checkout")) @onclick:preventDefault>checkout</a>
                                    </li>
                                    @*<li><a href="wishlist">wishlist</a></li>*@
                                    @*<li><a href="under-construction">Under Construction</a></li>*@
                                </ul>
                            </li>
                            @*<li class="@GetActive("blog", NavLinkMatch.All)">
                                <a href="blog">blog</a>
                                <ul class="sub-menu">
                                    <li><a href="blog">blog page</a></li>
                                    <li><a href="blog-details">blog details</a></li>
                                </ul>
                            </li>*@
                            <li class="@GetActive("contact", NavLinkMatch.All))"><a href="" @onclick=@(() => NavigeteTo("contact")) @onclick:preventDefault>contact</a></li>
                        </ul>
                    </nav>

                    <!-- Header Search -->
                    <div class="header-search">

                        <!-- Search Toggle -->
                        <button class="search-toggle"><i class="ion-ios-search-strong"></i></button>

                        <!-- Search Form -->
                        <div class="header-search-form">
                            <form action="#">
                                <input type="text" placeholder="Search...">
                                <button><i class="ion-ios-search-strong"></i></button>
                            </form>
                        </div>

                    </div>

                    <!-- Mobile Menu -->
                    <div class="mobile-menu section d-md-none"></div>

                </div><!-- Header Bottom Wrapper End -->

            </div>
        </div>
    </div><!-- Header Bottom End -->

@code{

    private void NavigeteTo(String page)
    {
        
        Navigation.NavigateTo(page, true);
    }


    private void NavigeteToLogin()
    {
        
        Navigation.NavigateToLogin("authentication/login");
    }

    protected override void OnInitialized() => Navigation.LocationChanged += (s, e) => StateHasChanged();

    bool IsActive(string href, NavLinkMatch navLinkMatch = NavLinkMatch.Prefix)
    {
        var relativePath = Navigation.ToBaseRelativePath(Navigation.Uri).ToLower();
        return navLinkMatch == NavLinkMatch.All ? relativePath == href.ToLower() : relativePath.StartsWith(href.ToLower());
    }

    string GetActive(string href, NavLinkMatch navLinkMatch = NavLinkMatch.Prefix) => IsActive(href, navLinkMatch) ? "active" : "";
}

