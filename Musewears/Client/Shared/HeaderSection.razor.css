body {}

/* Rounded image style */
.profile-image {
    border-radius: 50%;
    /* Make it fully rounded */
    width: 26px;
    /* Adjust the width as needed */
    height: 26px;
    /* Adjust the height as needed */
}

/* Add margin to the submit button only */
.header-icons input[type="submit"] {
    color: #fff;
    /* Set text color to white */
}

.hidden-link {
    visibility: hidden;
    pointer-events: none;
}

.main-border-button a {
    font-size: 13px;
    color: #fff;
    border: 1px solid #fff;
    padding: 12px 25px;
    display: inline-block;
    font-weight: 500;
    transition: all .3s;
    border-radius: 10px !important;
    background-color: #a3c1ad;
    color: #fff;
    /* Adding !important to ensure it takes effect */
}

.main-border-button a:hover {
    background-color: #2a2a2a;
    color: #fff;
}

.main-border-button input {
    font-size: 13px;
    color: #fff;
    border: 1px solid #fff;
    padding: 12px 25px;
    display: inline-block;
    font-weight: 500;
    transition: all .3s;
    border-radius: 10px !important;
    background-color: #a3c1ad;
    color: #fff;
    /* Adding !important to ensure it takes effect */
}

.main-border-button input:hover {
    background-color: #2a2a2a;
    color: #fff;
}