@inject IJSRuntime JSRuntime

<!-- Hero Slider Start-->
<div class="hero-slider">

    <!-- Hero Slide Item Start-->
    <div class="hero-item" style="background-image: url(img/hero/1.jpg)">

        <!-- Hero Content Start-->
        <div class="hero-content text-center m-auto">

            <h2>Save 25%</h2>
            <h1>Christmas Sale</h1>
            <p>There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which.</p>
            <a href="#">LEARN MORE</a>

        </div><!-- Hero Content End-->


    </div><!-- Hero Slide Item End-->
    <!-- Hero Slide Item Start-->
    <div class="hero-item" style="background-image: url(img/hero/2.jpg)">

        <!-- Hero Content Start-->
        <div class="hero-content text-center m-auto">

            <h2>Save 25%</h2>
            <h1>Christmas Sale</h1>
            <p>There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which.</p>
            <a href="#">LEARN MORE</a>

        </div><!-- Hero Content End-->


    </div><!-- Hero Slide Item End-->
</div><!-- Hero Slider End-->

@code {

    //protected override async Task OnInitializedAsync()
    //{
    //    await JSRuntime.InvokeAsync<IJSObjectReference>("import", "/js/main.js");
    //}

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //await JSRuntime.InvokeAsync<IJSObjectReference>("import", "/js/main.js");
    }
}
