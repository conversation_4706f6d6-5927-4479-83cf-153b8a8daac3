
using Musewears.Shared;
using Refit;

namespace Musewears.Client.Api
{
    public interface IWearApi
    {
        [Get("/api/wears")]
        Task<List<Wear>> GetAll();

        [Get("/api/wears/{id}")]
        Task<Wear> GetById(string id);

        [Post("/api/wears")]
        Task<int> Create([Body] Wear wear);

        [Put("/api/wears/{id}")]
        Task Update(string id, [Body] Wear wear);

        [Delete("/api/wears/{id}")]
        Task Delete(string id);
    }
}

