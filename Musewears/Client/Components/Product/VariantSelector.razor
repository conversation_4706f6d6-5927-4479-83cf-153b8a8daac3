@using WebAppComponents.Catalog
@namespace Musewears.Client.Components.Product

<!-- Components/VariantSelector.razor -->
@if (Colors?.Any() == true)
{
    <!-- ADDED: Color Selector -->
    <div class="quantity-content">
        <div class="left-content">
            <h6>Color</h6>
        </div>
        <div class="right-content">
            <div class="color-options">
                @foreach (var color in Colors)
                {
                    <button class="@(color.Id == SelectedColorId ? "active" : "")" 
                            @onclick="() => SelectColor(color)"
                            style="background-color: @color.ColorCode;">
                    </button>
                }
            </div>
        </div>
    </div>
}

@if (Sizes?.Any() == true)
{
    <!-- ADDED: Size Selector -->
    <div class="quantity-content">
        <div class="left-content">
            <h6>Size</h6>
        </div>
        <div class="right-content">
            <div class="size-options">
                @foreach (var size in Sizes)
                {
                    <button class="@(size.Id == SelectedSizeId ? "active" : "")" 
                            @onclick="() => SelectSize(size)">
                        @size.SizeName
                    </button>
                }
            </div>
        </div>
    </div>
}

<style>
    .quantity-content {
        padding-bottom: 0;
        border-bottom: none !important;
    }
    
    /* ADDED: Size and Color Selector Styles */
    /*.quantity-content:last-child {*/
    /*    border-bottom: none !important; !* Remove border for the last item *!*/
    /*}*/

    .size-content .left-content,
    .color-content .left-content {
        flex: 1;
        display: flex;
        align-items: center;
    }

    .size-content .right-content,
    .color-content .right-content {
        flex: 2;
    }

    .size-options, .color-options {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
    }

    .size-options button {
        min-width: 40px;
        padding: 6px 10px;
        border: 1px solid #ddd;
        background: white;
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.3s;
    }

    .size-options button.active {
        border-color: #000;
        background: #000;
        color: white;
    }

    .color-options button {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        border: 2px solid #ddd;
        cursor: pointer;
        position: relative;
    }

    .color-options button.active {
        border-color: #000;
        box-shadow: 0 0 0 2px white, 0 0 0 3px #000;
    }

    .color-options button::after {
        content: "";
        position: absolute;
        top: -4px;
        left: -4px;
        right: -4px;
        bottom: -4px;
        border-radius: 50%;
    }
</style>

@code {
    [Parameter] public CatalogItemColor[]? Colors { get; set; }
    [Parameter] public CatalogItemSize[]? Sizes { get; set; }
    [Parameter] public CatalogItemVariant[]? Variants { get; set; }
    [Parameter] public required EventCallback<CatalogItemVariant> OnVariantSelected { get; set; }
    [Parameter] public required EventCallback<CatalogItemColor?> OnColorSelected { get; set; }
    [Parameter] public required EventCallback<CatalogItemSize?> OnSizeSelected { get; set; }

    private CatalogItemColor? _selectedColor;
    private CatalogItemSize? _selectedSize;

    // Current selections
    private int? SelectedColorId => _selectedColor?.Id;
    private int? SelectedSizeId => _selectedSize?.Id;
    
    // Available options (filtered based on current selections)
    private List<CatalogItemColor> AvailableColors => GetAvailableColors();
    private List<CatalogItemSize> AvailableSizes => GetAvailableSizes();

    private void SelectColor(CatalogItemColor? color)
    {
        _selectedColor = color;
        OnColorSelected.InvokeAsync(color);
        TrySelectVariant();
        
        Console.WriteLine($"Selected color: {_selectedColor?.ColorName}");
    }

    private void SelectSize(CatalogItemSize size)
    {
        _selectedSize = size;
        OnSizeSelected.InvokeAsync(size);
        TrySelectVariant();
        
        Console.WriteLine($"Selected size: {_selectedSize?.SizeName}");
    }

    private void TrySelectVariant()
    {
        if (_selectedColor != null && _selectedSize != null)
        {
            var variant = Variants?.FirstOrDefault(v => v.CatalogItemColorId == _selectedColor.Id && v.CatalogItemSizeId ==
            _selectedSize.Id);
            OnVariantSelected.InvokeAsync(variant);
        }
        else
        {
            OnVariantSelected.InvokeAsync(null);
        }
    }
    
    private List<CatalogItemColor> GetAvailableColors()
    {
        if (Colors == null) return [];
    
        // If no size selected, show all colors
        if (_selectedSize == null)
            return Colors.ToList();
    
        // Show only colors that have variants with the selected size
        return Colors.Where(color =>
        Variants?.Any(v =>
        v.CatalogItemColorId == color.Id &&
        v.CatalogItemSizeId == _selectedSize?.Id) == true
        ).ToList();
    }
    
    private List<CatalogItemSize> GetAvailableSizes()
    {
        if (Sizes == null) return [];
    
        // If no color selected, show all sizes
        if (_selectedColor == null)
            return Sizes.ToList();
    
        // Show only sizes that have variants with the selected color
        return Sizes.Where(size =>
        Variants?.Any(v =>
        v.CatalogItemSizeId == size.Id &&
        v.CatalogItemColorId == _selectedColor.Id) == true
        ).ToList();
    }

    private bool IsColorAvailable(CatalogItemColor color)
    {
        // if (SelectedSizeId == null) return true;
        
        // if (Variants == null) return true;

        return Variants?.Any(v =>
        v.CatalogItemColorId == color.Id &&
        v.CatalogItemSizeId == SelectedSizeId &&
        v.AvailableStock > 0) == true;
    }

    private bool IsSizeAvailable(CatalogItemSize size)
    {
        // if (SelectedColorId == null) return true;

        // if (Variants == null) return true;

        return Variants?.Any(v =>
        v.CatalogItemSizeId == size.Id &&
        v.CatalogItemColorId == SelectedColorId &&
        v.AvailableStock > 0) == true;
    }
}