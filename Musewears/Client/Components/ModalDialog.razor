@using Musewears.Client.Services
@inject MusewearDialogService DialogService

@if (isDialogVisible)
{
    <div class="modal fade show" id="myModal" style="display:block; background-color: rgba(10,10,10,.8);" aria-modal="true"
    role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">@Title</h4>
                <button type="button" class="close" @onclick="@ModalCancel">&times;</button>
            </div>
            <div class="modal-body">
                <p>@Message</p>
            </div>
            <div class="modal-footer">
                @switch (DialogType)
                    {
                        case ModalDialogType.Ok:
                            <button type="button" class="btn btn-primary" @onclick=@ModalOk>OK</button>
                            break;
                        case ModalDialogType.OkCancel:
                            <button type="button" class="btn" @onclick="@ModalCancel">Cancel</button>
                            <button type="button" class="btn btn-primary" @onclick=@ModalOk>OK</button>
                            break;
                        case ModalDialogType.DeleteCancel:
                            <button type="button" class="btn" @onclick="@ModalCancel">Cancel</button>
                            <button type="button" class="btn btn-danger" @onclick=@ModalOk>Delete</button>
                            break;
                    }
                </div>
            </div>
        </div>
    </div>
}

@code {
    private string? Title { get; set; }

    private string? Message { get; set; }

    @* [Parameter] *@
    @* public EventCallback<bool> OnClose { get; set; } *@

    [Parameter]
    public ModalDialogType? DialogType { get; set; }

    private bool isDialogVisible = false;

    private TaskCompletionSource<bool>? dialogResultCompletionSource;

    private void ModalCancel()
    {
        dialogResultCompletionSource?.TrySetResult(false);
        @* return OnClose.InvokeAsync(false); *@
    }

    private void ModalOk()
    {
        dialogResultCompletionSource?.TrySetResult(true);
        @* return OnClose.InvokeAsync(true); *@
    }

    public enum ModalDialogType
    {
        Ok,
        OkCancel,
        DeleteCancel
    }

    protected override void OnInitialized()
    {
        DialogService.OnShow += ShowDialog;
    }

    private async Task<bool> ShowDialog(DialogParameters parameters)
    {
        Title = parameters?.Title;
        Message = parameters?.Message;
        DialogType = parameters?.DialogType;

        isDialogVisible = true;

        this.dialogResultCompletionSource = new TaskCompletionSource<bool>();

        // Logic to show the modal
        StateHasChanged(); // Ensure UI updates


        // This will asynchronously wait until the dialog result is set
        var result = await dialogResultCompletionSource.Task;

        isDialogVisible = false;

        StateHasChanged(); // Ensure UI updates


        return result;


        // Wait for the user to close the dialog
        @* var result = await Task.FromResult<bool?>(null); // Replace with actual logic to get the result
return result; *@
    }

    // Don't forget to unsubscribe when the component is disposed
    public void Dispose()
    {
        dialogResultCompletionSource?.TrySetResult(false);
        dialogResultCompletionSource = null;
        DialogService.OnShow -= ShowDialog;
    }
}