@using Musewears.Shared
@using WebAppComponents.Catalog
@using WebAppComponents.Services

@inject IJSRuntime JsRuntime
@inject IProductImageUrlProvider ProductImages

@* @rendermode RenderMode.InteractiveServer *@

@* @rendermode @(new InteractiveAutoRenderMode(prerender: false)) *@

<section class="section" id="women">
    <div class="container">
        <div class="row">
            <div class="col-lg-6">
                <div class="section-heading">
                    <h2>@Title</h2>
                    <span>@Subtitle</span>
                </div>
            </div>
        </div>
    </div>
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="women-item-carousel">
                    <div class="owl-women-item owl-carousel">

                        @foreach (var wear in Wears)
                        {
                            <div class="item">
                                <div class="thumb">
                                    <div class="hover-content">
                                        <ul>
                                            <li><a href="/product/@wear.Id"><i class="fa fa-eye"></i></a></li>
                                            @* <li><a href="/product/"><i class="fa fa-star"></i></a></li> *@
                                            <li>
                                                <a href="/cart"><i class="fa fa-shopping-cart"></i></a>
                                            </li>
                                        </ul>
                                    </div>
                                    <img src="@ProductImages.GetProductImageUrl(wear)" alt="">
                                </div>
                                <div class="down-content">
                                    <h4>@wear.Name</h4>
                                    <span>@<EMAIL>("N2")</span>
                                    <ul class="stars">
                                        @for (int i = 1; i <= 5; i++)
                                        {
                                            <li>
                                                @if (i <= (int)wear.Rating)
                                                {
                                                    <i class="fa fa-star"></i> <!-- Full star -->
                                                }
                                                else if (i <= wear.Rating + 0.5m)
                                                {
                                                    <i class="fa fa-star-half-o" style="margin-left: 5px;"></i> <!-- Half star -->
                                                }
                                                else
                                                {
                                                    <i class=" fa fa-star-o" style="margin-left: 5px;"></i> <!-- Empty star -->
                                                }
                                            </li>
                                        }
                                    </ul>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@* <script src="assets/js/custom.js"></script> *@

@code {

    [Parameter]
    public List<CatalogItem> Wears { get; set; } = [];

    [Parameter]
    public string Title { get; set; } = string.Empty;

    [Parameter]
    public string Subtitle { get; set; } = "Details to details is what makes Musewears different from the other themes.";

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JsRuntime.InvokeVoidAsync("owlCarouselInit", ".owl-women-item");
        }
    }
}
@* How do I trigger this only when ready or only when *@