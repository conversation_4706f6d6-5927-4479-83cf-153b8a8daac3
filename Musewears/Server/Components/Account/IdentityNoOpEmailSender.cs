using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Musewears.Server.Models;
using Musewears.Shared.Models;

namespace Musewears.Server;

public class IdentityNoOpEmailSender(IEmailSender emailSender) : IEmailSender<ApplicationUser>
{
    // private readonly IEmailSender emailSender = new NoOpEmailSender();

    public Task SendConfirmationLinkAsync(ApplicationUser user, string email, string confirmationLink)
    {
        return emailSender.SendEmailAsync(email, "Confirm your email", $"Please confirm your account by <a href='{confirmationLink}'>");
    }

    public Task SendPasswordResetCodeAsync(ApplicationUser user, string email, string resetCode) => emailSender.SendEmailAsync(email, "Reset your password", $"Please reset your password using the following code: {resetCode}");

    public Task SendPasswordResetLinkAsync(ApplicationUser user, string email, string resetLink) => emailSender.SendEmailAsync(email, "Reset your password", $"Please reset your password by <a href='{resetLink}'>");
}
