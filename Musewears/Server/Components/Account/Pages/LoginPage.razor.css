body {
}

.form-container {
    width: 40%; /* Adjust the width as needed */
    margin: 0 auto;
    background-color: #fff; /* Optional: Add a background color for better visibility */
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); /* Optional: Add a box shadow for a subtle effect */
}

.center-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

.form-check {
    vertical-align: central;
    justify-content: center;
    align-items: center;
}

/* Add margin to the submit button only */
.billing-address-form input[type="submit"] {
    margin-top: 20px; /* Adjust the margin as needed */
    margin-bottom: 20px;
    color: #fff; /* Set text color to white */
}


/* Additional styles for links */
.additional-links {
    margin-top: 20px;
    text-align: center;
}

.additional-links a {
    margin-right: 20px; /* Adjust the margin as needed */
    color: #007bff; /* Set link color to blue */
}
