@page "/Account/Manage"

@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Identity
@using Musewears.Server.Data
@using Musewears.Server.Models

@inject UserManager<ApplicationUser> UserManager
@inject SignInManager<ApplicationUser> SignInManager
@inject IdentityUserAccessor UserAccessor
@inject IdentityRedirectManager RedirectManager

<PageTitle>Profile</PageTitle>

<h3>Profile</h3>
<StatusMessage />

<div class="row">
    <div class="col-md-6">
        <EditForm Model="Input" FormName="profile" OnValidSubmit="OnValidSubmitAsync" method="post">
            <DataAnnotationsValidator />
            <ValidationSummary class="text-danger" role="alert" />
            <div class="form-floating mb-3">
                <input type="text" value="@username" class="form-control" placeholder="Please choose your username." disabled />
                <label for="username" class="form-label">Username</label>
            </div>
            <div class="form-floating mb-3">
                <InputText @bind-Value="Input.Fullname" class="form-control" placeholder="Please enter your fullname." />
                <label for="fullname" class="form-label">Fullname</label>
                <ValidationMessage For="() => Input.Fullname" class="text-danger" />
            </div>
            <div class="form-floating mb-3">
                <InputText @bind-Value="Input.PhoneNumber" class="form-control" placeholder="Please enter your phone number." />
                <label for="phone-number" class="form-label">Phone number</label>
                <ValidationMessage For="() => Input.PhoneNumber" class="text-danger" />
            </div>
            <button type="submit" class="w-100 btn btn-lg btn-primary">Save</button>
        </EditForm>
    </div>
</div>

@code {
    private ApplicationUser user = default!;
    private string? username;
    private string? phoneNumber;

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    protected override async Task OnInitializedAsync()
    {
        user = await UserAccessor.GetRequiredUserAsync(HttpContext);
        username = await UserManager.GetUserNameAsync(user);
        phoneNumber = await UserManager.GetPhoneNumberAsync(user);

        Input.PhoneNumber ??= phoneNumber;
        Input.Fullname ??= user.Fullname;

    }

    private async Task OnValidSubmitAsync()
    {
            user.Fullname = Input.Fullname;
            user.PhoneNumber = Input.PhoneNumber;
            var setPhoneResult = await UserManager.UpdateAsync(user);
            if (!setPhoneResult.Succeeded)
            {
                RedirectManager.RedirectToCurrentPageWithStatus("Error: Failed to set phone number.", HttpContext);
            }
       

        await SignInManager.RefreshSignInAsync(user);
        RedirectManager.RedirectToCurrentPageWithStatus("Your profile has been updated", HttpContext);
    }

    private sealed class InputModel
    {
        
        [Display(Name = "Phone number")]
        public string? PhoneNumber { get; set; }
        [Display(Name = "Full Name")]
        public string? Fullname { get; set; }
    }
}
