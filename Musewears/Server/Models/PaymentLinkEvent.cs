using Newtonsoft.Json;

namespace Musewears.Server.Models;

public class PaymentLinkEvent
{
    [JsonProperty("event")]
    public string? Event { get; set; }

    [JsonProperty("uuid")]
    public string? Uuid { get; set; }

    [JsonProperty("timestamp")]
    public long Timestamp { get; set; }

    [JsonProperty("data")]
    public PaymentLinkEventData? Data { get; set; }
}

public class PaymentLinkEventData
{
    [JsonProperty("id")]
    public int Id { get; set; }

    [JsonProperty("merchantCode")]
    public string? MerchantCode { get; set; }

    [JsonProperty("transactionReference")]
    public string? TransactionReference { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("amount")]
    public decimal Amount { get; set; }

    [JsonProperty("reference")]
    public string? Reference { get; set; }

    [JsonProperty("linkType")]
    public string? LinkType { get; set; }

    [JsonProperty("currencyCode")]
    public string? CurrencyCode { get; set; }

    [JsonProperty("subscriptionId")]
    public string? SubscriptionId { get; set; }

    [JsonProperty("customerEmail")]
    public string? CustomerEmail { get; set; }

    [JsonProperty("customerName")]
    public string? CustomerName { get; set; }

    [JsonProperty("responseCode")]
    public string? ResponseCode { get; set; }

    [JsonProperty("responseDescription")]
    public string? ResponseDescription { get; set; }

    [JsonProperty("transactionDate")]
    public long TransactionDate { get; set; }
}
