using Newtonsoft.Json;

namespace Musewears.Server.Models;

public class PayoutEvent
{
    [JsonProperty("event")]
    public string Event { get; set; }

    [JsonProperty("uuid")]
    public string Uuid { get; set; }

    [JsonProperty("timestamp")]
    public long Timestamp { get; set; }

    [JsonProperty("data")]
    public PayoutEventData Data { get; set; }
}

public class PayoutEventData
{
    [JsonProperty("merchantCode")]
    public string MerchantCode { get; set; }

    [JsonProperty("merchantWalletId")]
    public int MerchantWalletId { get; set; }

    [JsonProperty("walletId")]
    public string WalletId { get; set; }

    [JsonProperty("beneficiaryAccountType")]
    public string BeneficiaryAccountType { get; set; }

    [JsonProperty("beneficiaryWalletIdType")]
    public string BeneficiaryWalletIdType { get; set; }

    [JsonProperty("beneficiaryAccountNumber")]
    public string BeneficiaryAccountNumber { get; set; }

    [JsonProperty("beneficiaryAccountName")]
    public string BeneficiaryAccountName { get; set; }

    [JsonProperty("beneficiaryBankCbnCode")]
    public string BeneficiaryBankCbnCode { get; set; }

    [JsonProperty("amount")]
    public decimal Amount { get; set; }

    [JsonProperty("fee")]
    public decimal Fee { get; set; }

    [JsonProperty("responseCode")]
    public string ResponseCode { get; set; }

    [JsonProperty("responseDescription")]
    public string ResponseDescription { get; set; }

    [JsonProperty("requestDate")]
    public DateTime? RequestDate { get; set; }

    [JsonProperty("transactionReference")]
    public string TransactionReference { get; set; }

    [JsonProperty("payoutReference")]
    public string PayoutReference { get; set; }
}
