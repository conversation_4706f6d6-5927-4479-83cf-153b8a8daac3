using Newtonsoft.Json;

namespace Musewears.Server.Models;

public class SubscriptionEvent
{
    [JsonProperty("event")]
    public string Event { get; set; }

    [JsonProperty("uuid")]
    public string Uuid { get; set; }

    [JsonProperty("timestamp")]
    public long Timestamp { get; set; }

    [JsonProperty("data")]
    public SubscripttionEventData Data { get; set; }
}

public class SubscripttionEventData
{
    [JsonProperty("amount")]
    public decimal Amount { get; set; }

    [JsonProperty("paymentId")]
    public int? PaymentId { get; set; }

    [JsonProperty("initialPaymentId")]
    public int InitialPaymentId { get; set; }

    [JsonProperty("paymentScheduleId")]
    public int PaymentScheduleId { get; set; }

    [JsonProperty("scheduleId")]
    public string ScheduleId { get; set; }

    [JsonProperty("subscriptionId")]
    public int SubscriptionId { get; set; }

    [JsonProperty("paymentLinkReference")]
    public string PaymentLinkReference { get; set; }

    [JsonProperty("merchantReference")]
    public string MerchantReference { get; set; }

    [JsonProperty("transactionReference")]
    public string TransactionReference { get; set; }

    [JsonProperty("subscriptionName")]
    public string SubscriptionName { get; set; }

    [JsonProperty("subscriptionFrequency")]
    public string SubscriptionFrequency { get; set; }

    [JsonProperty("subscriptionInterval")]
    public int SubscriptionInterval { get; set; }

    [JsonProperty("customerId")]
    public string CustomerId { get; set; }

    [JsonProperty("customerName")]
    public string CustomerName { get; set; }

    [JsonProperty("currencyCode")]
    public string CurrencyCode { get; set; }

    [JsonProperty("startDate")]
    public long StartDate { get; set; }

    [JsonProperty("endDate")]
    public long EndDate { get; set; }
}
