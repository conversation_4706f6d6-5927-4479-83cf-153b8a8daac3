using Newtonsoft.Json;

namespace Musewears.Server.Models;

public class InvoiceEvent
{
    [JsonProperty("event")]
    public string? Event { get; set; }

    [JsonProperty("uuid")]
    public string? Uuid { get; set; }

    [JsonProperty("timestamp")]
    public long Timestamp { get; set; }

    [JsonProperty("data")]
    public InvoiceEventData? Data { get; set; }
}

public class InvoiceEventData
{
    [JsonProperty("id")]
    public int Id { get; set; }

    [JsonProperty("merchantCode")]
    public string? MerchantCode { get; set; }

    [JsonProperty("reference")]
    public string? Reference { get; set; }

    [JsonProperty("amount")]
    public decimal Amount { get; set; }

    [JsonProperty("customerName")]
    public string? CustomerName { get; set; }

    [JsonProperty("customerEmail")]
    public string? CustomerEmail { get; set; }

    [JsonProperty("currencyCode")]
    public string? CurrencyCode { get; set; }

    [JsonProperty("invoiceStatus")]
    public string? InvoiceStatus { get; set; }

    [JsonProperty("responseCode")]
    public string? ResponseCode { get; set; }

    [JsonProperty("paymentId")]
    public int PaymentId { get; set; }

    [JsonProperty("transactionReference")]
    public string? TransactionReference { get; set; }

    [JsonProperty("dateOfPayment")]
    public long DateOfPayment { get; set; }

    [JsonProperty("discountPercent")]
    public int DiscountPercent { get; set; }

    [JsonProperty("responseDescription")]
    public string? ResponseDescription { get; set; }
}
