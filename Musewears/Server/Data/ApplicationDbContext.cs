using Microsoft.EntityFrameworkCore;
using Musewears.Shared.Models;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Musewears.Server.Models;
using Musewears.Shared;

namespace Musewears.Server.Data;

public class ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : IdentityDbContext<ApplicationUser>(options)
{
    public DbSet<Wear> Wear { get; set; }

    //public DbSet<Wear> Wardrobe { get; set; }

    public DbSet<Rating> Rating { get; set; }

    public DbSet<CartItem> CartItem { get; set; }

    // public DbSet<Order> Order { get; set; }

    public DbSet<OrderItem> OrderItem { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        //    optionsBuilder.UseSqlite("your_connection_string");
        //optionsBuilder.EnableSensitiveDataLogging();

        // Choose one of the following:
        //optionsBuilder.UseSqlite().EnsureCreated(); // Only creates the database if it does not exist
        //                                            // OR
        //optionsBuilder.UseSqlite().Migrate(); // Applies pending migrations without recreating the database
    }

    //protected override void OnModelCreating(ModelBuilder modelBuilder)
    //{
    //    base.OnModelCreating(modelBuilder);

    //    // Customize the model if needed
    //    // For example, you can configure the Wear entity here
    //    modelBuilder.Entity<Wear>(entity =>
    //    {
    //        //entity.ToTable("wardrobe");
    //        entity.HasKey(e => e.Id);
    //        // Add additional configurations as needed
    //    });
    //}

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);


        builder.Entity<Rating>().HasKey(r => new { r.UserId, r.WearId });
        builder.Entity<CartItem>().HasKey(r => new { r.UserId, r.WearId });
    }
}

