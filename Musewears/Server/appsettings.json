{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "IdentityServer": {
    "Clients": {
      "Musewears.Client": {
        "Profile": "IdentityServerSPA"
      }
    }
  },
  "AllowedHosts": "*",
  "Interswitch": {
    "WebhookSecret": "your-webhook-secret-key-here",
    "MerchantCode": "your-merchant-code",
    "PayItemId": "your-pay-item-id",
    "BaseUrl": "https://sandbox.interswitchng.com", // Use production URL for live
    "ClientId": "your-client-id",
    "ClientSecret": "your-client-secret"
  },
  "OpenApi": {
    "Endpoint": {
      "Name": "Musewears.API V1"
    },
    "Document": {
      "Description": "The Musewears Service HTTP API",
      "Title": "Musewears - Musewears HTTP API",
      "Version": "v1"
    }
  }
}