using Microsoft.JSInterop;
using Pixata.Extensions;
using Square;
using Square.Cards;
using Square.Checkout.PaymentLinks;
using Square.Customers;
using Square.Payments;
using Square.Subscriptions;

namespace Musewears.Server.Services.SquareServices;

public class SquareHelper (SquareData data, IJSRuntime js)
{
  private readonly SquareClient _client = new SquareClient(token: data.AccessToken, clientOptions: new ClientOptions
  {
    BaseUrl = data.Environment == "production" ? SquareEnvironment.Production : SquareEnvironment.Sandbox
  });
  
  private IJSObjectReference? _squareJs;
  private IJSObjectReference? _squareCard;
  private string _elementId = "";

  private const string JsUri = "/Square.js";

  #region API methods

  public static Address BuildAddress(string address1, string address2, string postcode, string country)
  {
    Address address = new Address
      {
        AddressLine1 = address1,
        AddressLine2 = address2,
        PostalCode = postcode,
        Country = Country.FromCustom(country)
      };
    return address;
  }

  public async Task<string?> CreateSquareCustomer(string firstName, string surname, string email, string phone, Address address)
  {
    CreateCustomerRequest customerRequest = new CreateCustomerRequest
      {
        IdempotencyKey = Guid.NewGuid().ToString(),
        GivenName = firstName,
        FamilyName = surname,
        EmailAddress = email,
        Address = address,
        PhoneNumber = phone
      };
      // .IdempotencyKey(Guid.NewGuid().ToString())
      // .GivenName(firstName)
      // .FamilyName(surname)
      // .EmailAddress(email)
      // .Address(address)
      // .PhoneNumber(phone)
      // .Build();
    var customerResponse = await _client.Customers.CreateAsync(customerRequest);
    return customerResponse.Customer?.Id;
  }

  public async Task SetUpCard(string elementId)
  {
    _elementId = elementId;
    await js.InvokeAsync<IJSObjectReference>("import", data.Environment == "production" ? "https://web.squarecdn.com/v1/square.js" : "https://sandbox.web.squarecdn.com/v1/square.js");
    _squareJs = await js.InvokeAsync<IJSObjectReference>("import", JsUri);
    _squareCard = await _squareJs.InvokeAsync<IJSObjectReference>("addSquareCardPayment", _elementId, data.AppId, data.LocationId);
  }

  public async Task<string?> CreateSquareCard(string firstName, string surname, Address address, string customerId)
  {
    var sourceId = await _squareJs!.InvokeAsync<string>("getSquareCardToken", _squareCard);
    
    // NOTE - The following will throw an error if the postcode/zip used in the card element does not match the one you use in the address
    var cardRequest = new CreateCardRequest
    {
      IdempotencyKey = Guid.NewGuid().ToString(),
      SourceId = sourceId,
      Card = new Card
        {
          CardholderName = $"{firstName} {surname}",
          BillingAddress = address,
          CustomerId = customerId,
          ReferenceId = $"{firstName}{surname}"
        }
    };
    var cardResponse = await _client.Cards.CreateAsync(cardRequest);
    return cardResponse.Card?.Id;
  }

  public async Task<string?> CreateSquareSubscription(string customerId, string cardId, int dayOfMonth = 0)
  {
    DateTime start = dayOfMonth == 0 ? DateTime.Now : DateTime.Now.EndOfMonth().AddMilliseconds(2);
    CreateSubscriptionRequest subscriptionRequest = new CreateSubscriptionRequest
    {
      LocationId = data.LocationId,
      CustomerId = customerId,
      IdempotencyKey = Guid.NewGuid().ToString(),
      CardId = cardId,
      StartDate = start.ToString("yyyy-MM-dd")
    };
    var subscriptionResponse = await _client.Subscriptions.CreateAsync(subscriptionRequest);
    return subscriptionResponse.Subscription?.Id;
  }

  public async Task<List<Customer>> GetAllCustomers()
  {
    List<Customer> results = [];
    var customerPager = await _client.Customers.ListAsync(new ListCustomersRequest());
    var currentPage = customerPager.CurrentPage;
    results.AddRange(currentPage.Items);
    
    // 3. Loop until there's no next page
    while (customerPager.HasNextPage)
    {
      // this advances the internal cursor and updates HasNextPage
      var nextPage = await customerPager.GetNextPageAsync();
      results.AddRange(nextPage.Items);
    }
    
    // string cursor = customerResponse.Cursor;
    // while (!string.IsNullOrWhiteSpace(cursor))
    // {
    //   customerResponse = await _client.CustomersApi.ListCustomersAsync(cursor);
    //   if (customerResponse.Customers == null)
    //   {
    //     break;
    //   }
    //   results.AddRange(customerResponse.Customers);
    //   cursor = customerResponse.Cursor;
    // }
    return results;
  }

  public async Task<List<Customer>> SearchCustomers(string email)
  {
    var body = new SearchCustomersRequest
    {
      Query = new CustomerQuery
      {
        Filter = new CustomerFilter
        {
          EmailAddress = new CustomerTextFilter
          {
            Fuzzy = email
          }
        }
      }
    };
    
    List<Customer> results = [];
    SearchCustomersResponse customerResponse = await _client.Customers.SearchAsync(body);
    if (customerResponse.Customers == null)
    {
      return results;
    }
    results.AddRange(customerResponse.Customers);
    var cursor = customerResponse.Cursor;
    while (!string.IsNullOrWhiteSpace(cursor))
    {
      customerResponse = await _client.Customers.SearchAsync(body with { Cursor = cursor });
      if (customerResponse.Customers == null)
      {
        break;
      }
      results.AddRange(customerResponse.Customers);
      cursor = customerResponse.Cursor;
    }
    return results;
  }

  public Task<DeleteCustomerResponse> DeleteCustomer(string id) =>
     _client.Customers.DeleteAsync(new DeleteCustomersRequest { CustomerId = id });

  public Task<CreatePaymentLinkResponse> CreatePaymentLink(string customerId, List<OrderLineItem> lineItems, string reference)
  {
    var orderRequest = new CreateOrderRequest
    {
      Order = new Order
      {
        LocationId = data.LocationId,
        CustomerId = customerId,
        LineItems = lineItems
      },
      IdempotencyKey = Guid.NewGuid().ToString(),
    };

    var paymentLinkRequest = new CreatePaymentLinkRequest
    {
      Order = orderRequest.Order,
      IdempotencyKey = Guid.NewGuid().ToString()
    };
    
      // .Order(new Order.Builder(data.LocationId)
      //   .CustomerId(customerId)
      //   .LineItems(lineItems)
      //   .Build())
      // .IdempotencyKey(Guid.NewGuid().ToString())
      // .Build();
    return _client.Checkout.PaymentLinks.CreateAsync(paymentLinkRequest);


    // CreateOrderResponse orderResponse = await _client.OrdersApi.CreateOrderAsync(orderRequest);
    // var order = orderResponse.Order;
    // string orderId = order.Id;
    // // long total = lineItems.Select(li => Convert.ToInt32(li.Quantity) * (li.BasePriceMoney.Amount ?? 0)).Sum(t => t);
    // CreatePaymentRequest paymentRequest = new CreatePaymentRequest.Builder(
    //     await _squareJs!.InvokeAsync<string>("getSquareCardToken", _squareCard), Guid.NewGuid().ToString()
    //       // , 
    //       // new Money.Builder()
    //       //   .Amount(total)
    //       //   .Currency("GBP")
    //       //   .Build()
    //       )
    //   .Autocomplete(true)
    //   .OrderId(orderId)
    //   .ReferenceId(reference)
    //   .Build();
    // CreatePaymentResponse result = await _client.PaymentsApi.CreatePaymentAsync(paymentRequest);
    // return (order, result.Payment);
  }


  public async Task<(Order? order, Payment? Payment)> OrderWithPayment(string customerId, List<OrderLineItem> lineItems, string reference)
  {
    var orderRequest = new CreateOrderRequest
    {
      Order = new Order
      {
        LocationId = data.LocationId,
        CustomerId = customerId,
        LineItems = lineItems,
      },
      IdempotencyKey = Guid.NewGuid().ToString(),
    };
    
    var orderResponse = await _client.Orders.CreateAsync(orderRequest);
    var order = orderResponse.Order;
    var orderId = order?.Id;
    // long total = lineItems.Select(li => Convert.ToInt32(li.Quantity) * (li.BasePriceMoney.Amount ?? 0)).Sum(t => t);
    CreatePaymentRequest paymentRequest = new CreatePaymentRequest
    {
      Autocomplete = true,
      OrderId = orderId,
      ReferenceId = reference,
      IdempotencyKey = Guid.NewGuid().ToString(),
      SourceId = await _squareJs!.InvokeAsync<string>("getSquareCardToken", _squareCard),
      // AmountMoney = new Money.Builder()
      //   .Amount(total)
      //   .Currency("GBP")
      //   .Build()
    };
    
    CreatePaymentResponse result = await _client.Payments.CreateAsync(paymentRequest);
    return (order, result.Payment);
  }

  #endregion

  #region Deprecated

  // Move all the Square API calls inside this class and remove these three...
  public SquareClient GetSquareClient() =>
    _client;

  public string AppId =>
    data.AppId;

  public string LocationId =>
    data.LocationId;

  #endregion
}

