using System.ComponentModel.DataAnnotations;
using Musewears.Server.Models;
using Musewears.Shared;
using Musewears.Shared.Models;

namespace Musewears.Server.Services
{
    public interface IWardrobe
    {
        Task<List<Wear>?> GetWardrobe();

        Task<List<Wear>?> GetWears(int Skip = 0, int Limit = 20);

        Task<int?> GetTotalWearCount();

        Task<Wear?> GetWearById(string id);
        Task InsertWear(Wear wear);
        Task UpdateWear(string id, Wear wear);
        Task RemoveWear(string id);

        Task<bool> RateWear(string WearId, string UserId, [Range(0, 5)] float Stars);

        Task<bool> AddOrUpdateToUserCart(string WearId, string UserId, string Photo, string Name, int Quantity = 1);

        Task<CartItem?> GetCartItem(string WearId, string UserId);

        Task<List<CartItem>?> GetCartItems(string UserId);

        Task<bool> RemoveCartItem(CartItem cart);

        Task<bool> RemoveCartItems(List<CartItem> items, string UserId);

        Task<bool> RemoveCartItems(IEnumerable<int> cartItemIds, string UserId);

        Task<bool> UpdateCartItems(List<CartItem> items);

        // Task<bool> SaveOrder(Order order);
        //
        // Task<bool> UpdateOrderPaymentStatus(Order order, PaymentStatus status);
        //
        // Task<bool> UpdateOrderStatus(Order order, OrderStatus status);
        //
        // Task<Order?> GetOrder(string OrderId, string UserId);
        //
        // Task<Order?> GetOrderByPaymentReference(string PaymentReference);

        Task<bool> SavePayment(TransactionEventData Payment);

        // Task<List<Order>?> GetOrders(string UserId);

        // Task<bool> RemoveOrder(string OrderId, string UserId);

        Task<BillingAddress?> SaveBillingAddress(string UserId, string Name, string Email, string Address, string City, string State, string Country, string ZipCode, string Phone);

        Task<ShippingAddress?> SaveShippingAddress(string UserId, string Name, string Email, string Address, string City, string State, string Country, string ZipCode, string Phone);
    }
}