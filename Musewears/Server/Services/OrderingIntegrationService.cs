using MediatR;
using Musewears.Shared.Models;
using Ordering.API.Application.Commands;
using OrderStatus = Ordering.Domain.AggregatesModel.OrderAggregate.OrderStatus;
using Musewears.Shared.Models;

namespace Musewears.Server.Services;

/// <summary>
/// Integration service that bridges Server project and Ordering project using domain events and commands
/// This service follows DDD principles by using MediatR commands instead of direct repository access
/// </summary>
public class OrderingIntegrationService(
    IMediator mediator,
    ILogger<OrderingIntegrationService> logger) : IOrderingIntegrationService
{
    public async Task<bool> AssociateBuyerWithOrderAsync(string userId, string userName, string paymentReference, string orderId)
    {
        try
        {
            logger.LogInformation("Processing buyer association for payment reference: {PaymentReference}", paymentReference);

            // Use the ProcessInterswitchPaymentCommand to handle buyer association through domain events
            var command = new ProcessInterswitchPaymentCommand(
                MerchantReference: paymentReference,
                UserId: userId,
                UserName: userName,
                PaymentStatus: "pending", // Default status for association
                PaymentId: null,
                MerchantCustomerId: userId,
                MerchantCustomerName: userName,
                PaymentReference: paymentReference);

            var result = await mediator.Send(command);
            
            if (result)
            {
                logger.LogInformation("Successfully processed buyer association for payment reference: {PaymentReference}", paymentReference);
            }
            else
            {
                logger.LogWarning("Failed to process buyer association for payment reference: {PaymentReference}", paymentReference);
            }

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error associating buyer with order for payment reference: {PaymentReference}", paymentReference);
            return false;
        }
    }

    public async Task<bool> UpdateOrderStatusFromPaymentAsync(string paymentReference, PaymentStatus paymentStatus)
    {
        try
        {
            logger.LogInformation("Processing payment status update for payment reference: {PaymentReference} with status: {PaymentStatus}", 
                paymentReference, paymentStatus);

            // Use the ProcessInterswitchPaymentCommand to handle payment status updates through domain events
            var command = new ProcessInterswitchPaymentCommand(
                MerchantReference: paymentReference,
                UserId: "", // Will be determined from the order
                UserName: "", // Will be determined from the order
                PaymentStatus: paymentStatus.ToString(),
                PaymentId: null,
                MerchantCustomerId: null,
                MerchantCustomerName: null,
                PaymentReference: paymentReference);

            var result = await mediator.Send(command);
            
            if (result)
            {
                logger.LogInformation("Successfully processed payment status update for payment reference: {PaymentReference}", paymentReference);
            }
            else
            {
                logger.LogWarning("Failed to process payment status update for payment reference: {PaymentReference}", paymentReference);
            }

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating order status for payment reference: {PaymentReference}", paymentReference);
            return false;
        }
    }

    public async Task<int?> CreateOrFindBuyerAsync(string userId, string userName)
    {
        try
        {
            logger.LogInformation("Creating or finding buyer for user: {UserId}", userId);

            // This method is now handled by domain events when ProcessInterswitchPaymentCommand is executed
            // We return a placeholder value since the actual buyer creation is handled by domain events
            logger.LogInformation("Buyer creation/finding is handled by domain events for user: {UserId}", userId);
            return 1; // Placeholder - actual buyer ID will be determined by domain events
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error creating or finding buyer for user: {UserId}", userId);
            return null;
        }
    }

    public OrderStatus MapPaymentStatusToOrderStatus(PaymentStatus paymentStatus)
    {
        return paymentStatus switch
        {
            Musewears.Shared.Models.PaymentStatus.completed => OrderStatus.Paid,
            Musewears.Shared.Models.PaymentStatus.pending => OrderStatus.AwaitingValidation,
            Musewears.Shared.Models.PaymentStatus.failed => OrderStatus.Cancelled,
            Musewears.Shared.Models.PaymentStatus.refunded => OrderStatus.Cancelled,
            _ => OrderStatus.Submitted
        };
    }

    public async Task<bool> SetPaymentMethodVerifiedAsync(string paymentReference, int buyerId, int paymentMethodId)
    {
        try
        {
            logger.LogInformation("Processing payment method verification for payment reference: {PaymentReference}", paymentReference);

            // Use the ProcessInterswitchPaymentCommand to handle payment method verification through domain events
            var command = new ProcessInterswitchPaymentCommand(
                MerchantReference: paymentReference,
                UserId: "", // Will be determined from the order
                UserName: "", // Will be determined from the order
                PaymentStatus: "completed", // Payment method verified means payment is successful
                PaymentId: paymentMethodId,
                MerchantCustomerId: null,
                MerchantCustomerName: null,
                PaymentReference: paymentReference);

            var result = await mediator.Send(command);
            
            if (result)
            {
                logger.LogInformation("Successfully processed payment method verification for payment reference: {PaymentReference}", paymentReference);
            }
            else
            {
                logger.LogWarning("Failed to process payment method verification for payment reference: {PaymentReference}", paymentReference);
            }

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error setting payment method verified for payment reference: {PaymentReference}", paymentReference);
            return false;
        }
    }

    public async Task<bool> ProcessTransactionCreated(TransactionEvent transactionEvent)
    {
        try
        {
            logger.LogInformation("Processing TRANSACTION.CREATED for payment ID: {PaymentId}",
                transactionEvent.Data?.PaymentId);

            var command = new CreateTransactionCommand(transactionEvent);
            var result = await mediator.Send(command);

            if (result)
            {
                logger.LogInformation("Successfully processed TRANSACTION.CREATED for payment ID: {PaymentId}",
                    transactionEvent.Data?.PaymentId);
            }

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing TRANSACTION.CREATED for payment ID: {PaymentId}",
                transactionEvent.Data?.PaymentId);
            return false;
        }
    }

    public async Task<bool> ProcessTransactionUpdated(TransactionEvent transactionEvent)
    {
        try
        {
            logger.LogInformation("Processing TRANSACTION.UPDATED for payment ID: {PaymentId}",
                transactionEvent.Data?.PaymentId);

            var command = new UpdateTransactionCommand(transactionEvent);
            var result = await mediator.Send(command);

            if (result)
            {
                logger.LogInformation("Successfully processed TRANSACTION.UPDATED for payment ID: {PaymentId}",
                    transactionEvent.Data?.PaymentId);
            }

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing TRANSACTION.UPDATED for payment ID: {PaymentId}",
                transactionEvent.Data?.PaymentId);
            return false;
        }
    }

    public async Task<bool> ProcessTransactionCompleted(TransactionEvent transactionEvent)
    {
        try
        {
            logger.LogInformation("Processing TRANSACTION.COMPLETED for payment ID: {PaymentId}",
                transactionEvent.Data?.PaymentId);

            var command = new CompleteTransactionCommand(transactionEvent);
            var result = await mediator.Send(command);

            if (result)
            {
                logger.LogInformation("Successfully processed TRANSACTION.COMPLETED for payment ID: {PaymentId}",
                    transactionEvent.Data?.PaymentId);
            }

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing TRANSACTION.COMPLETED for payment ID: {PaymentId}",
                transactionEvent.Data?.PaymentId);
            return false;
        }
    }



    // private Musewears.Shared.Models.OrderStatus MapPaymentStatusToServerOrderStatus(PaymentStatus paymentStatus)
    // {
    //     return paymentStatus switch
    //     {
    //         PaymentStatus.completed => Musewears.Shared.Models.OrderStatus.confirmed,
    //         PaymentStatus.pending => Musewears.Shared.Models.OrderStatus.pending,
    //         PaymentStatus.failed => Musewears.Shared.Models.OrderStatus.cancelled,
    //         PaymentStatus.refunded => Musewears.Shared.Models.OrderStatus.cancelled,
    //         _ => Musewears.Shared.Models.OrderStatus.incomplete
    //     };
    // }
}
