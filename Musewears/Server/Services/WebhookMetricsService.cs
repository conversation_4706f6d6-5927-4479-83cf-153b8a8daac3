using System.Diagnostics.Metrics;

namespace Musewears.Server.Services;

/// <summary>
/// Interface for webhook metrics service
/// </summary>
public interface IWebhookMetricsService
{
    void RecordWebhookProcessed(string eventType, int paymentId, double durationMs);
    void RecordWebhookError(string eventType, int paymentId, double durationMs, string errorType);
    void RecordDuplicateEvent(string eventType, int paymentId);
    void RecordProcessingDuration(string eventType, double durationMs, string status = "success");
}

/// <summary>
/// Service for tracking webhook processing metrics
/// </summary>
public class WebhookMetricsService : IWebhookMetricsService, IDisposable
{
    private readonly Meter _meter;
    private readonly Counter<long> _webhookProcessedCounter;
    private readonly Counter<long> _webhookErrorCounter;
    private readonly Counter<long> _duplicateEventCounter;
    private readonly Histogram<double> _processingDurationHistogram;
    private readonly ILogger<WebhookMetricsService> _logger;

    public WebhookMetricsService(ILogger<WebhookMetricsService> logger)
    {
        _logger = logger;
        _meter = new Meter("Musewears.Webhooks", "1.0.0");
        
        _webhookProcessedCounter = _meter.CreateCounter<long>(
            "webhook_processed_total",
            "count",
            "Total number of webhooks processed");

        _webhookErrorCounter = _meter.CreateCounter<long>(
            "webhook_errors_total", 
            "count",
            "Total number of webhook processing errors");

        _duplicateEventCounter = _meter.CreateCounter<long>(
            "webhook_duplicate_events_total",
            "count", 
            "Total number of duplicate webhook events detected");

        _processingDurationHistogram = _meter.CreateHistogram<double>(
            "webhook_processing_duration_ms",
            "milliseconds",
            "Duration of webhook processing");
    }

    /// <summary>
    /// Records a successfully processed webhook
    /// </summary>
    public void RecordWebhookProcessed(string eventType, int paymentId, double durationMs)
    {
        _webhookProcessedCounter.Add(1, 
            new KeyValuePair<string, object?>("event_type", eventType),
            new KeyValuePair<string, object?>("payment_id", paymentId));

        _processingDurationHistogram.Record(durationMs,
            new KeyValuePair<string, object?>("event_type", eventType),
            new KeyValuePair<string, object?>("status", "success"));

        _logger.LogDebug("Recorded successful webhook processing: {EventType}, PaymentId: {PaymentId}, Duration: {DurationMs}ms",
            eventType, paymentId, durationMs);
    }

    /// <summary>
    /// Records a webhook processing error
    /// </summary>
    public void RecordWebhookError(string eventType, int paymentId, double durationMs, string errorType)
    {
        _webhookErrorCounter.Add(1,
            new KeyValuePair<string, object?>("event_type", eventType),
            new KeyValuePair<string, object?>("payment_id", paymentId),
            new KeyValuePair<string, object?>("error_type", errorType));

        _processingDurationHistogram.Record(durationMs,
            new KeyValuePair<string, object?>("event_type", eventType),
            new KeyValuePair<string, object?>("status", "error"));

        _logger.LogDebug("Recorded webhook processing error: {EventType}, PaymentId: {PaymentId}, Duration: {DurationMs}ms, Error: {ErrorType}",
            eventType, paymentId, durationMs, errorType);
    }

    /// <summary>
    /// Records a duplicate event detection
    /// </summary>
    public void RecordDuplicateEvent(string eventType, int paymentId)
    {
        _duplicateEventCounter.Add(1,
            new KeyValuePair<string, object?>("event_type", eventType),
            new KeyValuePair<string, object?>("payment_id", paymentId));

        _logger.LogDebug("Recorded duplicate event: {EventType}, PaymentId: {PaymentId}",
            eventType, paymentId);
    }

    /// <summary>
    /// Records webhook processing duration
    /// </summary>
    public void RecordProcessingDuration(string eventType, double durationMs, string status = "success")
    {
        _processingDurationHistogram.Record(durationMs,
            new KeyValuePair<string, object?>("event_type", eventType),
            new KeyValuePair<string, object?>("status", status));
    }

    public void Dispose()
    {
        _meter?.Dispose();
    }
}
