using Musewears.Shared.Models;
using Musewears.Shared.Models;

namespace Musewears.Server.Services;

public interface IOrderingIntegrationService
{
    /// <summary>
    /// Associates a buyer with an order and creates/verifies payment method for Interswitch payments
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="userName">User name</param>
    /// <param name="paymentReference">Interswitch payment reference</param>
    /// <param name="orderId">Order identifier from Server project</param>
    /// <returns>True if successful</returns>
    Task<bool> AssociateBuyerWithOrderAsync(string userId, string userName, string paymentReference, string orderId);

    /// <summary>
    /// Updates order status in the Ordering project based on payment status
    /// </summary>
    /// <param name="paymentReference">Interswitch payment reference</param>
    /// <param name="paymentStatus">Payment status from Interswitch</param>
    /// <returns>True if successful</returns>
    Task<bool> UpdateOrderStatusFromPaymentAsync(string paymentReference, PaymentStatus paymentStatus);

    /// <summary>
    /// Creates or finds a buyer in the Ordering project using domain events
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="userName">User name</param>
    /// <returns>Buyer ID if successful, null otherwise</returns>
    Task<int?> CreateOrFindBuyerAsync(string userId, string userName);

    /// <summary>
    /// Maps payment status to order status
    /// </summary>
    /// <param name="paymentStatus">Payment status</param>
    /// <returns>Corresponding order status</returns>
    Ordering.Domain.AggregatesModel.OrderAggregate.OrderStatus MapPaymentStatusToOrderStatus(PaymentStatus paymentStatus);

    /// <summary>
    /// Sets payment method verified for an order using buyer ID and payment method ID
    /// </summary>
    /// <param name="paymentReference">Payment reference to find the order</param>
    /// <param name="buyerId">Buyer ID</param>
    /// <param name="paymentMethodId">Payment method ID</param>
    /// <returns>True if successful</returns>
    Task<bool> SetPaymentMethodVerifiedAsync(string paymentReference, int buyerId, int paymentMethodId);

    Task<bool> ProcessTransactionCreated(TransactionEvent transactionEvent);
    Task<bool> ProcessTransactionUpdated(TransactionEvent transactionEvent);
    Task<bool> ProcessTransactionCompleted(TransactionEvent transactionEvent);
}
