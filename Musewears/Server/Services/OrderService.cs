// using System;
// using System.Security.Cryptography;
// using System.Text;
// using Musewears.Server.Models;
// using Musewears.Shared.Models;
//
// namespace Musewears.Server.Services
// {
//     public class OrderService(IWardrobe wardrobe) : IOrderService
//     {
//         public async Task<Order?> CreateOrder(string userId, List<CartItem> cartItems, PaymentStatus paymentStatus, BillingAddress billingAddress, ShippingAddress shippingAddress, string paymentReference, decimal total)
//         {
//             try
//             {
//                 var orderId = Guid.NewGuid().ToString();
//
//                 var payment = new TransactionEventData
//                 {
//                     OrderId = orderId,
//                     UserId = userId,
//                     Amount = total,
//                     PaymentReference = paymentReference,
//                     CreatedAt = DateTime.UtcNow,
//                 };
//
//                 if (await wardrobe.SavePayment(payment))
//                 {
//                     var order = new Order
//                     {
//                         Id = orderId,
//                         UserId = userId,
//                         CreatedAt = DateTime.UtcNow,
//                         OrderItems = [],
//                         PaymentStatus = paymentStatus.ToString(),
//                         Status = OrderStatus.pending.ToString(),
//                         ShippingAddressId = shippingAddress.Id,
//                         BillingAddressId = billingAddress.Id,
//                         PaymentReference = payment.PaymentReference!,
//                     };
//
//                     foreach (var cartItem in cartItems)
//                     {
//                         order.OrderItems.Add(new OrderItem
//                         {
//                             Id = cartItem.Id,
//                             UserId = userId,
//                             OrderId = order.Id,
//                             WearId = cartItem.WearId,
//                             Price = cartItem.Wear.Price,
//                             Quantity = cartItem.Quantity,
//                             CreatedAt = DateTime.UtcNow,
//                             ShippingStatus = ShippingStatus.pending.ToString()
//                         });
//                     }
//
//                     if (await wardrobe.SaveOrder(order))
//                     {
//                         //If Order was saved successfully, return order object
//                         return order;
//                     }
//                 }
//
//                 return null;
//             }
//             catch (Exception ex)
//             {
//                 Console.WriteLine("Error Creating Order {0}", ex);
//             }
//
//             return null;
//         }
//
//         public async Task<bool> DeleteOrder(string orderId, string userId)
//         {
//             try
//             {
//                 var order = await wardrobe.GetOrder(orderId, userId);
//
//                 //Remove order if order is found and if payment status is incomplete, i.e payment has not been made
//                 if (order != null && order.PaymentStatus == PaymentStatus.incomplete.ToString())
//                 {
//                     return await wardrobe.RemoveOrder(orderId, userId);
//                 }
//
//                 //Return true if order is not found, or false if payment has been made
//                 return order == null;
//             }
//             catch (Exception ex)
//             {
//                 Console.WriteLine("Error Creating Order {0}", ex);
//             }
//
//             return false;
//         }
//
//         public string GetUniqueHashForCartItems(List<CartItem> cartItems)
//         {
//             // Create a concatenated string of all cart item properties that uniquely identify them
//             var concatenatedItemDetails = string.Join("", cartItems.OrderBy(item => item.Id)
//                 .Select(item => $"{item.Id}-{item.WearId}-{item.UserId}-{item.Quantity}-{item.Wear.Price}"));
//
//             // Compute the hash of the concatenated string
//             var hashBytes = SHA256.HashData(Encoding.UTF8.GetBytes(concatenatedItemDetails));
//
//             // Convert byte array to a hex string
//             var hash = BitConverter.ToString(hashBytes).Replace("-", "").ToLowerInvariant();
//
//             return hash;
//         }
//
//         public async Task<bool> UpdateOrderPaymentStatus(string orderId, string userId, PaymentStatus status)
//         {
//             var order = await wardrobe.GetOrder(OrderId: orderId, UserId: userId);
//
//             if (order != null)
//             {
//                 if (order.PaymentStatus == PaymentStatus.completed.ToString())
//                 {
//                     //If payment has been completed, return false
//                     return false;
//                 }
//
//                 if (await wardrobe.UpdateOrderPaymentStatus(order!, status))
//                 {
//                     //Remove corresponding cart items if payment has been completed
//                     return await wardrobe.RemoveCartItems(order.OrderItems.Select(item => item.Id), userId);
//                 }
//             }
//
//             return false;
//         }
//
//         public async Task<bool> UpdateOrderPaymentStatus(TransactionEventData payment)
//         {
//             var order = await wardrobe.GetOrderByPaymentReference(PaymentReference: payment.PaymentReference!);
//
//             if (order != null)
//             {
//                 if (order.PaymentStatus == PaymentStatus.completed.ToString())
//                 {
//                     //If payment has been completed, return false
//                     return false;
//                 }
//
//                 //Update payment status
//
//                 var status = PaymentResponseCode.GetPaymentStatus(payment.ResponseCode!);
//
//
//                 if (await wardrobe.UpdateOrderPaymentStatus(order!, status))
//                 {
//                     //Remove corresponding cart items if payment has been completed
//                     return await wardrobe.RemoveCartItems(order.OrderItems.Select(item => item.Id), order.UserId);
//                 }
//             }
//
//             return false;
//         }
//
//         public async Task<bool> UpdateOrderStatus(string orderId, string userId, OrderStatus status)
//         {
//             var order = await wardrobe.GetOrder(OrderId: orderId, UserId: userId);
//
//             if (order != null)
//             {
//                 return await wardrobe.UpdateOrderStatus(order!, status);
//             }
//
//             return false;
//         }
//     }
// }
//
