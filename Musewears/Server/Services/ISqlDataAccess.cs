namespace Musewears.Server
{
    public interface ISqlDataAccess
    {
        string ConnectionStringName { get; set; }

        Task<List<T>?> QueryAsync<T, U>(string sql, U parameters);

        Task<T?> QueryFirstOrDefaultAsync<T, U>(string sql, U parameters);

        Task<List<TFirst>?> QueryAsync<TFirst, TSecond, U>(string sql, Func<TFirst, TSecond, TFirst> map, U parameters, string splitOn);

        Task<List<TFirst>?> QueryAsync<TFirst, TSecond, TThird, TFourth, U>(string sql, Func<TFirst, TSecond, TThird, TFourth, TFirst> map, U parameters, string splitOn);

        Task<List<TFirst>?> QueryAsync<TFirst, TSecond, TThird, TFourth, TFifth, U>(string sql, Func<TFirst, TSecond, TThird, TFourth, TFifth, TFirst> map, U parameters, string splitOn);

        Task<bool> ExecuteAsync<T>(string sql, T parameters);

        Task ExecuteTransactionsAsync<T>(params (string sql, T parameters)[] sqlWithParams);

        Task<O?> ExecuteScalarAsync<T, O>(string sql, T parameters);
    }
}