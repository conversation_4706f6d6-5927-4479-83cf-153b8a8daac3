using System.Data;
using Dapper;
using Npgsql;

namespace Musewears.Server.Services
{
    public class SqlDataAccess(IConfiguration config) : ISqlDataAccess
    {
        private readonly IConfiguration? _config = config;

        public string ConnectionStringName { get; set; } = "DefaultConnection";

        //if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Production")
        //{
        //    // Use production connection string
        //    ConnectionStringName = "ProductionConnection";
        //}
        //else
        //{
        //    // Use the default connection string for other environments
        //    ConnectionStringName = "DevelopmentConnection";
        //}

        public async Task<List<T>?> QueryAsync<T, U>(string sql, U parameters)
        {

            string? connectionString = _config?.GetConnectionString(ConnectionStringName);

            // Console.WriteLine($"connectionString:: {connectionString}");

            using IDbConnection connection = new NpgsqlConnection(connectionString);
            // Console.WriteLine($"sql: {sql}");
            // Console.WriteLine($"parameters: {parameters}");

            var data = await connection.QueryAsync<T>(sql, parameters!);

            //Console.WriteLine($"data: {data}");

            return data.ToList();
        }

        public async Task<T?> QueryFirstOrDefaultAsync<T, U>(string sql, U parameters)
        {

            string? connectionString = _config?.GetConnectionString(ConnectionStringName);

            // Console.WriteLine($"connectionString:: {connectionString}");

            using IDbConnection connection = new NpgsqlConnection(connectionString);
            // Console.WriteLine($"sql: {sql}");
            // Console.WriteLine($"parameters: {parameters}");

            return await connection.QueryFirstOrDefaultAsync<T>(sql, parameters!);
        }

        public async Task<List<TFirst>?> QueryAsync<TFirst, TSecond, U>(string sql, Func<TFirst, TSecond, TFirst> map, U parameters, string splitOn)
        {

            string? connectionString = _config?.GetConnectionString(ConnectionStringName);

            // Console.WriteLine($"connectionString:: {connectionString}");

            using IDbConnection connection = new NpgsqlConnection(connectionString);
            //Console.WriteLine($"sql: {sql}");
            //Console.WriteLine($"parameters: {parameters}");

            var data = await connection.QueryAsync(sql, map, param: parameters!, splitOn: splitOn);

            //Console.WriteLine($"data: {data}");

            return data.ToList();
        }


        public async Task<List<TFirst>?> QueryAsync<TFirst, TSecond, TThird, TFourth, U>(string sql, Func<TFirst, TSecond, TThird, TFourth, TFirst> map, U parameters, string splitOn)
        {

            string? connectionString = _config?.GetConnectionString(ConnectionStringName);

            // Console.WriteLine($"connectionString:: {connectionString}");

            using IDbConnection connection = new NpgsqlConnection(connectionString);
            //Console.WriteLine($"sql: {sql}");
            //Console.WriteLine($"parameters: {parameters}");

            var data = await connection.QueryAsync(sql, map, param: parameters!, splitOn: splitOn);

            //Console.WriteLine($"data: {data}");

            return data.ToList();
        }


        public async Task<List<TFirst>?> QueryAsync<TFirst, TSecond, TThird, TFourth, TFifth, U>(string sql, Func<TFirst, TSecond, TThird, TFourth, TFifth, TFirst> map, U parameters, string splitOn)
        {

            string? connectionString = _config?.GetConnectionString(ConnectionStringName);

            // Console.WriteLine($"connectionString:: {connectionString}");

            using IDbConnection connection = new NpgsqlConnection(connectionString);
            //Console.WriteLine($"sql: {sql}");
            //Console.WriteLine($"parameters: {parameters}");

            var data = await connection.QueryAsync(sql, map, param: parameters!, splitOn: splitOn);

            //Console.WriteLine($"data: {data}");

            return data.ToList();
        }

        public async Task<bool> ExecuteAsync<T>(string sql, T parameters)
        {
            string? connectionString = _config?.GetConnectionString(ConnectionStringName);

            using IDbConnection connection = new NpgsqlConnection(connectionString);

            return await connection.ExecuteAsync(sql, parameters) > 0;
        }

        public Task<O?> ExecuteScalarAsync<T, O>(string sql, T parameters)
        {
            string? connectionString = _config?.GetConnectionString(ConnectionStringName);

            using IDbConnection connection = new NpgsqlConnection(connectionString);

            return connection.ExecuteScalarAsync<O>(sql, parameters);
        }


        public async Task ExecuteTransactionsAsync<T>(params (string sql, T parameters)[] sqlWithParams)
        {
            string? connectionString = _config?.GetConnectionString(ConnectionStringName);

            using IDbConnection connection = new NpgsqlConnection(connectionString);
            connection.Open(); // Open the connection before starting the transaction

            using var transaction = connection.BeginTransaction();

            try
            {
                foreach ((string sql, T parameters) in sqlWithParams)
                {
                    await connection.ExecuteAsync(sql, parameters, transaction: transaction);
                }

                transaction.Commit();
            }
            catch (Exception ex)
            {
                transaction.Rollback();

                Console.WriteLine($"An error occurred: {ex.Message}");

                throw; // Re-throw the exception to allow the calling code to handle it
            }
        }
    }
}

