// using System;
// using Musewears.Server.Models;
// using Musewears.Shared.Models;
//
// namespace Musewears.Server.Services
// {
//     public interface IOrderService
//     {
//         Task<Order?> CreateOrder(string userId, List<CartItem> cartItems, PaymentStatus paymentStatus, BillingAddress billingAddress, ShippingAddress shippingAddress, string paymentReference, decimal total);
//
//         Task<bool> UpdateOrderPaymentStatus(string orderId, string userId, PaymentStatus status);
//
//         Task<bool> UpdateOrderPaymentStatus(TransactionEventData payment);
//
//         Task<bool> UpdateOrderStatus(string orderId, string userId, OrderStatus status);
//
//     }
// }
