using System.ComponentModel.DataAnnotations;
using Dapper;
using Musewears.Server.Models;
using Musewears.Shared;
using Musewears.Shared.Models;

namespace Musewears.Server.Services
{
    public partial class Wardrobe(ISqlDataAccess db) : IWardrobe
    {
        // private readonly ApplicationDbContext _dbContext;

        private readonly List<string> images = [
            "https://res.cloudinary.com/dm0x2rlfl/image/upload/v1711284092/Musewears/vr1zsanlhqirveapoiu7.jpg",
            "https://res.cloudinary.com/dm0x2rlfl/image/upload/v1711284091/Musewears/tcc5xmzeq5xviqk6vjs9.jpg",
            "https://res.cloudinary.com/dm0x2rlfl/image/upload/v1711284091/Musewears/negtzrronqgcp7kgtfgu.jpg",
            "https://res.cloudinary.com/dm0x2rlfl/image/upload/v1711284090/Musewears/qeicnj8a8tjvdihoyiez.jpg",
            "https://res.cloudinary.com/dm0x2rlfl/image/upload/v1711284091/Musewears/x3pl1tizzmc9zsdy2kab.jpg",
            "https://res.cloudinary.com/dm0x2rlfl/image/upload/v1711284090/Musewears/ya1jyizp3nlum0gq35sf.jpg",
            "https://res.cloudinary.com/dm0x2rlfl/image/upload/v1711284090/Musewears/qzrvlfuuxqygq70layst.jpg",
            "https://res.cloudinary.com/dm0x2rlfl/image/upload/v1711284090/Musewears/afpdhojghgehuyosnzon.jpg",
            "https://res.cloudinary.com/dm0x2rlfl/image/upload/v1711284090/Musewears/j420xzu8kajp7j2q0wqp.jpg"

        ];


        private readonly List<string> names = [
            "Air Force 1 X",
            "Love Nana ‘20",
            "New Green Jacket",
            "Classic Dress",
            "Spring Collection",
            "School Collection",
            "Summer Cap",
            "Classic Kid"

        ];

        // _dbContext = dbContext;
        //string sql = "CREATE TABLE IF NOT EXIST Wear(Name, Photo, Id, IsNew, Price, Currency)";
        //_dbContext.Database.EnsureCreated();
        //_dbContext.Database.Migrate();

        public async Task<List<Wear>?> GetWardrobe()
        {
            string sql = @"select * from ""Wear""";

            // Random random = new();
            // string randomImage = images[random.Next(images.Count)];
            // string randomName = names[random.Next(names.Count)];
            // int randomAmount = random.Next(10000, 25000);

            // await InsertWear(new Wear
            // {
            //     Currency = "₦",
            //     Id = Guid.NewGuid().ToString(),
            //     IsNew = true,
            //     Name = randomName,
            //     Photo = randomImage,
            //     Price = randomAmount,
            //     CostPrice = randomAmount
            // });

            return await db.QueryAsync<Wear, dynamic>(sql, new { });
        }

        public async Task<List<Wear>?> GetWears(int Skip, int Limit = 20)
        {
            string sql = @"select * from ""Wear"" ORDER BY ""Id"" LIMIT @Limit OFFSET @Skip";

            return await db.QueryAsync<Wear, dynamic>(sql, new
            {
                Limit,
                Skip
            });
        }

        public Task<int?> GetTotalWearCount()
        {
            string sql = @"SELECT COUNT(*) FROM ""Wear""";

            return db.ExecuteScalarAsync<dynamic, int?>(sql, new { });
        }

        public Task InsertWear(Wear wear)
        {
            string sql = @"INSERT INTO ""Wear"" (""Name"", ""Photo"", ""Id"", ""IsNew"", ""Price"", ""Currency"")
                           values (@Name, @Photo, @Id, @IsNew, @Price, @Currency);";

            return db.ExecuteAsync(sql, wear);
        }


        public async Task<bool> RateWear(string WearId, string UserId, float Stars)
        {

            string query = @"
                        INSERT INTO ""Rating"" (""WearId"", ""UserId"", ""Stars"") 
                        SELECT @WearId, @UserId, @Stars 
                        WHERE NOT EXISTS (
                            SELECT 1 FROM ""Rating"" 
                            WHERE ""WearId"" = @WearId AND ""UserId"" = @UserId
                        );
                        
                        UPDATE ""Rating"" 
                        SET ""Stars"" = @Stars 
                        WHERE ""WearId"" = @WearId AND ""UserId"" = @UserId;";

            try
            {

                await db.ExecuteAsync(query, new
                {
                    WearId,
                    UserId,
                    Stars
                });

                return true;
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"An error occurred: {ex.Message}");

                return false;
            }
        }


        public async Task<bool> AddOrUpdateToUserCart(string WearId, string UserId, string Photo, string Name, int Quantity)
        {
            if (string.IsNullOrEmpty(UserId) || string.IsNullOrEmpty(WearId))
            {
                return false;
            }

            string query = @"
                            INSERT INTO ""CartItem"" (""WearId"", ""UserId"", ""Quantity"", ""Photo"", ""Name"") 
                            SELECT @WearId, @UserId, @Quantity, @Photo, @Name
                            WHERE NOT EXISTS (
                                SELECT 1 FROM ""CartItem"" 
                                WHERE ""WearId"" = @WearId AND ""UserId"" = @UserId
                            );
                            UPDATE ""CartItem"" 
                            SET ""Quantity"" = @Quantity 
                            WHERE ""WearId"" = @WearId AND ""UserId"" = @UserId;";

            try
            {

                await db.ExecuteAsync(query, new
                {
                    WearId,
                    UserId,
                    Quantity,
                    Name,
                    Photo
                });

                return true;
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"An error occurred: {ex.Message}");

                return false;
            }
        }

        Task<Wear?> IWardrobe.GetWearById(string Id)
        {

            string sql = @"SELECT * FROM ""Wear"" WHERE ""Id"" = @Id LIMIT 1;";
            return db.QueryAsync<Wear, dynamic>(sql, new { Id })
                .ContinueWith(result => result.Result?.FirstOrDefault());
        }

        Task<CartItem?> IWardrobe.GetCartItem(string WearId, string UserId)
        {
            string sql = @"SELECT * FROM ""CartItem"" WHERE ""WearId"" = @WearId AND ""UserId"" = @UserId LIMIT 1;";
            return db.QueryAsync<CartItem, dynamic>(sql, new { WearId, UserId })
                .ContinueWith(result => result.Result?.FirstOrDefault());
        }

        Task<List<CartItem>?> IWardrobe.GetCartItems(string UserId)
        {
            // var column = _dbContext.Model.FindEntityType(typeof(CartItem))?.FindProperty("Quantity");
            // var columnType = column?.GetColumnType();

            // Console.WriteLine("columnType: " + columnType);

            // return _dbContext.CartItem?.Where(item => item.UserId == UserId)?.ToListAsync() ?? Task.FromResult<List<CartItem>?>(null);

            string sql = @"
                       SELECT c.*, w.*
                       FROM ""CartItem"" c
                       LEFT JOIN ""Wear"" w ON c.""WearId"" = w.""Id""
                       WHERE c.""UserId"" = @UserId;";
            // string sql = @"SELECT * FROM ""CartItem"" WHERE ""WearId"" = @WearId AND ""UserId"" = @UserId;";

            // The map function will set the Wear property on the CartItem object.
            static CartItem map(CartItem cartItem, Wear wear)
            {
                cartItem.Wear = wear; // This will set the Wear property of CartItem
                return cartItem;
            }

            return db.QueryAsync<CartItem, Wear, dynamic>(sql, map, new { UserId }, splitOn: "Id");
        }

        Task IWardrobe.RemoveWear(string Id)
        {

            string sql = @"DELETE FROM ""Wear"" WHERE ""Id"" = @Id";
            return db.ExecuteAsync(sql, new { Id });
        }

        Task IWardrobe.UpdateWear(string id, Wear wear)
        {

            string sql = @"UPDATE Wear
                           SET ""Name"" = @Name, Photo = @Photo
                           WHERE ""Id"" = @Id;";
            return db.ExecuteAsync(sql, new { Id = id, wear.Name, wear.Photo });
        }

        public async Task<bool> RemoveCartItem(CartItem cart)
        {

            string sql = @"DELETE FROM ""CartItem"" WHERE ""WearId"" = @WearId AND ""UserId"" = @UserId;";

            try
            {
                await db.ExecuteAsync(sql, new { cart.WearId, cart.UserId });
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }


        public async Task<bool> RemoveCartItems(List<CartItem> items, string UserId)
        {

            // Extract distinct Id values from the cartItems list
            var ids = items.Select(item => item.Id).Distinct().ToList();

            // Construct the SQL query using ANY operator
            string sql = @"
                DELETE FROM ""CartItem""
                WHERE ""Id"" = ANY(@Ids)
                AND ""UserId"" = @UserId;";

            try
            {
                return await db.ExecuteAsync(sql, new { Ids = ids, UserId });
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error deleting cart items: {0}", ex.Message);
                return false;
            }
        }

        public async Task<bool> RemoveCartItems(IEnumerable<int> items, string UserId)
        {

            // Construct the SQL query using ANY operator
            string sql = @"
                DELETE FROM ""CartItem""
                WHERE ""Id"" = ANY(@items)
                AND ""UserId"" = @UserId;";

            try
            {
                await db.ExecuteAsync(sql, new { items, UserId });
                return true;
            }
            catch (Exception)
            {
                Console.WriteLine("Error deleting cart items");
                return false;
            }
        }

        public async Task<bool> UpdateCartItems(List<CartItem> items)
        {
            string sql = @"UPDATE ""CartItem"" SET ""Quantity"" = @Quantity WHERE ""WearId"" = @WearId AND ""UserId"" = @UserId;";

            try
            {
                await db.ExecuteAsync(sql, items);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

//         public async Task<List<Order>?> GetOrders(string UserId)
//         {
//             string sql = @"
//                        SELECT o.*, oi.*, sa.*, ba.*, pm.*
//                        FROM ""Order"" o
//                        LEFT JOIN ""OrderItem"" oi ON o.""Id"" = oi.""OrderId""
//                        LEFT JOIN ""ShippingAddress"" sa ON o.""ShippingAddressId"" = sa.""Id""
//                        LEFT JOIN ""BillingAddress"" ba ON o.""BillingAddressId"" = ba.""Id""
//                        LEFT JOIN ""Payment"" pm ON o.""PaymentReference"" = pm.""PaymentReference""
//                        WHERE o.""UserId"" = @UserId;";
//
//             var orderDictionary = new Dictionary<string, Order>();
//
//             // The map function will set the OrderItems, ShippingAddress, and BillingAddress property on the Order object.
//             Order map(Order order, OrderItem orderItem, ShippingAddress shippingAddress, BillingAddress billingAddress, TransactionEventData payment)
//             {
//
//                 if (!orderDictionary.TryGetValue(order.Id, out Order? orderEntry))
//                 {
//                     orderEntry = order;
//                     orderEntry.OrderItems = [];
//                     orderDictionary.Add(orderEntry.Id, orderEntry);
//                 }
//
//                 if (orderItem != null)
//                 {
//                     orderEntry.OrderItems.Add(orderItem);
//                 }
//
//                 // Console.WriteLine("Orders: " + orderDictionary.Count);
//
//                 orderEntry.ShippingAddress = shippingAddress;
//                 orderEntry.BillingAddress = billingAddress;
//
//                 // Use reflection to set Payment property to avoid conditional compilation issues
//                 var paymentProperty = orderEntry.GetType().GetProperty("Payment");
//                 if (paymentProperty != null)
//                 {
//                     paymentProperty.SetValue(orderEntry, payment);
//                 }
//                 return orderEntry;
//             }
//
//             await db.QueryAsync<Order, OrderItem, ShippingAddress, BillingAddress, TransactionEventData, dynamic>(sql, map, new { UserId }, splitOn: "Id,Id,Id,PaymentReference");
//
//             return [.. orderDictionary.Values];
//         }
//
//         public Task<Order?> GetOrder(string OrderId, string UserId)
//         {
//             string sql = @"
//                        SELECT o.*, oi.*, sa.*, ba.*
//                        FROM ""Order"" o
//                        LEFT JOIN ""OrderItem"" oi ON o.""Id"" = oi.""OrderId""
//                        LEFT JOIN ""ShippingAddress"" sa ON o.""ShippingAddressId"" = sa.""Id""
//                        LEFT JOIN ""BillingAddress"" ba ON o.""BillingAddressId"" = ba.""Id""
//                        LEFT JOIN ""Payment"" pm ON o.""PaymentReference"" = pm.""PaymentReference""
//                        WHERE o.""UserId"" = @UserId AND o.""Id"" = @OrderId
//                        LIMIT 1;";
//
//
//             var orderDictionary = new Dictionary<string, Order>();
//
//             // The map function will set the OrderItems, ShippingAddress, and BillingAddress property on the Order object.
//             Order map(Order order, OrderItem orderItem, ShippingAddress shippingAddress, BillingAddress billingAddress, TransactionEventData payment)
//             {
//
//                 if (!orderDictionary.TryGetValue(order.Id, out Order? orderEntry))
//                 {
//                     orderEntry = order;
//                     orderEntry.OrderItems = [];
//                     orderDictionary.Add(orderEntry.Id, orderEntry);
//                 }
//
//                 if (orderItem != null)
//                 {
//                     orderEntry.OrderItems.Add(orderItem);
//                 }
//
//                 orderEntry.ShippingAddress = shippingAddress;
//                 orderEntry.BillingAddress = billingAddress;
//
//                 // Use reflection to set Payment property to avoid conditional compilation issues
//                 var paymentProperty = orderEntry.GetType().GetProperty("Payment");
//                 if (paymentProperty != null)
//                 {
//                     paymentProperty.SetValue(orderEntry, payment);
//                 }
//                 return orderEntry;
//             }
//
//             return db.QueryAsync<Order, OrderItem, ShippingAddress, BillingAddress, TransactionEventData, dynamic>(sql, map, new { OrderId, UserId }, splitOn: "Id,Id,Id,PaymentReference")
//                 .ContinueWith(result => result.Result?.FirstOrDefault());
//         }
//
//         public async Task<bool> SaveOrder(Order order)
//         {
//             // try
//             // {
//             //     _dbContext.Order.Add(order);
//             //     await _dbContext.SaveChangesAsync();
//             // }
//             // catch (Exception)
//             // {
//
//             //     throw;
//             // }
//
//             // Start a new transaction
//             try
//             {
//
//                 var queries = new List<(string, object)>
//                 {
//                     // Insert or update the Order
//                     (@"INSERT INTO ""Order"" (""Id"", ""UserId"", ""CreatedAt"", ""PaymentStatus"", ""Status"", ""ShippingAddressId"", ""BillingAddressId"", ""PaymentReference"")
//                     VALUES (@Id, @UserId, @CreatedAt, @PaymentStatus, @Status, @ShippingAddressId, @BillingAddressId, @PaymentReference)
//                     ON CONFLICT (""Id"")
//                     DO UPDATE SET
//                         ""UpdatedAt"" = EXCLUDED.""UpdatedAt"",
//                         ""PaymentStatus"" = EXCLUDED.""PaymentStatus"",
//                         ""ShippingAddressId"" = EXCLUDED.""ShippingAddressId"",
//                         ""BillingAddressId"" = EXCLUDED.""BillingAddressId"",
//                         ""Status"" = EXCLUDED.""Status"";", order)
//                 };
//
//                 // Insert or update the OrderItems
//                 foreach (var item in order.OrderItems)
//                 {
//                     string itemQuery = @"
//                     INSERT INTO ""OrderItem"" (""Id"", ""UserId"", ""OrderId"", ""WearId"", ""Quantity"", ""Price"", ""ShippingStatus"", ""CreatedAt"")
//                     VALUES (@Id, @UserId, @OrderId, @WearId, @Quantity, @Price, @ShippingStatus, @CreatedAt)
//                     ON CONFLICT (""Id"")
//                     DO UPDATE SET
//                         ""UpdatedAt"" = EXCLUDED.""UpdatedAt"",
//                         ""UserId"" = EXCLUDED.""UserId"",
//                         ""WearId"" = EXCLUDED.""WearId"",
//                         ""Quantity"" = EXCLUDED.""Quantity"",
//                         ""Price"" = EXCLUDED.""Price"",
//                         ""ShippingStatus"" = EXCLUDED.""ShippingStatus"";";
//
//                     queries.Add((itemQuery, item));
//                 }
//
//                 await db.ExecuteTransactionsAsync(queries.ToArray());
//
//                 return true;
//             }
//             catch (Exception)
//             {
//                 // Attempt to roll back the transaction
//                 // Console.WriteLine($"An error occurred: {ex.Message}");
//                 return false;
//             }
//
//             //string query = @"
//             //                INSERT INTO ""Order""
//             //                WHERE NOT EXISTS (
//             //                    SELECT 1 FROM ""Order"" 
//             //                    WHERE ""OrderId"" = @OrderId AND ""UserId"" = @UserId
//             //                );
//             //                UPDATE ""Order""
//             //                SET ""OrderItems"" = @OrderItems
//             //                SET ""UpdatedAt"" = @UpdatedAt
//             //                WHERE ""OrderId"" = @OrderId AND ""UserId"" = @UserId;";
//
//             //try
//             //{
//
//             //    await _db.ExecuteAsync(query, order);
//
//             //    return true;
//             //}
//             //catch (Exception ex)
//             //{
//             //    // Log the exception
//             //    Console.WriteLine($"An error occurred: {ex.Message}");
//
//             //    return false;
//             //}
//         }
//
//         public async Task<bool> RemoveOrder(string OrderId, string UserId)
//         {
//             string sql = @"
//                DELETE FROM ""OrderItem""
//                USING ""Order""
//                WHERE ""Order"".Id = ""OrderItem"".OrderId AND ""Order"".UserId = @UserId AND ""Order"".Id = @OrderId;
//
//                DELETE FROM ""Order""
//                WHERE ""Id"" = @OrderId AND ""UserId"" = @UserId;";
//
//             try
//             {
//                 await db.ExecuteAsync(sql, new { OrderId, UserId });
//                 return true;
//             }
//             catch (Exception ex)
//             {
//                 // Log the exception
//                 Console.WriteLine($"An error occurred: {ex.Message}");
//                 return false;
//             }
//         }
//
//         public async Task<bool> UpdateOrderPaymentStatus(Order order, PaymentStatus status)
//         {
//             if (order == null)
//             {
//                 return false;
//             }
//
//             string query = @"
//                             UPDATE ""Order"" 
//                             SET ""PaymentStatus"" = @PaymentStatus
//                             WHERE ""Id"" = @Id AND ""UserId"" = @UserId;";
//
//
//             try
//             {
//
//                 await db.ExecuteAsync(query, new { order.Id, order.UserId, PaymentStatus = status.ToString() });
//
//                 return true;
//             }
//             catch (Exception ex)
//             {
//                 // Log the exception
//                 Console.WriteLine($"An error occurred: {ex.Message}");
//
//                 return false;
//             }
//         }
//
//         public async Task<bool> UpdateOrderStatus(Order order, OrderStatus status)
//         {
//             if (order == null)
//             {
//                 return false;
//             }
//
//             string query = @"
//                             UPDATE ""Order"" 
//                             SET ""Status"" = @Status
//                             WHERE ""Id"" = @Id AND ""UserId"" = @UserId;";
//
//             try
//             {
//
//                 await db.ExecuteAsync(query, new { order.Id, order.UserId, Status = status.ToString() });
//
//                 return true;
//             }
//             catch (Exception ex)
//             {
//                 // Log the exception
//                 Console.WriteLine($"An error occurred: {ex.Message}");
//
//                 return false;
//             }
//         }

        public async Task<BillingAddress?> SaveBillingAddress(string UserId, string Name, string Email, string Address, string City, string State, string Country, string ZipCode, string Phone)
        {
            if (!new EmailAddressAttribute().IsValid(Email))
            {

                throw new ValidationException("Invalid email address format");
            }

            if (string.IsNullOrEmpty(UserId) || string.IsNullOrEmpty(Name) || string.IsNullOrEmpty(Email) || string.IsNullOrEmpty(Address) || string.IsNullOrEmpty(City) || string.IsNullOrEmpty(Country) || string.IsNullOrEmpty(ZipCode) || string.IsNullOrEmpty(Phone))
            {
                throw new ValidationException("All Billing Address fields are required");
            }

            string query = @"
                            INSERT INTO ""BillingAddress"" (""Id"", ""UserId"", ""Name"", ""Email"", ""Address"", ""City"", ""State"", ""Country"", ""ZipCode"", ""Phone"")
                            VALUES (@Id, @UserId, @Name, @Email, @Address, @City, @State, @Country, @ZipCode, @Phone)
                            ON CONFLICT (""Id"")
                            DO UPDATE SET
                                ""Name"" = EXCLUDED.""Name"",
                                ""Email"" = EXCLUDED.""Email"",
                                ""Address"" = EXCLUDED.""Address"",
                                ""City"" = EXCLUDED.""City"",
                                ""State"" = EXCLUDED.""State"",
                                ""Country"" = EXCLUDED.""Country"",
                                ""ZipCode"" = EXCLUDED.""ZipCode"",
                                ""Phone"" = EXCLUDED.""Phone"";";

            try
            {
                var address = new BillingAddress
                {
                    Id = Guid.NewGuid().ToString(),
                    UserId = UserId,
                    Name = Name,
                    Email = Email,
                    Address = Address,
                    City = City,
                    State = State,
                    Country = Country,
                    ZipCode = ZipCode,
                    Phone = Phone
                };

                await db.ExecuteAsync(query, address);
                return address;
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"An error occurred: {ex.Message}");

                throw; // Re-throw the exception
            }
        }

        public async Task<ShippingAddress?> SaveShippingAddress(string UserId, string Name, string Email, string Address, string City, string State, string Country, string ZipCode, string Phone)
        {
            if (!new EmailAddressAttribute().IsValid(Email))
            {
                throw new ValidationException("Invalid email address format");
            }

            if (string.IsNullOrEmpty(UserId) || string.IsNullOrEmpty(Name) || string.IsNullOrEmpty(Email) || string.IsNullOrEmpty(Address) || string.IsNullOrEmpty(City) || string.IsNullOrEmpty(Country) || string.IsNullOrEmpty(ZipCode) || string.IsNullOrEmpty(Phone))
            {
                throw new ValidationException("All Shipping Address fields are required");
            }

            string query = @"
                            INSERT INTO ""ShippingAddress"" (""Id"", ""UserId"", ""Name"", ""Email"", ""Address"", ""City"", ""State"", ""Country"", ""ZipCode"", ""Phone"")
                            VALUES (@Id, @UserId, @Name, @Email, @Address, @City, @State, @Country, @ZipCode, @Phone)
                            ON CONFLICT (""Id"")
                            DO UPDATE SET
                                ""Name"" = EXCLUDED.""Name"",
                                ""Email"" = EXCLUDED.""Email"",
                                ""Address"" = EXCLUDED.""Address"",
                                ""City"" = EXCLUDED.""City"",
                                ""State"" = EXCLUDED.""State"",
                                ""Country"" = EXCLUDED.""Country"",
                                ""ZipCode"" = EXCLUDED.""ZipCode"",
                                ""Phone"" = EXCLUDED.""Phone"";";

            try
            {
                var address = new ShippingAddress
                {
                    Id = Guid.NewGuid().ToString(),
                    UserId = UserId,
                    Name = Name,
                    Email = Email,
                    Address = Address,
                    City = City,
                    State = State,
                    Country = Country,
                    ZipCode = ZipCode,
                    Phone = Phone
                };

                await db.ExecuteAsync(query, address);
                return address;
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"An error occurred: {ex.Message}");

                throw; // Re-throw the exception
            }
        }

        // public Task<Order?> GetOrderByPaymentReference(string PaymentReference)
        // {
        //     var sql = "SELECT * FROM \"Order\" WHERE \"PaymentReference\" = @PaymentReference LIMIT 1";
        //     return db.QueryFirstOrDefaultAsync<Order, dynamic>(sql, new { PaymentReference });
        // }

        public async Task<bool> SavePayment(TransactionEventData Payment)
        {
            Console.WriteLine("Saving Payment: ");

            var parameters = new DynamicParameters();
            var insertColumns = new List<string>();
            var insertValues = new List<string>();
            var updateSetList = new List<string>();

            // Use reflection to iterate over all properties of the Payment class
            foreach (var prop in typeof(TransactionEventData).GetProperties())
            {
                // Get the value of the property
                var value = prop.GetValue(Payment);
                // Check if the property is not null and not an empty string
                if (value != null && !(prop.PropertyType == typeof(string) && string.IsNullOrEmpty((string)value)))
                {
                    // Add the property to the INSERT part of the SQL statement
                    insertColumns.Add($"\"{prop.Name}\"");
                    insertValues.Add($"@{prop.Name}");

                    // Exclude the identifier column from the update set list
                    if (prop.Name != nameof(Payment.PaymentReference))
                    {
                        // Add the property to the SET part of the SQL statement for UPDATE
                        updateSetList.Add($"\"{prop.Name}\" = EXCLUDED.\"{prop.Name}\"");
                    }

                    parameters.Add($"@{prop.Name}", value);
                }
            }

            var insertColumnsSql = string.Join(", ", insertColumns);
            var insertValuesSql = string.Join(", ", insertValues);
            var updateSetSql = string.Join(", ", updateSetList);

            // Construct the SQL statement for the upsert operation
            var upsertSql = $@"
            INSERT INTO ""Payment"" ({insertColumnsSql})
            VALUES ({insertValuesSql})
            ON CONFLICT (""PaymentReference"")
            DO UPDATE SET {updateSetSql};";


            return await db.ExecuteAsync(upsertSql, Payment);

        }
    }
}

