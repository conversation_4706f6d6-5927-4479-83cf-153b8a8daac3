using System.Diagnostics;

namespace Musewears.Server.Middleware;

/// <summary>
/// Middleware to add correlation IDs and metrics for webhook processing
/// </summary>
public class WebhookCorrelationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<WebhookCorrelationMiddleware> _logger;

    public WebhookCorrelationMiddleware(RequestDelegate next, ILogger<WebhookCorrelationMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Only apply to webhook endpoints
        if (!context.Request.Path.StartsWithSegments("/api/InterswitchWebhook"))
        {
            await _next(context);
            return;
        }

        // Generate or extract correlation ID
        var correlationId = context.Request.Headers["X-Correlation-ID"].FirstOrDefault() 
                           ?? context.Request.Headers["X-Request-ID"].FirstOrDefault()
                           ?? Guid.NewGuid().ToString();

        // Add correlation ID to response headers
        context.Response.Headers.Add("X-Correlation-ID", correlationId);

        // Create activity for tracing
        using var activity = Activity.Current?.Source.StartActivity("WebhookProcessing");
        activity?.SetTag("correlation.id", correlationId);
        activity?.SetTag("webhook.path", context.Request.Path);

        // Add correlation ID to log context (simplified without Serilog dependency)
        using (var scope = _logger.BeginScope(new Dictionary<string, object>
        {
            ["CorrelationId"] = correlationId,
            ["WebhookPath"] = context.Request.Path.ToString()
        }))
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                _logger.LogInformation("Starting webhook processing for {Path} with correlation ID {CorrelationId}", 
                    context.Request.Path, correlationId);

                await _next(context);

                stopwatch.Stop();
                
                _logger.LogInformation("Completed webhook processing for {Path} in {ElapsedMs}ms with status {StatusCode}", 
                    context.Request.Path, stopwatch.ElapsedMilliseconds, context.Response.StatusCode);

                activity?.SetTag("http.status_code", context.Response.StatusCode);
                activity?.SetTag("duration.ms", stopwatch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                
                _logger.LogError(ex, "Error processing webhook for {Path} after {ElapsedMs}ms", 
                    context.Request.Path, stopwatch.ElapsedMilliseconds);

                activity?.SetTag("error", true);
                activity?.SetTag("error.message", ex.Message);
                activity?.SetTag("duration.ms", stopwatch.ElapsedMilliseconds);
                
                throw;
            }
        }
    }
}
