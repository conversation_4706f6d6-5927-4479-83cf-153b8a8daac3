#components-reconnect-modal {
    /* display: none; */
    /* Hide by default */
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
}

.components-reconnect-hide {
    display: none;
}

/* Styles for individual reconnect states */
.components-reconnect-show,
.components-reconnect-failed,
.components-reconnect-rejected {
    /* display: none; */
    /* Hide all reconnect UI states by default */
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    text-align: center;
    z-index: 1000;
}

.components-reconnect-show>div,
.components-reconnect-failed>div,
.components-reconnect-rejected>div {

    display: none;
}

/* Specific styles for the reconnecting state */
.components-reconnect-show>.show {

    display: block;
    background-color: rgba(255, 0, 0, 1);
    /* Semi-transparent black background */
    color: white;
    /* White text color */
}

/* Specific styles for the failed state */
.components-reconnect-failed>.failed {

    display: block;
    background-color: rgba(255, 0, 0, 1);
    /* Semi-transparent red background */
    color: white;
    /* White text color */
}

/* Specific styles for the rejected state */
.components-reconnect-rejected>.rejected {
    display: block;
    background-color: rgba(255, 0, 0, 1);
    /* Semi-transparent red background */
    color: white;
    /* White text color */
}