// This should be called from Blazor to pass the DotNetObjectReference to JavaScript
function setPaymentHandler(dotNetReference) {
    window.paymentHandler = dotNetReference;
}

function paymentCallback(response, userId) {
    console.log(response);
    // console.log("orderId: " + orderId);
    console.log("userId: " + userId);

    // Use the DotNetObjectReference to invoke the method on the Blazor component
    window.paymentHandler.invokeMethodAsync('OnPaymentResponse', response, userId);

    // You can call a Blazor method to handle the response if needed
    // DotNet.invokeMethodAsync('Musewears.Server', 'OnPaymentResponse', response, orderId, userId);
}

//sample payment request
var samplePaymentRequest = {
    merchant_code: "MX190087",
    pay_item_id: "6101231",
    txn_ref: "sample_txn_ref_123309",
    site_redirect_url: "https://musewears.onrender.com/",
    amount: 10000,
    currency: 566, // ISO 4217 numeric code of the currency used
    onComplete: paymentCallback,
    mode: 'TEST'
};

// You need to ensure that this function is available globally if you're calling it from Blazor

function initiatePayment(paymentRequest, userId) {
    paymentRequest.onComplete = (response) => {
        paymentCallback(response, userId);
    }; // Assign the callback function
    paymentRequest.headers = {
        "X-CSRF-TOKEN": window.CSRF_TOKEN
    };
    window.webpayCheckout(paymentRequest);
}