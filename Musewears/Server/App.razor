<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">
    <link href="https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,500,600,700,800,900&display=swap"
        rel="stylesheet">

    @* <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content=""> *@

    <title>Musewears</title>
    <base href="/" />
    <!-- Additional CSS Files -->
    <!-- Your existing links to stylesheets and scripts -->

    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/responsive.css">

    <link rel="stylesheet" type="text/css" href="assets/css/bootstrap.min.css">

    <link rel="stylesheet" type="text/css" href="assets/css/font-awesome.css">

    <link rel="stylesheet" href="assets/css/templatemo-hexashop.css">

    <link rel="stylesheet" href="assets/css/owl-carousel.css">

    <link rel="stylesheet" href="assets/css/lightbox.css">

    <link rel="apple-touch-icon" sizes="512x512" href="icon-512.png" />
    <link rel="apple-touch-icon" sizes="192x192" href="icon-192.png" />

    <link href="assets/css/app.css" rel="stylesheet" />
    @* <link href="site.css" rel="stylesheet" /> *@
    <link href="Musewears.Server.styles.css" rel="stylesheet" />
    <link href="Musewears.Client.styles.css" rel="stylesheet" />
    @* <link href="manifest.json" rel="manifest" /> *@
    <link rel="apple-touch-icon" sizes="512x512" href="icon-512.png" />
    <link rel="apple-touch-icon" sizes="192x192" href="icon-192.png" />

    <!-- Favicon -->
    <link rel="shortcut icon" type="image/png" href="assets/images/logo.png">
    <link rel="icon" type="image/png" href="assets/images/logo.png">


    <link href="assets/css/reconnection-style.css" rel="stylesheet" />
    
    @* Setup MudBlazor *@
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" rel="stylesheet" />
    <link href="@Assets["_content/MudBlazor/MudBlazor.min.css"]" rel="stylesheet" />

    <!-- google font -->
    @* <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,700" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Poppins:400,700&display=swap" rel="stylesheet">
    <!-- fontawesome -->
    <link rel="stylesheet" href="assets/css/all.min.css">
    <!-- bootstrap -->
    <link rel="stylesheet" href="assets/bootstrap/css/bootstrap.min.css">
    <!-- owl carousel -->
    <link rel="stylesheet" href="assets/css/owl.carousel.css">
    <!-- magnific popup -->
    <link rel="stylesheet" href="assets/css/magnific-popup.css">
    <!-- animate css -->
    <link rel="stylesheet" href="assets/css/animate.css">
    <!-- mean menu css -->
    <link rel="stylesheet" href="assets/css/meanmenu.min.css">
    <!-- main style -->
    <link rel="stylesheet" href="assets/css/main.css">
    <!-- responsive -->
    <link rel="stylesheet" href="assets/css/responsive.css"> *@
    <HeadOutlet @rendermode="RenderModeForPage" />
</head>

<body>
    <Routes @rendermode="RenderModeForPage" />

    @* <div id="components-reconnect-modal" class="components-reconnect-hide">
    <div class="show">
    <p>
    Attempting to reconnect...
    </p>
    </div>
    <div class="failed">
    <p>
    Could not reconnect. Check your connection.
    </p>
    </div>
    <div class="rejected">
    <p>
    Reconnection rejected. Please refresh the page.
    </p>
    </div>
    </div> *@



    @*<div id="main-wrapper" class="section">
    Loading...
    </div>*@

    @*<div id="blazor-error-ui">
    An unhandled error has occurred.
    <a href="" class="reload">Reload</a>
    <a class="dismiss">🗙</a>
    </div>*@

    <div id="components-reconnect-modal" class="components-reconnect-hide">
        <div class="show">
            <p>
                Attempting to reconnect...
            </p>
        </div>
        <div class="failed">
            <p>
                Failed to connect. Check your connection.
            </p>
        </div>
        <div class="rejected">
            <p>
                Failed to connect. Check your connection.
            </p>
        </div>
    </div>

    @* <script
    src="_content/Microsoft.AspNetCore.Components.WebAssembly.Authentication/AuthenticationService.js"></script> *@
    @* <script src="_framework/blazor.webassembly.js"></script> *@
    <script src="_framework/blazor.web.js"></script>
    @*<script>navigator.serviceWorker.register('service-worker.js');</script>*@

    <!-- jQuery -->
    <script src="assets/js/jquery-2.1.0.min.js"></script>
    @* <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script> *@
    @* <script src="owl.carousel.min.js"></script> *@
    <script src="owl-init.js"></script>

    <!-- Bootstrap -->
    <script src="assets/js/popper.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>

    <!-- Plugins -->
    <script src="assets/js/owl-carousel.js"></script>
    <script src="assets/js/owlCarouselInit.js"></script>
    <script src="assets/js/interswitch-script.js"></script>
    <script src="assets/js/accordions.js"></script>
    <script src="assets/js/datepicker.js"></script>
    <script src="assets/js/scrollreveal.min.js"></script>
    <script src="assets/js/waypoints.min.js"></script>
    <script src="assets/js/jquery.counterup.min.js"></script>
    <script src="assets/js/imgfix.min.js"></script>
    <script src="assets/js/slick.js"></script>
    <script src="assets/js/lightbox.js"></script>
    <script src="assets/js/isotope.js"></script>
    <script src="https://newwebpay.qa.interswitchng.com/inline-checkout.js"></script>
    
    <script src="@Assets["_content/MudBlazor/MudBlazor.min.js"]"></script>

    <!-- Global Init -->
    <script src="assets/js/custom.js"></script>

    @* <script>
    // Add this to your site's JavaScript file or within a <script> tag in your main layout file
    document.addEventListener('DOMContentLoaded', function () {
    var modal = document.getElementById('components-reconnect-modal');
    // Monitor the class changes on the modal
    var observer = new MutationObserver(function (mutations) {
    mutations.forEach(function (mutation) {
    if (mutation.attributeName === 'class') {
    var currentClassState = modal.className;
    // Check if the modal is visible and handle each state
    if (currentClassState.includes('components-reconnect-show')) {
    // Handle attempting to reconnect state
    } else if (currentClassState.includes('components-reconnect-failed')) {
    // Handle reconnection failed state
    } else if (currentClassState.includes('components-reconnect-rejected')) {
    // Handle reconnection rejected state
    }
    }
    });
    });
    // Start observing the modal for attribute changes
    observer.observe(modal, { attributes: true });
    });

    </script> *@

    <script>

        $(function () {
            var selectedClass = "";
            $("p").click(function () {
                selectedClass = $(this).attr("data-rel");
                $("#portfolio").fadeTo(50, 0.1);
                $("#portfolio div").not("." + selectedClass).fadeOut();
                setTimeout(function () {
                    $("." + selectedClass).fadeIn();
                    $("#portfolio").fadeTo(50, 1);
                }, 500);

            });
        });

    </script>
</body>

</html>

@code
{
    [CascadingParameter]
    private HttpContext? HttpContext { get; set; }

    private IComponentRenderMode? RenderModeForPage =>
            HttpContext?.Request.Path.StartsWithSegments("/Account") == true
        ? null
        :
        new InteractiveWebAssemblyRenderMode(
            prerender: true
        );
}
