<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ServerGarbageCollection>false</ServerGarbageCollection>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>Musewears.Server-a67a7cac-36eb-456e-9414-2cc666684605</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="9.0.6" />
    <PackageReference Include="MudBlazor" Version="8.8.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.1" />
    <PackageReference Include="Square" Version="42.0.0" />
    <PackageReference Include="Pixata.Extensions" Version="1.32.0" />
    <PackageReference Include="Blazored.Toast" Version="4.2.1" />
    <PackageReference Include="Refit" Version="8.0.0" />
    <PackageReference Include="Refit.HttpClientFactory" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Basket.API\Basket.API.csproj" />
    <ProjectReference Include="..\Catalog.API\Catalog.API.csproj" />
    <ProjectReference Include="..\Client\Musewears.Client.csproj" />
    <ProjectReference Include="..\Ordering\Ordering.API\Ordering.API.csproj" />
    <ProjectReference Include="..\WebAppComponents\WebAppComponents.csproj" />
<!--    <ProjectReference Include="..\Shared\Musewears.Shared.csproj" />-->
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.ServiceDiscovery.Yarp" Version="9.3.1" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>
  
<!--  <ItemGroup>-->
<!--    <Compile Include="..\Shared\MigrateDbContextExtensions.cs" Link="Extensions\MigrateDbContextExtensions.cs" />-->
<!--    <Compile Include="..\Shared\ActivityExtensions.cs" Link="Extensions\ActivityExtensions.cs" />-->
<!--    <Compile Include="..\Shared\Models\TransactionEventData.cs" Link="Models\TransactionEventData.cs" />-->
<!--  </ItemGroup>-->

  <ItemGroup>
    <None Remove="Services\" />
    <None Remove="Components\Pages\" />
    <None Remove="Layout\" />
    <None Remove="Shared\" />
    <None Remove="Data\Migrations\" />
    <None Remove="Models\" />
    <None Remove="Services\SquareServices\" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Layout\" />
    <Folder Include="Components\Pages\" />
    <Folder Include="Data\Migrations\" />
    <Folder Include="Services\InterswitchServices\" />
    <Folder Include="wwwroot\assets\" />
    <Folder Include="Services\SquareServices\" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Remove="Components/App.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <_ContentIncludedByDefault Remove="Components/App.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Remove="wwwroot\" />
    <Content Remove="wwwroot\assets\" />
  </ItemGroup>
  <ItemGroup>
    <AdditionalFiles Include="Shared\FooterSection.razor" />
    <AdditionalFiles Include="Shared\HeaderSection.razor" />
  </ItemGroup>
</Project>
