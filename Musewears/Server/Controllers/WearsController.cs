using Asp.Versioning;
using Microsoft.AspNetCore.Mvc;
using Musewears.Server.Services;
using Musewears.Shared;

// For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace Musewears.Server.Controllers
{
    [ApiController]
    [ApiVersion("2.0")]
    [Route("api/[controller]")]
    public class WearsController(IWardrobe wardrobe, ILogger<WearsController> logger) : ControllerBase
    {
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Wear>>> GetAsync()
        {
            try
            {
                var wears = await wardrobe.GetWardrobe();
                return Ok(wears);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving wears");
                return StatusCode(500, $"Error retrieving wears: {ex.Message}");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Wear>> GetByIdAsync(string id)
        {
            try
            {
                var wear = await wardrobe.GetWearById(id);

                if (wear == null)
                {
                    return NotFound();
                }

                return Ok(wear);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Error retrieving wear with ID {id}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost]
        public async Task<ActionResult<int>> CreateAsync([FromBody] Wear wear)
        {
            try
            {
                await wardrobe.InsertWear(wear);
                return Ok(wear.Id);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error creating wear");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateAsync(string id, [FromBody] Wear updatedWear)
        {
            try
            {
                await wardrobe.UpdateWear(id, updatedWear);
                return NoContent();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Error updating wear with ID {id}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteAsync(string id)
        {
            try
            {
                await wardrobe.RemoveWear(id);
                return NoContent();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Error deleting wear with ID {id}");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}

