using System.Security.Cryptography;
using System.Text;
using Asp.Versioning;
using Microsoft.AspNetCore.Mvc;
using Musewears.Server.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Musewears.Server.Services;
using Musewears.Shared.Models;
using System.Diagnostics;

namespace Musewears.Server.Controllers
{
    [ApiController]
    [ApiVersion("2.0")]
    [Route("api/[controller]")]
    public class InterswitchWebhookController(
        IWardrobe wardrobe,
        // IOrderService orderService,
        IOrderingIntegrationService orderingIntegrationService,
        IWebhookMetricsService webhookMetricsService,
        ILogger<InterswitchWebhookController> logger)
        : ControllerBase
    {

        [HttpPost("interswitch-notification")]
        public async Task<IActionResult> HandleInterswitchNotification()
        {
            // Handle the webhook request here
            // You can access the request body using HttpContext.Request.Body
            // Process the webhook data and perform the necessary actions

            try
            {
                Console.WriteLine($"Interswitch webhook called");

                using var streamReader = new StreamReader(HttpContext.Request.Body);
                var requestBody = await streamReader.ReadToEndAsync();

                // Read the signature from the X-Interswitch-Signature header
                if (HttpContext.Request.Headers.TryGetValue("X-Interswitch-Signature", out var signatureHeader) && !string.IsNullOrEmpty(signatureHeader))
                {

                    // Initialize your verifier (as per the previous example)
                    var secretKey = Environment.GetEnvironmentVariable("INTERSWITCH_SECRET_KEY"); // You should retrieve this securely as discussed earlier

                    if (secretKey == null)
                    {
                        Console.WriteLine("Interswitch webhook secret not found");
                        return Ok();
                    }

                    // Verify the signature
                    var isVerified = VerifySignature(requestBody, signatureHeader!, secretKey);

                    if (isVerified)
                    {
                        Console.WriteLine($"Interswitch webhook verified successfully");
                        
                        Console.WriteLine($"Interswitch webhook body: {requestBody}");

                        // Process the request body here
                        // You can deserialize it into a model or perform any other necessary operations

                        // Example: Deserialize the request body into a model
                        var webhookData = JsonConvert.DeserializeObject<JObject>(requestBody);

                        if (webhookData == null)
                        {
                            return Ok();
                        }

                        var eventType = webhookData["event"]?.ToString();

                        logger.LogInformation("Processing Interswitch webhook event: {EventType}", eventType);

                        // Process transaction events with improved error handling and metrics
                        switch (eventType)
                        {
                            case "TRANSACTION.CREATED":
                            case "TRANSACTION.UPDATED":
                            case "TRANSACTION.COMPLETED":
                                {
                                    await ProcessTransactionEventAsync(webhookData, eventType, orderingIntegrationService, webhookMetricsService);
                                    break;
                                }
                            case "INVOICE.TRANSACTION_SUCCESSFUL":
                                // var invoiceData = JsonConvert.DeserializeObject<InvoiceEvent>(webhookData.ToString());
                                // Handle invoice event
                                break;

                            case "INVOICE.TRANSACTION_FAILURE":
                                // var invoiceData = JsonConvert.DeserializeObject<InvoiceEvent>(webhookData.ToString());
                                // Handle invoice event
                                break;

                            case "LINK.TRANSACTION_SUCCESSFUL":
                                {
                                    var paymentLinkSuccess = JsonConvert.DeserializeObject<PaymentLinkEvent>(webhookData.ToString());

                                    if (paymentLinkSuccess?.Data?.Reference != null)
                                    {
                                        // Update order status to completed for successful payment link transactions
                                        await orderingIntegrationService.UpdateOrderStatusFromPaymentAsync(
                                            paymentLinkSuccess.Data.Reference, PaymentStatus.completed);

                                        // Associate buyer if we have customer information
                                        if (!string.IsNullOrEmpty(paymentLinkSuccess.Data.CustomerEmail) &&
                                            !string.IsNullOrEmpty(paymentLinkSuccess.Data.CustomerName))
                                        {
                                            await orderingIntegrationService.AssociateBuyerWithOrderAsync(
                                                paymentLinkSuccess.Data.CustomerEmail,
                                                paymentLinkSuccess.Data.CustomerName,
                                                paymentLinkSuccess.Data.Reference,
                                                "");
                                        }

                                        Console.WriteLine($"Interswitch payment link successful: {paymentLinkSuccess.Data.Reference}");
                                    }
                                    break;
                                }

                            case "LINK.TRANSACTION_FAILURE":
                                {
                                    var paymentLinkFailure = JsonConvert.DeserializeObject<PaymentLinkEvent>(webhookData.ToString());

                                    if (paymentLinkFailure?.Data?.Reference != null)
                                    {
                                        // Update order status to failed for failed payment link transactions
                                        await orderingIntegrationService.UpdateOrderStatusFromPaymentAsync(
                                            paymentLinkFailure.Data.Reference, PaymentStatus.failed);

                                        Console.WriteLine($"Interswitch payment link failed: {paymentLinkFailure.Data.Reference}");
                                    }
                                    break;
                                }


                            case "SUBSCRIPTION.CREATED":
                                // var data = JsonConvert.DeserializeObject<SubscriptionEvent>(webhookData.ToString());
                                // Handle payment link event
                                break;

                            case "SUBSCRIPTION.TRANSACTION_SUCCESSFUL":
                                // var data = JsonConvert.DeserializeObject<SubscriptionEvent>(webhookData.ToString());
                                // Handle payment link event
                                break;


                            case "SUBSCRIPTION.SUBSCRIPTION.TRANSACTION_FAILURE":
                                var data = JsonConvert.DeserializeObject<SubscriptionEvent>(webhookData.ToString());
                                // Handle payment link event
                                break;

                            case "SUBSCRIPTION.CANCELLED":
                                // var data = JsonConvert.DeserializeObject<SubscriptionEvent>(webhookData.ToString());
                                // Handle payment link event
                                break;

                            case "PAYOUT.SUCCESS":
                                // var data = JsonConvert.DeserializeObject<PayoutEvent>(webhookData.ToString());
                                // Handle payment link event
                                break;

                            case "PAYOUT.FAILURE":
                                // var data = JsonConvert.DeserializeObject<PayoutEvent>(webhookData.ToString());
                                // Handle payment link event
                                break;
                            
                            case "TEST.STOP":
                                Console.WriteLine($"Test event triggered successfully");
                                break;
                        }

                        return Ok();

                    }
                    else
                    {

                        Console.WriteLine($"Interswitch webhook Unauthorized");
                        // The signature is invalid; respond with an error
                        return Unauthorized();
                    }
                }
                else
                {
                    Console.WriteLine($"Interswitch webhook Missing X-Interswitch-Signature header.");

                    // If the header is not present, respond with an error
                    return BadRequest("Missing X-Interswitch-Signature header.");
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
            }

            return Ok();
        }

        public bool VerifySignature(string jsonPayload, string receivedSignature, string secretKey)
        {
            using var hmac = new HMACSHA512(Encoding.UTF8.GetBytes(secretKey));
            // Compute the hash of the received payload using HMACSHA512
            var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(jsonPayload));

            // Convert the computed hash to a hex-encoded string
            var computedSignature = Convert.ToHexStringLower(hash);

            // Compare the computed hash with the received X-Interswitch-Signature header value
            return computedSignature.Equals(receivedSignature.ToLowerInvariant());
        }

        /// <summary>
        /// Processes transaction events with improved error handling and metrics
        /// </summary>
        private async Task ProcessTransactionEventAsync(JObject webhookData, string eventType,
            IOrderingIntegrationService orderingIntegrationService, IWebhookMetricsService webhookMetricsService)
        {
            var stopwatch = Stopwatch.StartNew();
            var transactionData = JsonConvert.DeserializeObject<TransactionEvent>(webhookData.ToString());

            if (transactionData?.Data?.PaymentReference == null)
            {
                logger.LogWarning("Invalid transaction data received for event type {EventType}", eventType);
                return;
            }

            var paymentId = transactionData.Data.PaymentId;

            try
            {
                // Set the UpdatedAt timestamp from the webhook timestamp
                transactionData.Data.UpdatedAt = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)
                    .AddMilliseconds(transactionData.Timestamp);

                bool success = eventType switch
                {
                    "TRANSACTION.CREATED" => await orderingIntegrationService.ProcessTransactionCreated(transactionData),
                    "TRANSACTION.UPDATED" => await orderingIntegrationService.ProcessTransactionUpdated(transactionData),
                    "TRANSACTION.COMPLETED" => await orderingIntegrationService.ProcessTransactionCompleted(transactionData),
                    _ => false
                };

                stopwatch.Stop();

                if (success)
                {
                    webhookMetricsService.RecordWebhookProcessed(eventType, paymentId, stopwatch.ElapsedMilliseconds);
                    logger.LogInformation("Successfully processed {EventType} for payment ID {PaymentId} in {ElapsedMs}ms",
                        eventType, paymentId, stopwatch.ElapsedMilliseconds);
                }
                else
                {
                    webhookMetricsService.RecordWebhookError(eventType, paymentId, stopwatch.ElapsedMilliseconds, "ProcessingFailed");
                    logger.LogWarning("Failed to process {EventType} for payment ID {PaymentId}", eventType, paymentId);
                }

                Console.WriteLine($"Interswitch event: {eventType} | Status: {success} | Payment Reference: {transactionData.Data.PaymentReference}");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                webhookMetricsService.RecordWebhookError(eventType, paymentId, stopwatch.ElapsedMilliseconds, ex.GetType().Name);
                logger.LogError(ex, "Error processing {EventType} for payment ID {PaymentId}", eventType, paymentId);
                throw;
            }
        }
    }
}