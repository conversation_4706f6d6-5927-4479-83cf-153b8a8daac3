using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Asp.Versioning;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using Musewears.Server.Models;
using static System.Text.Encoding;

namespace Musewears.Server.Controllers;

[ApiController]
[ApiVersion("2.0")]
[Route("api/admin/auth")]
public class AdminAuthController(
    UserManager<ApplicationUser> userManager,
    SignInManager<ApplicationUser> signInManager,
    IConfiguration configuration,
    ILogger<AdminAuthController> logger) : ControllerBase
{
    [HttpPost("login")]
    public async Task<IActionResult> Login([FromBody] AdminLoginRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.Email) || string.IsNullOrEmpty(request.Password))
            {
                return BadRequest(new { Success = false, Message = "Email and password are required" });
            }

            var user = await userManager.FindByEmailAsync(request.Email);
            if (user == null)
            {
                return Unauthorized(new { Success = false, Message = "Invalid credentials" });
            }

            var result = await signInManager.CheckPasswordSignInAsync(user, request.Password, false);
            if (!result.Succeeded)
            {
                return Unauthorized(new { Success = false, Message = "Invalid credentials" });
            }

            // Check if user has admin role
            var roles = await userManager.GetRolesAsync(user);
            if (!roles.Contains("Admin") && !roles.Contains("Manager"))
            {
                return StatusCode(403, new { Success = false, Message = "Access denied. Admin privileges required." });
            }

            // Generate JWT token
            var token = await GenerateJwtToken(user, roles);



            logger.LogInformation("Admin user {Email} logged in successfully", request.Email);

            return Ok(new
            {
                Success = true,
                Token = token,
                Message = "Login successful",
                User = new
                {
                    Id = user.Id,
                    Email = user.Email,
                    UserName = user.UserName,
                    Roles = roles
                }
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during admin login for {Email}", request.Email);
            return StatusCode(500, new { Success = false, Message = "An error occurred during login" });
        }
    }



    [HttpPost("refresh")]
    public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenRequest request)
    {
        try
        {
            var handler = new JwtSecurityTokenHandler();
            var jsonToken = handler.ReadJwtToken(request.Token);
            
            var userIdClaim = jsonToken.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
            if (userIdClaim == null)
            {
                return Unauthorized(new { Success = false, Message = "Invalid token" });
            }

            var user = await userManager.FindByIdAsync(userIdClaim.Value);
            if (user == null)
            {
                return Unauthorized(new { Success = false, Message = "User not found" });
            }

            var roles = await userManager.GetRolesAsync(user);
            if (!roles.Contains("Admin") && !roles.Contains("Manager"))
            {
                return StatusCode(403, new { Success = false, Message = "Access denied" });
            }

            var newToken = await GenerateJwtToken(user, roles);

            return Ok(new
            {
                Success = true,
                Token = newToken,
                Message = "Token refreshed successfully"
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error refreshing token");
            return StatusCode(500, new { Success = false, Message = "An error occurred while refreshing token" });
        }
    }

    private async Task<string> GenerateJwtToken(ApplicationUser user, IList<string> roles)
    {
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, user.Id),
            new(ClaimTypes.Name, user.UserName ?? user.Email!),
            new(ClaimTypes.Email, user.Email!),
            new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64)
        };

        // Add role claims
        claims.AddRange(roles.Select(role => new Claim(ClaimTypes.Role, role)));

        // Add user claims
        var userClaims = await userManager.GetClaimsAsync(user);
        claims.AddRange(userClaims);

        var key = new SymmetricSecurityKey("musewears-super-secret-key-for-development-only-2024"u8.ToArray());
        var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var token = new JwtSecurityToken(
            issuer: "https://localhost:7135",
            audience: "musewears-admin",
            claims: claims,
            expires: DateTime.UtcNow.AddHours(24),
            signingCredentials: creds
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }
}

public class AdminLoginRequest
{
    public string Email { get; set; } = "";
    public string Password { get; set; } = "";
    public bool RememberMe { get; set; }
}

public class RefreshTokenRequest
{
    public string Token { get; set; } = "";
}
