using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Asp.Versioning;
using DefaultNamespace;
using Microsoft.AspNetCore.Authentication;
using Musewears.Server.Models;

namespace Musewears.Server.Controllers;

[ApiController]
[ApiVersion("2.0")]
[Route("api/[controller]")]
public class AccountController(
    SignInManager<ApplicationUser> signInManager,
    UserManager<ApplicationUser> userManager,
    ILogger<AccountController> logger)
    : ControllerBase
{
    [Authorize]
    // [ValidateAntiForgeryToken]
    [HttpPost("logout")]
    public async Task<IActionResult> Logout([FromBody] LogoutRequest? request = null)
    {
        try
        {
            // Sign out the user
            await signInManager.SignOutAsync();
            
            // Clear any additional authentication schemes
            await HttpContext.SignOutAsync();
            
            logger.LogInformation("User logged out successfully");
            
            return Ok(new { 
                success = true, 
                message = "Logged out successfully",
                redirectUrl = request?.ReturnUrl ?? "/"
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during logout");
            return StatusCode(500, new { 
                success = false, 
                message = "An error occurred during logout" 
            });
        }
    }

    [HttpGet("status")]
    public async Task<IActionResult> GetAuthenticationStatus()
    {
        var isAuthenticated = User.Identity?.IsAuthenticated ?? false;

        if (!isAuthenticated) return Ok(new { isAuthenticated = false });
        
        var user = await userManager.GetUserAsync(User);
        return Ok(new
        {
            isAuthenticated = true,
            userId = user?.Id,
            userName = user?.UserName,
            email = user?.Email
        });

    }
}