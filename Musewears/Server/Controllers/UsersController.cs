using System.ComponentModel.DataAnnotations;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Musewears.Server.Models;

namespace Musewears.Server.Controllers;

/// <summary>
/// Admin controller for managing users, roles, and user operations
/// </summary>
[ApiController]
[ApiVersion("2.0")]
[Route("api/admin/[controller]")]
[Authorize(Policy = "AdminOnly")]
public class UsersController(
    UserManager<ApplicationUser> userManager,
    RoleManager<IdentityRole> roleManager,
    ILogger<UsersController> logger) : ControllerBase
{
    /// <summary>
    /// Get paginated list of users with optional search and filtering
    /// </summary>

    [HttpGet]
    public async Task<ActionResult<PaginatedItems<UserViewModel>>> GetUsers(
        [FromQuery] int pageIndex = 0,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? search = null,
        [FromQuery] string? role = null,
        [FromQuery] bool? emailConfirmed = null,
        [FromQuery] bool? lockedOut = null)
    {
        try
        {
            var query = userManager.Users.AsQueryable();

            // Apply search filter
            if (!string.IsNullOrEmpty(search))
            {
                var searchLower = search.ToLower();
                query = query.Where(u =>
                    u.Email!.ToLower().Contains(searchLower) ||
                    u.UserName!.ToLower().Contains(searchLower) ||
                    u.Fullname!.ToLower().Contains(searchLower));
            }

            // Apply email confirmation filter
            if (emailConfirmed.HasValue)
            {
                query = query.Where(u => u.EmailConfirmed == emailConfirmed.Value);
            }

            // Apply lockout filter
            if (lockedOut.HasValue)
            {
                if (lockedOut.Value)
                {
                    query = query.Where(u => u.LockoutEnd.HasValue && u.LockoutEnd > DateTimeOffset.UtcNow);
                }
                else
                {
                    query = query.Where(u => !u.LockoutEnd.HasValue || u.LockoutEnd <= DateTimeOffset.UtcNow);
                }
            }

            var totalItems = await query.LongCountAsync();

            var users = await query
                .OrderBy(u => u.Email)
                .Skip(pageIndex * pageSize)
                .Take(pageSize)
                .Select(u => new UserViewModel
                {
                    Id = u.Id,
                    Email = u.Email,
                    UserName = u.UserName,
                    Fullname = u.Fullname,
                    EmailConfirmed = u.EmailConfirmed,
                    LockoutEnabled = u.LockoutEnabled,
                    LockoutEnd = u.LockoutEnd
                })
                .ToListAsync();

            // Efficiently load roles for all users in one query
            if (!string.IsNullOrEmpty(role))
            {
                var usersInRole = await userManager.GetUsersInRoleAsync(role);
                var userIdsInRole = usersInRole.Select(u => u.Id).ToHashSet();
                users = users.Where(u => userIdsInRole.Contains(u.Id)).ToList();
                totalItems = users.Count;
            }
            else
            {
                // Load roles for displayed users
                foreach (var user in users)
                {
                    var userEntity = await userManager.FindByIdAsync(user.Id);
                    if (userEntity != null)
                    {
                        user.Roles = (await userManager.GetRolesAsync(userEntity)).ToList();
                    }
                }
            }

            return Ok(new PaginatedItems<UserViewModel>(pageIndex, pageSize, totalItems, users));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving users");
            return StatusCode(500, "An error occurred while retrieving users");
        }
    }

    /// <summary>
    /// Get detailed information about a specific user
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<UserDetailViewModel>> GetUser(string id)
    {
        try
        {
            if (string.IsNullOrEmpty(id))
            {
                return BadRequest("User ID is required");
            }

            var user = await userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound($"User with ID {id} not found");
            }

            var roles = await userManager.GetRolesAsync(user);

            return Ok(new UserDetailViewModel
            {
                Id = user.Id,
                Email = user.Email,
                UserName = user.UserName,
                Fullname = user.Fullname,
                PhoneNumber = user.PhoneNumber,
                EmailConfirmed = user.EmailConfirmed,
                PhoneNumberConfirmed = user.PhoneNumberConfirmed,
                TwoFactorEnabled = user.TwoFactorEnabled,
                LockoutEnabled = user.LockoutEnabled,
                LockoutEnd = user.LockoutEnd,
                AccessFailedCount = user.AccessFailedCount,
                Roles = roles.ToList()
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving user {UserId}", id);
            return StatusCode(500, "An error occurred while retrieving the user");
        }
    }

    /// <summary>
    /// Create a new user
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<UserOperationResult>> CreateUser([FromBody] CreateUserRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Check if user already exists
            var existingUser = await userManager.FindByEmailAsync(request.Email);
            if (existingUser != null)
            {
                return BadRequest(new UserOperationResult
                {
                    Success = false,
                    Message = "User with this email already exists",
                    Errors = new List<string> { "Email already in use" }
                });
            }

            // Validate roles exist
            foreach (var roleName in request.Roles)
            {
                if (!await roleManager.RoleExistsAsync(roleName))
                {
                    return BadRequest(new UserOperationResult
                    {
                        Success = false,
                        Message = $"Role '{roleName}' does not exist",
                        Errors = new List<string> { $"Invalid role: {roleName}" }
                    });
                }
            }

            var user = new ApplicationUser
            {
                UserName = request.Email,
                Email = request.Email,
                Fullname = request.Fullname,
                PhoneNumber = request.PhoneNumber,
                EmailConfirmed = request.EmailConfirmed
            };

            var result = await userManager.CreateAsync(user, request.Password);
            if (!result.Succeeded)
            {
                return BadRequest(new UserOperationResult
                {
                    Success = false,
                    Message = "Failed to create user",
                    Errors = result.Errors.Select(e => e.Description).ToList()
                });
            }

            // Assign roles
            if (request.Roles.Any())
            {
                await userManager.AddToRolesAsync(user, request.Roles);
            }

            logger.LogInformation("User {Email} created successfully by admin {AdminId}", request.Email, User.Identity?.Name);

            return Ok(new UserOperationResult
            {
                Success = true,
                Message = "User created successfully"
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error creating user {Email}", request.Email);
            return StatusCode(500, new UserOperationResult
            {
                Success = false,
                Message = "An error occurred while creating the user",
                Errors = new List<string> { ex.Message }
            });
        }
    }

    /// <summary>
    /// Update user information
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<UserOperationResult>> UpdateUser(string id, [FromBody] UpdateUserRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var user = await userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound(new UserOperationResult
                {
                    Success = false,
                    Message = $"User with ID {id} not found"
                });
            }

            // Check if email is changing and if new email is already in use
            if (user.Email != request.Email)
            {
                var existingUser = await userManager.FindByEmailAsync(request.Email);
                if (existingUser != null && existingUser.Id != id)
                {
                    return BadRequest(new UserOperationResult
                    {
                        Success = false,
                        Message = "Email already in use by another user"
                    });
                }
            }

            user.Email = request.Email;
            user.UserName = request.Email;
            user.Fullname = request.Fullname;
            user.PhoneNumber = request.PhoneNumber;
            user.EmailConfirmed = request.EmailConfirmed;
            user.PhoneNumberConfirmed = request.PhoneNumberConfirmed;
            user.TwoFactorEnabled = request.TwoFactorEnabled;
            user.LockoutEnabled = request.LockoutEnabled;

            var result = await userManager.UpdateAsync(user);
            if (!result.Succeeded)
            {
                return BadRequest(new UserOperationResult
                {
                    Success = false,
                    Message = "Failed to update user",
                    Errors = result.Errors.Select(e => e.Description).ToList()
                });
            }

            logger.LogInformation("User {UserId} updated successfully by admin {AdminId}", id, User.Identity?.Name);

            return Ok(new UserOperationResult
            {
                Success = true,
                Message = "User updated successfully"
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating user {UserId}", id);
            return StatusCode(500, new UserOperationResult
            {
                Success = false,
                Message = "An error occurred while updating the user"
            });
        }
    }

    /// <summary>
    /// Assign roles to a user
    /// </summary>
    [HttpPost("{id}/roles")]
    public async Task<ActionResult<UserOperationResult>> AssignRoles(string id, [FromBody] AssignRolesRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var user = await userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound(new UserOperationResult
                {
                    Success = false,
                    Message = $"User with ID {id} not found"
                });
            }

            // Validate all roles exist
            foreach (var roleName in request.Roles)
            {
                if (!await roleManager.RoleExistsAsync(roleName))
                {
                    return BadRequest(new UserOperationResult
                    {
                        Success = false,
                        Message = $"Role '{roleName}' does not exist"
                    });
                }
            }

            // Remove current roles and assign new ones
            var currentRoles = await userManager.GetRolesAsync(user);
            if (currentRoles.Any())
            {
                await userManager.RemoveFromRolesAsync(user, currentRoles);
            }

            if (request.Roles.Any())
            {
                await userManager.AddToRolesAsync(user, request.Roles);
            }

            logger.LogInformation("Roles updated for user {UserId} by admin {AdminId}", id, User.Identity?.Name);

            return Ok(new UserOperationResult
            {
                Success = true,
                Message = "Roles assigned successfully"
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error assigning roles to user {UserId}", id);
            return StatusCode(500, new UserOperationResult
            {
                Success = false,
                Message = "An error occurred while assigning roles"
            });
        }
    }

    /// <summary>
    /// Lock out a user
    /// </summary>
    [HttpPost("{id}/lockout")]
    public async Task<ActionResult<UserOperationResult>> LockoutUser(string id, [FromBody] LockoutUserRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var user = await userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound(new UserOperationResult
                {
                    Success = false,
                    Message = $"User with ID {id} not found"
                });
            }

            var result = await userManager.SetLockoutEndDateAsync(user, request.LockoutEnd);
            if (!result.Succeeded)
            {
                return BadRequest(new UserOperationResult
                {
                    Success = false,
                    Message = "Failed to lock out user",
                    Errors = result.Errors.Select(e => e.Description).ToList()
                });
            }

            logger.LogInformation("User {UserId} locked out until {LockoutEnd} by admin {AdminId}. Reason: {Reason}",
                id, request.LockoutEnd, User.Identity?.Name, request.Reason);

            return Ok(new UserOperationResult
            {
                Success = true,
                Message = "User locked out successfully"
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error locking out user {UserId}", id);
            return StatusCode(500, new UserOperationResult
            {
                Success = false,
                Message = "An error occurred while locking out the user"
            });
        }
    }

    /// <summary>
    /// Unlock a user
    /// </summary>
    [HttpPost("{id}/unlock")]
    public async Task<ActionResult<UserOperationResult>> UnlockUser(string id)
    {
        try
        {
            var user = await userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound(new UserOperationResult
                {
                    Success = false,
                    Message = $"User with ID {id} not found"
                });
            }

            var result = await userManager.SetLockoutEndDateAsync(user, null);
            if (!result.Succeeded)
            {
                return BadRequest(new UserOperationResult
                {
                    Success = false,
                    Message = "Failed to unlock user",
                    Errors = result.Errors.Select(e => e.Description).ToList()
                });
            }

            // Reset access failed count
            await userManager.ResetAccessFailedCountAsync(user);

            logger.LogInformation("User {UserId} unlocked by admin {AdminId}", id, User.Identity?.Name);

            return Ok(new UserOperationResult
            {
                Success = true,
                Message = "User unlocked successfully"
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error unlocking user {UserId}", id);
            return StatusCode(500, new UserOperationResult
            {
                Success = false,
                Message = "An error occurred while unlocking the user"
            });
        }
    }

    /// <summary>
    /// Reset user password
    /// </summary>
    [HttpPost("{id}/reset-password")]
    public async Task<ActionResult<UserOperationResult>> ResetPassword(string id, [FromBody] ResetPasswordRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var user = await userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound(new UserOperationResult
                {
                    Success = false,
                    Message = $"User with ID {id} not found"
                });
            }

            // Generate password reset token and reset password
            var token = await userManager.GeneratePasswordResetTokenAsync(user);
            var result = await userManager.ResetPasswordAsync(user, token, request.NewPassword);

            if (!result.Succeeded)
            {
                return BadRequest(new UserOperationResult
                {
                    Success = false,
                    Message = "Failed to reset password",
                    Errors = result.Errors.Select(e => e.Description).ToList()
                });
            }

            logger.LogInformation("Password reset for user {UserId} by admin {AdminId}", id, User.Identity?.Name);

            return Ok(new UserOperationResult
            {
                Success = true,
                Message = "Password reset successfully"
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error resetting password for user {UserId}", id);
            return StatusCode(500, new UserOperationResult
            {
                Success = false,
                Message = "An error occurred while resetting the password"
            });
        }
    }

    /// <summary>
    /// Delete a user
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult<UserOperationResult>> DeleteUser(string id)
    {
        try
        {
            var user = await userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound(new UserOperationResult
                {
                    Success = false,
                    Message = $"User with ID {id} not found"
                });
            }

            // Prevent deletion of current admin user
            var currentUserId = userManager.GetUserId(User);
            if (id == currentUserId)
            {
                return BadRequest(new UserOperationResult
                {
                    Success = false,
                    Message = "Cannot delete your own account"
                });
            }

            var result = await userManager.DeleteAsync(user);
            if (!result.Succeeded)
            {
                return BadRequest(new UserOperationResult
                {
                    Success = false,
                    Message = "Failed to delete user",
                    Errors = result.Errors.Select(e => e.Description).ToList()
                });
            }

            logger.LogInformation("User {UserId} ({Email}) deleted by admin {AdminId}", id, user.Email, User.Identity?.Name);

            return Ok(new UserOperationResult
            {
                Success = true,
                Message = "User deleted successfully"
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error deleting user {UserId}", id);
            return StatusCode(500, new UserOperationResult
            {
                Success = false,
                Message = "An error occurred while deleting the user"
            });
        }
    }

    /// <summary>
    /// Get all available roles
    /// </summary>
    [HttpGet("roles")]
    public async Task<ActionResult<List<RoleViewModel>>> GetRoles()
    {
        try
        {
            var roles = await roleManager.Roles.ToListAsync();
            var roleViewModels = new List<RoleViewModel>();

            foreach (var role in roles)
            {
                var usersInRole = await userManager.GetUsersInRoleAsync(role.Name!);
                roleViewModels.Add(new RoleViewModel
                {
                    Id = role.Id,
                    Name = role.Name!,
                    UserCount = usersInRole.Count
                });
            }

            return Ok(roleViewModels);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving roles");
            return StatusCode(500, "An error occurred while retrieving roles");
        }
    }

    /// <summary>
    /// Create a new role
    /// </summary>
    [HttpPost("roles")]
    public async Task<ActionResult<UserOperationResult>> CreateRole([FromBody] string roleName)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(roleName))
            {
                return BadRequest(new UserOperationResult
                {
                    Success = false,
                    Message = "Role name is required"
                });
            }

            if (await roleManager.RoleExistsAsync(roleName))
            {
                return BadRequest(new UserOperationResult
                {
                    Success = false,
                    Message = "Role already exists"
                });
            }

            var result = await roleManager.CreateAsync(new IdentityRole(roleName));
            if (!result.Succeeded)
            {
                return BadRequest(new UserOperationResult
                {
                    Success = false,
                    Message = "Failed to create role",
                    Errors = result.Errors.Select(e => e.Description).ToList()
                });
            }

            logger.LogInformation("Role {RoleName} created by admin {AdminId}", roleName, User.Identity?.Name);

            return Ok(new UserOperationResult
            {
                Success = true,
                Message = "Role created successfully"
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error creating role {RoleName}", roleName);
            return StatusCode(500, new UserOperationResult
            {
                Success = false,
                Message = "An error occurred while creating the role"
            });
        }
    }
}
