{"ConnectionStrings": {"DefaultConnection": "Host=dpg-d21gp9vfte5s73fj5ojg-a;Database=musewears_db_4m1i;Username=root;Password=********************************", "CatalogConnection": "postgresql://root:<EMAIL>/catalog_db_3i9i", "OrderingConnection": "postgresql://root:<EMAIL>/musewears_ordering_db_ay3c", "Redis": "red-d1bbcm2dbo4c73cc1iog:6379"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "IdentityServer": {"Key": {"Type": "Development"}}, "Local": {"Authority": "https://login.microsoftonline.com/", "ClientId": "33333333-3333-3333-33333333333333333"}, "DetailedErrors": true, "Interswitch": {"WebhookSecret": "your-production-webhook-secret-key", "MerchantCode": "your-production-merchant-code", "PayItemId": "your-production-pay-item-id", "BaseUrl": "https://webpay.interswitchng.com", "ClientId": "your-production-client-id", "ClientSecret": "your-production-client-secret"}, "Square": {"ClientId": "sq0idp-0KHA7ZaCMQYGpV_HomPedQ", "Environment": "production", "LocationId": "L8ND3PDVXW2C0", "AccessToken": "EAAAlsZ1q6079nLghYiAsXEOivUYEbJyF4nuDENYbdN_uX_7aej_mbdaDdWbzNmU", "AppId": "sq0idp-0KHA7ZaCMQYGpV_HomPedQ"}}