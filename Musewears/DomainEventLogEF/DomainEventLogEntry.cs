using System.ComponentModel.DataAnnotations;

namespace DomainEventLogEF;

public class DomainEventLogEntry
{
    private static readonly JsonSerializerOptions SIndentedOptions = new() { WriteIndented = true };
    private static readonly JsonSerializerOptions SCaseInsensitiveOptions = new() { PropertyNameCaseInsensitive = true };

    private DomainEventLogEntry() { }
    
    public DomainEventLogEntry(INotification domainEvent, Guid transactionId)
    {
        EventId = Guid.NewGuid();
        CreationTime = DateTime.UtcNow;
        EventTypeName = domainEvent.GetType().FullName!;
        Content = JsonSerializer.Serialize(domainEvent, domainEvent.GetType(), SIndentedOptions);
        State = EventStateEnum.NotProcessed;
        TimesSent = 0;
        TransactionId = transactionId;
    }
    
    public Guid EventId { get; private set; }
    
    [Required]
    public string EventTypeName { get; private set; } = string.Empty;
    
    [NotMapped]
    public string EventTypeShortName => EventTypeName.Split('.').Last();
    
    [NotMapped]
    public INotification? DomainEvent { get; private set; }
    
    public EventStateEnum State { get; set; }
    
    public int TimesSent { get; set; }
    
    public DateTime CreationTime { get; private set; }
    
    [Required]
    public string Content { get; private set; } = string.Empty;
    
    public Guid TransactionId { get; private set; }

    public DomainEventLogEntry DeserializeJsonContent(Type type)
    {
        DomainEvent = JsonSerializer.Deserialize(Content, type, SCaseInsensitiveOptions) as INotification;
        return this;
    }
}
