namespace DomainEventLogEF.Services;

public class DomainEventLogService<TContext> : IDomainEventLogService, IDisposable
    where TContext : DbContext
{
    private volatile bool _disposedValue;
    private readonly TContext _context;
    private readonly Type[] _eventTypes;

    public DomainEventLogService(TContext context)
    {
        _context = context;
        _eventTypes = Assembly.Load(Assembly.GetEntryAssembly()!.FullName!)
            .GetTypes()
            .Where(t => t.GetInterfaces().Contains(typeof(INotification)))
            .ToArray();
    }

    public async Task<IEnumerable<DomainEventLogEntry>> RetrieveEventLogsPendingToProcessAsync(Guid transactionId)
    {
        var result = await _context.Set<DomainEventLogEntry>()
            .Where(e => e.TransactionId == transactionId && e.State == EventStateEnum.NotProcessed)
            .ToListAsync();

        if (result.Count != 0)
        {
            return result.OrderBy(o => o.CreationTime)
                .Select(e => e.DeserializeJsonContent(_eventTypes.FirstOrDefault(t => t.Name == e.EventTypeShortName) ?? typeof(object)));
        }

        return [];
    }

    public Task SaveEventAsync(INotification domainEvent, IDbContextTransaction transaction)
    {
        ArgumentNullException.ThrowIfNull(transaction);

        var eventLogEntry = new DomainEventLogEntry(domainEvent, transaction.TransactionId);

        _context.Database.UseTransaction(transaction.GetDbTransaction());
        _context.Set<DomainEventLogEntry>().Add(eventLogEntry);

        return _context.SaveChangesAsync();
    }

    public Task MarkEventAsProcessedAsync(Guid eventId)
    {
        return UpdateEventStatus(eventId, EventStateEnum.Processed);
    }

    public Task MarkEventAsInProgressAsync(Guid eventId)
    {
        return UpdateEventStatus(eventId, EventStateEnum.InProgress);
    }

    public Task MarkEventAsFailedAsync(Guid eventId)
    {
        return UpdateEventStatus(eventId, EventStateEnum.ProcessingFailed);
    }

    private Task UpdateEventStatus(Guid eventId, EventStateEnum status)
    {
        var eventLogEntry = _context.Set<DomainEventLogEntry>().Single(ie => ie.EventId == eventId);
        eventLogEntry.State = status;

        if (status == EventStateEnum.InProgress)
            eventLogEntry.TimesSent++;

        return _context.SaveChangesAsync();
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposedValue)
        {
            if (disposing)
            {
                _context.Dispose();
            }

            _disposedValue = true;
        }
    }

    public void Dispose()
    {
        Dispose(disposing: true);
        GC.SuppressFinalize(this);
    }
}
