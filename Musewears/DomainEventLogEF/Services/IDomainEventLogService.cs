namespace DomainEventLogEF.Services;

public interface IDomainEventLogService
{
    Task<IEnumerable<DomainEventLogEntry>> RetrieveEventLogsPendingToProcessAsync(Guid transactionId);
    Task SaveEventAsync(INotification domainEvent, IDbContextTransaction transaction);
    Task MarkEventAsProcessedAsync(Guid eventId);
    Task MarkEventAsInProgressAsync(Guid eventId);
    Task MarkEventAsFailedAsync(Guid eventId);
}
