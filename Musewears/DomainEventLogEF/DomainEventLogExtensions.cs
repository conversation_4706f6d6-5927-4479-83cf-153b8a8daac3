namespace DomainEventLogEF;

public static class DomainEventLogExtensions
{
    public static void UseDomainEventLogs(this ModelBuilder builder)
    {
        builder.Entity<DomainEventLogEntry>(entityTypeBuilder =>
        {
            entityTypeBuilder.ToTable("DomainEventLog");

            entityTypeBuilder.HasKey(e => e.EventId);
            
            entityTypeBuilder.Property(e => e.EventTypeName)
                .IsRequired()
                .HasMaxLength(500);
                
            entityTypeBuilder.Property(e => e.Content)
                .IsRequired();
                
            entityTypeBuilder.Property(e => e.CreationTime)
                .IsRequired();
                
            entityTypeBuilder.Property(e => e.State)
                .IsRequired();
                
            entityTypeBuilder.Property(e => e.TimesSent)
                .IsRequired();
                
            entityTypeBuilder.Property(e => e.TransactionId)
                .IsRequired();
                
            entityTypeBuilder.HasIndex(e => e.State);
            entityTypeBuilder.HasIndex(e => e.TransactionId);
            entityTypeBuilder.HasIndex(e => e.CreationTime);
        });
    }
}
