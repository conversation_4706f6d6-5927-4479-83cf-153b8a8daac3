@using WebAppComponents.Item
@inject IProductImageUrlProvider ProductImages

<div class="catalog-item">
    <a class="catalog-product" href="@ItemHelper.Url(Item)" data-enhance-nav="false">
        <span class='catalog-product-image'>
            <img alt="@Item.Name" src='@ProductImages.GetProductImageUrl(Item)' />
        </span>
        <span class='catalog-product-content'>
            <span class='name'>@Item.Name</span>
            <span class='price'>$@Item.Price.ToString("0.00")</span>
        </span>
    </a>
</div>

@code {
    [Parameter, EditorRequired]
    public required CatalogItem Item { get; set; }

    [Parameter]
    public bool IsLoggedIn { get; set; }
}
