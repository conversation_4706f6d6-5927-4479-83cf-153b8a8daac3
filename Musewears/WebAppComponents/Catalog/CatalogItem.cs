namespace WebAppComponents.Catalog;

public record CatalogItem(
    int Id,
    string Name,
    string Description,
    decimal Price, // Base price
    string PictureUrl,
    int CatalogBrandId,
    CatalogBrand? CatalogBrand,
    int CatalogTypeId,
    decimal Rating,
    int ReviewCount,
    int AvailableStock,
    CatalogItemType? CatalogType,
    CatalogItemSize[]? Sizes,
    CatalogItemColor[]? Colors,
    CatalogItemVariant[]? Variants,
    CatalogItemImage[]? Images,
    string Currency = "$"
);

public record CatalogResult(int PageIndex, int PageSize, int Count, List<CatalogItem> Data);

public record CatalogBrand(int Id, string Brand);

public record CatalogItemType(int Id, string Type);

public record CatalogItemSize
{
    public int Id { get; init; }
    public string SizeName { get; init; } = "";
    // public decimal PriceModifier { get; init; } = 0; // Added price modifier
}

public record CatalogItemColor
{
    public int Id { get; init; }

    public string ColorName { get; init; } = "";

    public string ColorCode { get; init; } = "";

    // public decimal PriceModifier { get; init; } = 0; // Added price modifier
}

public record CatalogItemImage
{
    public int Id { get; init; }
    public string ImageUrl { get; init; } = ""; // Changed from FileName to ImageUrl
    
    public int CatalogItemId { get; init; }

    public int? CatalogItemColorId { get; init; } // Optional color association
    
    public int? CatalogItemSizeId { get; init; } // Optional size association
    
    public int? CatalogItemVariantId { get; set; }
    public int DisplayOrder { get; init; } = 0;
    public bool IsPrimary { get; init; } = false;
}

public record CatalogItemVariant
{
    public int     Id              { get; init; }
    public string  SKU             { get; init; } = "";
    public decimal Price           { get; init; }
    public int     AvailableStock  { get; init; }
    public int     RestockThreshold{ get; init; }
    public int     MaxStockThreshold{ get; init; }
    public bool    OnReorder       { get; init; }
    public int?     CatalogItemColorId { get; init; }
    public int?     CatalogItemSizeId  { get; init; }
    
    // public CatalogItemImage[] Images { get; init; } = [];
}