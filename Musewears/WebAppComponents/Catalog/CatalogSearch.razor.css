.catalog-search {
  flex-shrink: 0;
  width: 14rem;
}

.catalog-search .catalog-search-header {
  display: flex;
  align-items: center;
  align-self: stretch;
  gap: 0.7rem;
}

.catalog-search .search-badge {
  background: #000;
  color: #FFF;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 0.75rem;
  width: 1.5rem;
  height: 1.5rem;
  line-height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.catalog-search-group h3 {
  color: #000;
  font-size: 1rem;
  font-weight: 600;
  line-height: 150%;
}

.catalog-search-group .catalog-search-group-tags {
  border-top: 1px solid #404040;
  display: flex;
  padding: 0.75rem 0;
  align-items: center;
  align-content: center;
  gap: 0.25rem;
  align-self: stretch;
  flex-wrap: wrap;
  min-width: 12rem;
}

.catalog-search-tag {
  display: flex;
  padding: 0.5rem 0.75rem;
  justify-content: center;
  align-items: center;
  gap: 0.25rem;
  border-radius: 1.25rem;
  color: #404040;
  font-family: 'Open Sans', serif;
  font-size: 1rem;
  font-style: normal;
  font-weight: 400;
  line-height: 150%;
  text-decoration: none;
}

.catalog-search-tag:hover {
    cursor: pointer;
    background: #ddd;
}

.catalog-search-tag.active {
  background: #000;
  color: #FFF;
}

.catalog-search.button {
  width: 100%;
  margin-top: 1rem;
}

@media only screen and (max-width: 480px) { 
  .catalog-search {
      width: 100%;
  }

  .catalog-search .catalog-search-header {
    display: none;
  }

  .catalog-search-group .catalog-search-group-tags {
    justify-content: space-between;
  }
}

@media only screen and (min-width: 481px) and (max-width: 1024px) { 
  .catalog-search {
    width: 100%;
  }

  .catalog-search-types {
    display: flex;
    gap: 3rem;
  }

  .catalog-search-group {
    flex-basis: calc(50% - 3rem);
  }

  .catalog-search-group .catalog-search-group-tags {
    justify-content: space-between;
  }
}