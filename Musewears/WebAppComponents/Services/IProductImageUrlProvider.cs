using WebAppComponents.Catalog;

namespace WebAppComponents.Services;

public interface IProductImageUrlProvider
{
    string GetProductImageUrl(CatalogItem item)
        => GetProductImageUrl(item.Id);
    
    
    string GetProductImageUrl(CatalogItemImage image)
        => GetProductImageUrl(image.CatalogItemId, image.ImageUrl);

    public List<CatalogItemImage> GetProductImages(
        CatalogItem item,
        int? colorId = null,
        int? sizeId = null,
        int? variantId = null);

    string GetProductImageUrl(int productId);
    
    string GetProductImageUrl(int productId, string imageUrl);
}
