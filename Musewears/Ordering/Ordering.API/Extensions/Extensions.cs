using DomainEventBus.Extensions;
using DomainEventLogEF.Services;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Npgsql;
using Ordering.API.Apis;
using Ordering.API.Application.Behaviors;
using Ordering.API.Application.Commands;
using Ordering.API.Application.Queries;
using Ordering.API.Application.Services;
using Ordering.API.Application.Validations;
using Ordering.API.Infrastructure;
using Ordering.Domain.AggregatesModel.BuyerAggregate;
using Ordering.Domain.AggregatesModel.OrderAggregate;
using Ordering.Domain.AggregatesModel.TransactionAggregate;
using Ordering.Infrastructure;
using Ordering.Infrastructure.Repositories;
using Ordering.Infrastructure.Idempotency;

namespace Ordering.API.Extensions;

public static class Extensions
{
    public static void AddOrderingServices(this IHostApplicationBuilder builder)
    {
        var services = builder.Services;
        
        // Add the authentication services to DI
        // Authentication will be configured separately

        // DbContext pooling is now enabled - fixed by ensuring OrderingContext has only one public constructor
        
        if (builder.Environment.IsDevelopment())
        {
            // builder.AddNpgsqlDbContext<OrderingContext>("ordering");
            
            var connectionString = builder.Configuration.GetConnectionString("ordering");
            builder.Services.AddDbContext<OrderingContext>(options =>
            {
                options.UseNpgsql(connectionString);
            });
        }
        else
        {
            var connectionString = builder.Configuration.GetConnectionString("OrderingConnection");
            string npgsqlConn;

            if (connectionString!.StartsWith("postgresql://", StringComparison.OrdinalIgnoreCase))
            {
                var uri = new Uri(connectionString);
                var parts = uri.UserInfo.Split(':', 2);
                var bldr = new NpgsqlConnectionStringBuilder
                {
                    Host                     = uri.Host,
                    // Port                     = uri.Port,
                    Database                 = uri.AbsolutePath.TrimStart('/'),
                    Username                 = parts[0],
                    Password                 = parts.Length > 1 ? parts[1] : "",
                    SslMode                  = SslMode.Require              // or false, per your policy
                };
                npgsqlConn = bldr.ToString();
            }
            else
            {
                npgsqlConn = connectionString;
            }
            
            builder.Services.AddDbContext<OrderingContext>(options =>
            {
                options.UseNpgsql(npgsqlConn);
            });
        }
        
        builder.EnrichNpgsqlDbContext<OrderingContext>();
        
        services.AddMigration<OrderingContext, OrderingContextSeed>();

        // Add the domain event infrastructure
        services.AddTransient<IDomainEventLogService, DomainEventLogService<OrderingContext>>();
        services.AddTransient<IDomainEventService, DomainEventService>();
        services.AddDomainEventBus();

        // Register domain event handlers
        services.AddDomainEventHandlers(typeof(CancelOrderCommand));

        // Add application services
        // services.AddScoped<IIdentityService, IdentityService>();
        // services.AddHttpContextAccessor();

        services.AddHttpContextAccessor();
        services.AddTransient<IIdentityService, IdentityService>();

        // Configure mediatR
        services.AddMediatR(cfg =>
        {
            cfg.RegisterServicesFromAssemblyContaining<CancelOrderCommand>();

            cfg.AddOpenBehavior(typeof(LoggingBehavior<,>));
            cfg.AddOpenBehavior(typeof(ValidatorBehavior<,>));
            cfg.AddOpenBehavior(typeof(TransactionBehavior<,>));
        });

        // Register the command validators for the validator behavior (validators based on FluentValidation library)
        services.AddSingleton<IValidator<CancelOrderCommand>, CancelOrderCommandValidator>();
        services.AddSingleton<IValidator<CreateOrderCommand>, CreateOrderCommandValidator>();
        services.AddSingleton<IValidator<IdentifiedCommand<CreateOrderCommand, bool>>, IdentifiedCommandValidator>();
        services.AddSingleton<IValidator<ShipOrderCommand>, ShipOrderCommandValidator>();

        services.AddScoped<IOrderQueries, OrderQueries>();
        services.AddScoped<IBuyerRepository, BuyerRepository>();
        services.AddScoped<IOrderRepository, OrderRepository>();
        services.AddScoped<ITransactionRepository, TransactionRepository>();
        services.AddScoped<IRequestManager, RequestManager>();

        // Add the new consolidated transaction processing service
        services.AddScoped<ITransactionProcessingService, TransactionProcessingService>();
    }

    // Event bus subscriptions removed - using domain events in monolith architecture
    
    public static void CreateDbIfNotExist(this IHost host)
    {
        using var scope = host.Services.CreateScope();

        var services = scope.ServiceProvider;

        var context = services.GetRequiredService<OrderingContext>();

        try
        {
            // Use migrations instead of EnsureCreated for proper schema management
            context.Database.Migrate();
        }
        catch (Exception exception)
        {
            Console.WriteLine(exception);
        }
    }
    
    public static IApplicationBuilder UseOrderingApi(this WebApplication app)
    {
        ArgumentNullException.ThrowIfNull(app);
        
        var orders = app.NewVersionedApi("Orders");

        orders.MapOrdersApiV1()
            .RequireAuthorization();

        return app;
    }
}