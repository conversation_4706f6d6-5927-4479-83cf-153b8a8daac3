using DomainEventBus.Abstractions;
using Microsoft.Extensions.Logging;
using Ordering.Domain.AggregatesModel.BuyerAggregate;
using Ordering.Domain.AggregatesModel.OrderAggregate;
using Ordering.Domain.Events;
using OrderingApiTrace = Ordering.API.Extensions.OrderingApiTrace;

namespace Ordering.API.Application.DomainEventHandlers;

public class OrderCancelledDomainEventHandler(
    ILogger<OrderCancelledDomainEventHandler> logger)
    : IDomainEventHandler<OrderCancelledDomainEvent>
{
    private readonly ILogger _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    public async Task Handle(OrderCancelledDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        OrderingApiTrace.LogOrderStatusUpdated(_logger, domainEvent.Order.Id, OrderStatus.Cancelled);

        _logger.LogInformation("Order {OrderId} has been cancelled", domainEvent.Order.Id);

        // In a monolith, we can directly handle any business logic here
        // or trigger other domain events if needed
        await Task.CompletedTask;
    }
}
