using Ordering.Domain.AggregatesModel.BuyerAggregate;
using Ordering.Domain.AggregatesModel.OrderAggregate;
using Ordering.Domain.Events;
using OrderingApiTrace = Ordering.API.Extensions.OrderingApiTrace;

namespace Ordering.API.Application.DomainEventHandlers;

public class OrderStatusChangedToStockConfirmedDomainEventHandler(
    ILogger<OrderStatusChangedToStockConfirmedDomainEventHandler> logger)
    : INotificationHandler<OrderStatusChangedToStockConfirmedDomainEvent>
{
    private readonly ILogger _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    public async Task Handle(OrderStatusChangedToStockConfirmedDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        OrderingApiTrace.LogOrderStatusUpdated(_logger, domainEvent.OrderId, OrderStatus.StockConfirmed);

        _logger.LogInformation("Order {OrderId} status changed to StockConfirmed", domainEvent.OrderId);

        // In a monolith, we can directly handle any business logic here
        // or trigger other domain events if needed
        await Task.CompletedTask;
    }
}
