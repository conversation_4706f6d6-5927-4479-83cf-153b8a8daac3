using MediatR;
using Ordering.Domain.AggregatesModel.BuyerAggregate;
using Ordering.Domain.AggregatesModel.OrderAggregate;
using Ordering.Domain.AggregatesModel.TransactionAggregate;
using Ordering.Domain.Events;

namespace Ordering.API.Application.DomainEventHandlers;

/// <summary>
/// Domain event handler for TRANSACTION.COMPLETED events from Interswitch
/// Handles final transaction completion, payment method verification, and order finalization
/// </summary>
public class TransactionCompletedDomainEventHandler(
    IOrderRepository orderRepository,
    IBuyerRepository buyerRepository,
    ITransactionRepository transactionRepository,
    ILogger<TransactionCompletedDomainEventHandler> logger)
    : INotificationHandler<TransactionCompletedDomainEvent>
{
    private readonly IOrderRepository _orderRepository = orderRepository ?? throw new ArgumentNullException(nameof(orderRepository));
    private readonly IBuyerRepository _buyerRepository = buyerRepository ?? throw new ArgumentNullException(nameof(buyerRepository));
    private readonly ITransactionRepository _transactionRepository = transactionRepository ?? throw new ArgumentNullException(nameof(transactionRepository));
    private readonly ILogger<TransactionCompletedDomainEventHandler> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    public async Task Handle(TransactionCompletedDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        var transaction = domainEvent.Transaction;
        
        _logger.LogInformation("Handling TransactionCompleted event for payment ID {PaymentId}, successful: {IsSuccessful}",
            transaction.PaymentId, transaction.IsSuccessful);

        try
        {
            // Process order completion if transaction is associated with an order
            if (transaction.OrderId.HasValue)
            {
                var order = await _orderRepository.GetAsync(transaction.OrderId.Value);
                if (order is not null)
                {
                    await ProcessOrderCompletion(transaction, order);
                }
                else
                {
                    _logger.LogWarning("Order {OrderId} not found for completed transaction {PaymentId}",
                        transaction.OrderId, transaction.PaymentId);
                }
            }
            else
            {
                _logger.LogInformation("Completed transaction {PaymentId} is not associated with any order",
                    transaction.PaymentId);
            }

            // Save changes
            await _transactionRepository.UnitOfWork.SaveEntitiesAsync(cancellationToken);
            
            _logger.LogInformation("Successfully processed TransactionCompleted event for payment ID {PaymentId}",
                transaction.PaymentId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing TransactionCompleted event for payment ID {PaymentId}",
                transaction.PaymentId);
            throw;
        }
    }

    private async Task ProcessOrderCompletion(Transaction transaction, Order order)
    {
        // Update order with final payment status
        var paymentStatus = transaction.GetPaymentStatus();
        
        // Only trigger order payment status update if we have valid customer information
        // If we don't have customer info, skip the buyer creation/verification
        if (!string.IsNullOrEmpty(transaction.MerchantCustomerId) &&
            !string.IsNullOrEmpty(transaction.MerchantCustomerName))
        {
            order.ProcessInterSwitchPayment(
                transaction.PaymentReference,
                transaction.MerchantReference,
                transaction.MerchantCustomerId,
                transaction.MerchantCustomerName,
                paymentStatus,
                transaction.PaymentId,
                transaction.MerchantCustomerId,
                transaction.MerchantCustomerName,
                transaction.ResponseCode);
        }
        else
        {
            _logger.LogInformation("No customer information in webhook for transaction {PaymentId}. Creating buyer from order context.",
                transaction.PaymentId);

            // Create buyer and payment method using order context
            await CreateBuyerFromOrderContextAsync(transaction, order);

            // Still need to trigger payment status change even without customer info
            order.ProcessInterSwitchPayment(
                transaction.PaymentReference,
                transaction.MerchantReference,
                $"order-{order.Id}", // Fallback user ID
                $"Customer for Order {order.Id}", // Fallback user name
                paymentStatus,
                transaction.PaymentId,
                transaction.MerchantCustomerId,
                transaction.MerchantCustomerName,
                transaction.ResponseCode);
        }

        _logger.LogInformation("Updated order {OrderId} with final payment status {PaymentStatus}",
            order.Id, paymentStatus);

        // If payment is successful, verify payment method
        if (transaction.IsSuccessful &&
            !string.IsNullOrEmpty(transaction.MerchantCustomerId) &&
            !string.IsNullOrEmpty(transaction.MerchantCustomerName))
        {
            await VerifyPaymentMethod(transaction, order);
        }
        else if (!transaction.IsSuccessful)
        {
            _logger.LogInformation("Transaction {PaymentId} failed with response: {ResponseDescription}",
                transaction.PaymentId, transaction.ResponseDescription);
        }
    }

    private async Task VerifyPaymentMethod(Transaction transaction, Order order)
    {
        var buyer = await _buyerRepository.FindAsync(transaction.MerchantCustomerId!);
        
        if (buyer != null)
        {
            // Verify the payment method
            var paymentMethod = buyer.VerifyOrAddInterSwitchPaymentMethod(
                alias: $"Interswitch-{transaction.PaymentId}",
                paymentReference: transaction.PaymentReference,
                cardHolderName: transaction.MerchantCustomerName!,
                orderId: order.Id);

            // Set payment method as verified on the order
            order.SetPaymentMethodVerified(buyer.Id, paymentMethod.Id);

            _logger.LogInformation("Verified payment method {PaymentMethodId} for buyer {BuyerId} and order {OrderId}",
                paymentMethod.Id, buyer.Id, order.Id);
        }
        else
        {
            _logger.LogWarning("Buyer {BuyerId} not found for successful transaction {PaymentId}",
                transaction.MerchantCustomerId, transaction.PaymentId);
        }
    }

    private async Task CreateBuyerFromOrderContextAsync(Transaction transaction, Order order)
    {
        // Generate a buyer ID from the order context
        var buyerId = $"order-{order.Id}";
        var buyerName = $"Customer for Order {order.Id}";

        var buyer = await _buyerRepository.FindAsync(buyerId);

        if (buyer is null)
        {
            buyer = new Buyer(buyerId, buyerName);
            _buyerRepository.Add(buyer);

            _logger.LogInformation("Created new buyer {BuyerId} from order context for transaction {PaymentId}",
                buyerId, transaction.PaymentId);
        }

        // Create payment method for this transaction
        var paymentMethod = buyer.VerifyOrAddInterSwitchPaymentMethod(
            alias: $"Interswitch-{transaction.PaymentId}",
            paymentReference: transaction.PaymentReference,
            cardHolderName: buyerName,
            orderId: order.Id);

        _logger.LogInformation("Created/verified payment method {PaymentMethodId} for buyer {BuyerId} from order context",
            paymentMethod.Id, buyer.Id);

        await _orderRepository.UnitOfWork.SaveEntitiesAsync();
    }
}
