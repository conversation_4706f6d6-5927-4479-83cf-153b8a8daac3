using Ordering.API.Application.Commands;
using Ordering.API.Extensions;
using Ordering.Domain.Events;

namespace Ordering.API.Application.DomainEventHandlers;

public class GracePeriodConfirmedDomainEventHandler(
    IMediator mediator,
    ILogger<GracePeriodConfirmedDomainEventHandler> logger) : INotificationHandler<GracePeriodConfirmedDomainEvent>
{
    /// <summary>
    /// Event handler which confirms that the grace period
    /// has been completed and order will not initially be cancelled.
    /// Therefore, the order process continues for validation.
    /// </summary>
    /// <param name="domainEvent">
    /// </param>
    /// <returns></returns>
    public async Task Handle(GracePeriodConfirmedDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        logger.LogInformation("Handling domain event: {DomainEvent}", domainEvent);

        var command = new SetAwaitingValidationOrderStatusCommand(domainEvent.OrderId);

        logger.LogInformation(
            "Sending command: {CommandName} - {IdProperty}: {CommandId} ({@Command})",
            command.GetGenericTypeName(),
            nameof(command.OrderNumber),
            command.OrderNumber,
            command);

        await mediator.Send(command, cancellationToken);
    }
}
