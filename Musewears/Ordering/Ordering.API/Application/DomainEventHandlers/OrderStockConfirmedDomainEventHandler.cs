using Ordering.API.Application.Commands;
using Ordering.API.Extensions;
using Ordering.Domain.Events;

namespace Ordering.API.Application.DomainEventHandlers;

public class OrderStockConfirmedDomainEventHandler(
    IMediator mediator,
    ILogger<OrderStockConfirmedDomainEventHandler> logger) :
    INotificationHandler<OrderStockConfirmedDomainEvent>
{
    public async Task Handle(OrderStockConfirmedDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        logger.LogInformation("Handling domain event: {DomainEvent}", domainEvent);

        var command = new SetStockConfirmedOrderStatusCommand(domainEvent.OrderId);

        logger.LogInformation(
            "Sending command: {CommandName} - {IdProperty}: {CommandId} ({@Command})",
            command.GetGenericTypeName(),
            nameof(command.OrderNumber),
            command.OrderNumber,
            command);

        await mediator.Send(command, cancellationToken);
    }
}
