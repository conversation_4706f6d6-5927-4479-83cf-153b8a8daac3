using Ordering.Domain.AggregatesModel.BuyerAggregate;
using Ordering.Domain.AggregatesModel.OrderAggregate;
using Ordering.Domain.Events;
using OrderingApiTrace = Ordering.API.Extensions.OrderingApiTrace;

namespace Ordering.API.Application.DomainEventHandlers;

public class OrderShippedDomainEventHandler(
    ILogger<OrderShippedDomainEventHandler> logger)
    : INotificationHandler<OrderShippedDomainEvent>
{
    private readonly ILogger _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    public async Task Handle(OrderShippedDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        OrderingApiTrace.LogOrderStatusUpdated(_logger, domainEvent.Order.Id, OrderStatus.Shipped);

        _logger.LogInformation("Order {OrderId} has been shipped", domainEvent.Order.Id);

        // In a monolith, we can directly handle any business logic here
        // or trigger other domain events if needed
        await Task.CompletedTask;
    }
}
