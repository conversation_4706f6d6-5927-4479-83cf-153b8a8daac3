using Ordering.API.Application.Commands;
using Ordering.API.Extensions;
using Ordering.Domain.Events;

namespace Ordering.API.Application.DomainEventHandlers;

public class OrderPaymentFailedDomainEventHandler(
    IMediator mediator,
    ILogger<OrderPaymentFailedDomainEventHandler> logger) :
    INotificationHandler<OrderPaymentFailedDomainEvent>
{
    public async Task Handle(OrderPaymentFailedDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        logger.LogInformation("Handling domain event: {DomainEvent}", domainEvent);

        var command = new CancelOrderCommand(domainEvent.OrderId);

        logger.LogInformation(
            "Sending command: {CommandName} - {IdProperty}: {CommandId} ({@Command})",
            command.GetGenericTypeName(),
            nameof(command.OrderNumber),
            command.OrderNumber,
            command);

        await mediator.Send(command, cancellationToken);
    }
}
