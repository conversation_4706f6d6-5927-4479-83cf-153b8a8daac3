using Ordering.Domain.AggregatesModel.OrderAggregate;
using Ordering.Domain.Events;
using OrderingApiTrace = Ordering.API.Extensions.OrderingApiTrace;

namespace Ordering.API.Application.DomainEventHandlers;

public class OrderStatusChangedToPaidDomainEventHandler(
    ILogger<OrderStatusChangedToPaidDomainEventHandler> logger)
    : INotificationHandler<OrderStatusChangedToPaidDomainEvent>
{
    private readonly ILogger _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    public async Task Handle(OrderStatusChangedToPaidDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        OrderingApiTrace.LogOrderStatusUpdated(_logger, domainEvent.OrderId, OrderStatus.Paid);

        _logger.LogInformation("Order {OrderId} status changed to Paid with {ItemCount} items",
            domainEvent.OrderId,
            domainEvent.OrderItems.Count());

        // In a monolith, we can directly handle any business logic here
        // For example: update inventory, send confirmation emails, trigger shipping, etc.

        // Log the successful payment for audit purposes
        _logger.LogInformation("Payment successfully processed for order {OrderId}. Order is now ready for fulfillment.",
            domainEvent.OrderId);

        await Task.CompletedTask;
    }
}
