using Ordering.API.Application.Commands;
using Ordering.Domain.AggregatesModel.OrderAggregate;
using Ordering.Domain.Events;

namespace Ordering.API.Application.DomainEventHandlers;

public class OrderPaymentStatusChangedDomainEventHandler(
    IMediator mediator,
    IOrderRepository orderRepository,
    ILogger<OrderPaymentStatusChangedDomainEventHandler> logger)
    : INotificationHandler<OrderPaymentStatusChangedDomainEvent>
{
    private readonly IMediator _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
    private readonly IOrderRepository _orderRepository = orderRepository ?? throw new ArgumentNullException(nameof(orderRepository));
    private readonly ILogger _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    public async Task Handle(OrderPaymentStatusChangedDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Handling payment status change for payment reference: {PaymentReference} with status: {PaymentStatus}", 
            domainEvent.PaymentReference, domainEvent.PaymentStatus);

        // Find the order by payment reference
        var order = await _orderRepository.GetByPaymentReferenceAsync(domainEvent.PaymentReference);
        if (order == null)
        {
            _logger.LogWarning("Order not found for payment reference: {PaymentReference}", domainEvent.PaymentReference);
            return;
        }

        // Map payment status to appropriate order command
        var command = MapPaymentStatusToCommand(domainEvent.PaymentStatus, order.Id, domainEvent.ResponseCode);
        
        if (command != null)
        {
            _logger.LogInformation("Sending command: {CommandName} for order {OrderId}", 
                command.GetType().Name, order.Id);
            
            await _mediator.Send(command, cancellationToken);
        }
        else
        {
            _logger.LogInformation("No command needed for payment status: {PaymentStatus}", domainEvent.PaymentStatus);
        }
    }

    private IRequest<bool>? MapPaymentStatusToCommand(string paymentStatus, int orderId, string? responseCode)
    {
        return paymentStatus.ToLowerInvariant() switch
        {
            "completed" when IsSuccessfulResponse(responseCode) => new SetPaidOrderStatusCommand(orderId),
            "failed" => new CancelOrderCommand(orderId),
            "pending" => new SetAwaitingValidationOrderStatusCommand(orderId),
            _ => null
        };
    }

    private static bool IsSuccessfulResponse(string? responseCode)
    {
        // Interswitch success response codes
        return responseCode switch
        {
            "00" => true,  // Approved
            "10" => true,  // Approved (partial)
            "11" => true,  // Approved (VIP)
            _ => false
        };
    }
}
