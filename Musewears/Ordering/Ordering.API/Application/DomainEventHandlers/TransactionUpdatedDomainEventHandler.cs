using MediatR;
using Ordering.Domain.AggregatesModel.BuyerAggregate;
using Ordering.Domain.AggregatesModel.OrderAggregate;
using Ordering.Domain.AggregatesModel.TransactionAggregate;
using Ordering.Domain.Events;

namespace Ordering.API.Application.DomainEventHandlers;

/// <summary>
/// Domain event handler for TRANSACTION.UPDATED events from Interswitch
/// Handles transaction status updates and order status synchronization
/// </summary>
public class TransactionUpdatedDomainEventHandler(
    IOrderRepository orderRepository,
    ITransactionRepository transactionRepository,
    IBuyerRepository buyerRepository,
    ILogger<TransactionUpdatedDomainEventHandler> logger)
    : INotificationHandler<TransactionUpdatedDomainEvent>
{
    private readonly IOrderRepository _orderRepository = orderRepository ?? throw new ArgumentNullException(nameof(orderRepository));
    private readonly ITransactionRepository _transactionRepository = transactionRepository ?? throw new ArgumentNullException(nameof(transactionRepository));
    private readonly IBuyerRepository _buyerRepository = buyerRepository ?? throw new ArgumentNullException(nameof(buyerRepository));
    private readonly ILogger<TransactionUpdatedDomainEventHandler> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    public async Task Handle(TransactionUpdatedDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        var transaction = domainEvent.Transaction;
        
        _logger.LogInformation("Handling TransactionUpdated event for payment ID {PaymentId}, new status: {Status}",
            transaction.PaymentId, transaction.Status);

        try
        {
            // Update associated order status if transaction is associated with an order
            if (transaction.OrderId.HasValue)
            {
                var order = await _orderRepository.GetAsync(transaction.OrderId.Value);
                if (order is not null)
                {
                    // Update order payment status based on transaction status
                    var paymentStatus = transaction.GetPaymentStatus();
                    
                    // Only trigger order payment status update if we have valid customer information
                    // If we don't have customer info, skip the buyer creation/verification
                    if (!string.IsNullOrEmpty(transaction.MerchantCustomerId) &&
                        !string.IsNullOrEmpty(transaction.MerchantCustomerName))
                    {
                        order.ProcessInterSwitchPayment(
                            transaction.PaymentReference,
                            transaction.MerchantReference,
                            transaction.MerchantCustomerId,
                            transaction.MerchantCustomerName,
                            paymentStatus,
                            transaction.PaymentId,
                            transaction.MerchantCustomerId,
                            transaction.MerchantCustomerName,
                            transaction.ResponseCode);
                    }
                    else
                    {
                        _logger.LogInformation("No customer information in webhook for transaction {PaymentId}. Creating buyer from order context.",
                            transaction.PaymentId);

                        // Create buyer and payment method using order context
                        await CreateBuyerFromOrderContextAsync(transaction, order);

                        // Still need to trigger payment status change even without customer info
                        order.ProcessInterSwitchPayment(
                            transaction.PaymentReference,
                            transaction.MerchantReference,
                            $"order-{order.Id}", // Fallback user ID
                            $"Customer for Order {order.Id}", // Fallback user name
                            paymentStatus,
                            transaction.PaymentId,
                            transaction.MerchantCustomerId,
                            transaction.MerchantCustomerName,
                            transaction.ResponseCode);
                    }

                    _logger.LogInformation("Updated order {OrderId} payment status to {PaymentStatus} for transaction {PaymentId}",
                        order.Id, paymentStatus, transaction.PaymentId);
                }
                else
                {
                    _logger.LogWarning("Order {OrderId} not found for transaction {PaymentId}",
                        transaction.OrderId, transaction.PaymentId);
                }
            }
            else
            {
                _logger.LogInformation("Transaction {PaymentId} is not associated with any order",
                    transaction.PaymentId);
            }

            // Save changes
            await _transactionRepository.UnitOfWork.SaveEntitiesAsync(cancellationToken);
            
            _logger.LogInformation("Successfully processed TransactionUpdated event for payment ID {PaymentId}",
                transaction.PaymentId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing TransactionUpdated event for payment ID {PaymentId}",
                transaction.PaymentId);
            throw;
        }
    }

    private async Task CreateBuyerFromOrderContextAsync(Transaction transaction, Order order)
    {
        // Generate a buyer ID from the order context
        // var buyerId = $"order-{order.Id}";
        // var buyerName = $"Customer for Order {order.Id}";
        var buyerId = transaction.MerchantCustomerId;
        var buyerName = transaction.MerchantCustomerName ?? $"Customer for Order {order.Id}";

        if (buyerId is null)
        {
            return;
        }

        var buyer = await _buyerRepository.FindAsync(buyerId);

        if (buyer is null)
        {
            buyer = new Buyer(buyerId, buyerName);
            _buyerRepository.Add(buyer);

            _logger.LogInformation("Created new buyer {BuyerId} from order context for transaction {PaymentId}",
                buyerId, transaction.PaymentId);
        }

        // Create payment method for this transaction
        var paymentMethod = buyer.VerifyOrAddInterSwitchPaymentMethod(
            alias: $"Interswitch-{transaction.PaymentId}",
            paymentReference: transaction.PaymentReference,
            cardHolderName: buyerName,
            orderId: order.Id);

        _logger.LogInformation("Created/verified payment method {PaymentMethodId} for buyer {BuyerId} from order context",
            paymentMethod.Id, buyer.Id);

        await _orderRepository.UnitOfWork.SaveEntitiesAsync();
    }
}
