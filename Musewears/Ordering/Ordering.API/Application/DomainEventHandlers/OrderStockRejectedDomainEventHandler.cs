using Ordering.API.Application.Commands;
using Ordering.API.Extensions;
using Ordering.Domain.Events;

namespace Ordering.API.Application.DomainEventHandlers;

public class OrderStockRejectedDomainEventHandler(
    IMediator mediator,
    ILogger<OrderStockRejectedDomainEventHandler> logger) : INotificationHandler<OrderStockRejectedDomainEvent>
{
    public async Task Handle(OrderStockRejectedDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        logger.LogInformation("Handling domain event: {DomainEvent}", domainEvent);

        var orderStockRejectedItems = domainEvent.OrderStockItems
            .FindAll(c => !c.HasStock)
            .Select(c => c.ProductId)
            .ToList();

        var command = new SetStockRejectedOrderStatusCommand(domainEvent.OrderId, orderStockRejectedItems);

        logger.LogInformation(
            "Sending command: {CommandName} - {IdProperty}: {CommandId} ({@Command})",
            command.GetGenericTypeName(),
            nameof(command.OrderNumber),
            command.OrderNumber,
            command);

        await mediator.Send(command, cancellationToken);
    }
}
