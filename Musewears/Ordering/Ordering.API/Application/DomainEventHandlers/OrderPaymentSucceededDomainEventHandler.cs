using Ordering.API.Application.Commands;
using Ordering.API.Extensions;
using Ordering.Domain.Events;

namespace Ordering.API.Application.DomainEventHandlers;

public class OrderPaymentSucceededDomainEventHandler(
    IMediator mediator,
    ILogger<OrderPaymentSucceededDomainEventHandler> logger) :
    INotificationHandler<OrderPaymentSucceededDomainEvent>
{
    public async Task Handle(OrderPaymentSucceededDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        logger.LogInformation("Handling domain event: {DomainEvent}", domainEvent);

        var command = new SetPaidOrderStatusCommand(domainEvent.OrderId);

        logger.LogInformation(
            "Sending command: {CommandName} - {IdProperty}: {CommandId} ({@Command})",
            command.GetGenericTypeName(),
            nameof(command.OrderNumber),
            command.OrderNumber,
            command);

        await mediator.Send(command, cancellationToken);
    }
}
