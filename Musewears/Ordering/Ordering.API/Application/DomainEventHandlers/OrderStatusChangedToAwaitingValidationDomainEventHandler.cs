using Ordering.Domain.AggregatesModel.OrderAggregate;
using Ordering.Domain.Events;
using OrderingApiTrace = Ordering.API.Extensions.OrderingApiTrace;

namespace Ordering.API.Application.DomainEventHandlers;

public class OrderStatusChangedToAwaitingValidationDomainEventHandler(
    ILogger<OrderStatusChangedToAwaitingValidationDomainEventHandler> logger)
    : INotificationHandler<OrderStatusChangedToAwaitingValidationDomainEvent>
{
    private readonly ILogger _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    public async Task Handle(OrderStatusChangedToAwaitingValidationDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        OrderingApiTrace.LogOrderStatusUpdated(_logger, domainEvent.OrderId, OrderStatus.AwaitingValidation);

        _logger.LogInformation("Order {OrderId} status changed to AwaitingValidation with {ItemCount} items",
            domainEvent.OrderId,
            domainEvent.OrderItems.Count());

        // In a monolith, we can directly handle any business logic here
        // or trigger other domain events if needed
        await Task.CompletedTask;
    }
}
