using Musewears.Shared.Models;

namespace Ordering.API.Application.Services;

/// <summary>
/// Interface for consolidated transaction processing service
/// </summary>
public interface ITransactionProcessingService
{
    /// <summary>
    /// Processes a transaction webhook event with consolidated logic to prevent duplicates
    /// </summary>
    /// <param name="transactionEvent">The transaction event from Interswitch webhook</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if processing was successful</returns>
    Task<bool> ProcessTransactionWebhookAsync(TransactionEvent transactionEvent, CancellationToken cancellationToken = default);
}
