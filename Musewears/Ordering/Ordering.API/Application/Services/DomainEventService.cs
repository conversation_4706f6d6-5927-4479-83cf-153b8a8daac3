using DomainEventLogEF.Services;
using Ordering.Infrastructure;

namespace Ordering.API.Application.Services;

public class DomainEventService : IDomainEventService
{
    private readonly IDomainEventLogService _eventLogService;
    private readonly OrderingContext _orderingContext;
    private readonly IMediator _mediator;
    private readonly ILogger<DomainEventService> _logger;

    public DomainEventService(
        IDomainEventLogService eventLogService,
        OrderingContext orderingContext,
        IMediator mediator,
        ILogger<DomainEventService> logger)
    {
        _eventLogService = eventLogService;
        _orderingContext = orderingContext;
        _mediator = mediator;
        _logger = logger;
    }

    public async Task AddAndSaveEventAsync(INotification domainEvent)
    {
        _logger.LogInformation("Enqueuing domain event {DomainEvent}", domainEvent.GetType().Name);
        
        using var transaction = await _orderingContext.Database.BeginTransactionAsync();
        await _eventLogService.SaveEventAsync(domainEvent, transaction);
        await transaction.CommitAsync();
    }

    public async Task ProcessPendingEventsAsync(Guid transactionId)
    {
        var pendingLogEvents = await _eventLogService.RetrieveEventLogsPendingToProcessAsync(transactionId);

        foreach (var logEvent in pendingLogEvents)
        {
            _logger.LogInformation("Processing domain event {DomainEvent}", logEvent.EventTypeName);

            try
            {
                await _eventLogService.MarkEventAsInProgressAsync(logEvent.EventId);

                if (logEvent.DomainEvent != null)
                {
                    await _mediator.Publish(logEvent.DomainEvent);
                }

                await _eventLogService.MarkEventAsProcessedAsync(logEvent.EventId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing domain event {DomainEvent}", logEvent.EventTypeName);
                await _eventLogService.MarkEventAsFailedAsync(logEvent.EventId);
            }
        }
    }
}
