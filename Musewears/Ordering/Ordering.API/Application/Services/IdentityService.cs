using System.Security.Claims;
using Microsoft.AspNetCore.Http;

namespace Ordering.API.Application.Services;

public class IdentityService(IHttpContextAccessor context) : IIdentityService
{
    private readonly IHttpContextAccessor _context = context ?? throw new ArgumentNullException(nameof(context));

    public string GetUserIdentity()
    {
        return _context.HttpContext?.User?.FindFirst("sub")?.Value ?? string.Empty;
    }

    public string GetUserName()
    {
        return _context.HttpContext?.User?.Identity?.Name ?? string.Empty;
    }
}
