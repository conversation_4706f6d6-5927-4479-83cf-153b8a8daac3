using Musewears.Shared.Models;
using Ordering.Domain.AggregatesModel.BuyerAggregate;
using Ordering.Domain.AggregatesModel.OrderAggregate;
using Ordering.Domain.AggregatesModel.TransactionAggregate;

namespace Ordering.API.Application.Services;

/// <summary>
/// Consolidated service for processing Interswitch transaction webhooks
/// Handles the complete transaction lifecycle to prevent duplicate processing
/// </summary>
public class TransactionProcessingService(
    ITransactionRepository transactionRepository,
    IOrderRepository orderRepository,
    IBuyerRepository buyerRepository,
    ILogger<TransactionProcessingService> logger) : ITransactionProcessingService
{
    private readonly ITransactionRepository _transactionRepository = transactionRepository ?? throw new ArgumentNullException(nameof(transactionRepository));
    private readonly IOrderRepository _orderRepository = orderRepository ?? throw new ArgumentNullException(nameof(orderRepository));
    private readonly IBuyerRepository _buyerRepository = buyerRepository ?? throw new ArgumentNullException(nameof(buyerRepository));
    private readonly ILogger<TransactionProcessingService> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    /// <summary>
    /// Processes a transaction webhook event with consolidated logic to prevent duplicates
    /// </summary>
    public async Task<bool> ProcessTransactionWebhookAsync(TransactionEvent transactionEvent, CancellationToken cancellationToken = default)
    {
        var data = transactionEvent.Data!;
        var eventType = transactionEvent.Event;

        _logger.LogInformation("Processing {EventType} for payment ID {PaymentId}", eventType, data.PaymentId);

        try
        {
            // Get or create transaction
            var transaction = await GetOrCreateTransactionAsync(transactionEvent);
            if (transaction == null)
            {
                _logger.LogError("Failed to get or create transaction for payment ID {PaymentId}", data.PaymentId);
                return false;
            }

            // Process based on event type
            var result = eventType switch
            {
                "TRANSACTION.CREATED" => await ProcessTransactionCreatedAsync(transaction, transactionEvent),
                "TRANSACTION.UPDATED" => await ProcessTransactionUpdatedAsync(transaction, transactionEvent),
                "TRANSACTION.COMPLETED" => await ProcessTransactionCompletedAsync(transaction, transactionEvent),
                _ => throw new InvalidOperationException($"Unsupported event type: {eventType}")
            };

            if (result)
            {
                // Save all changes in a single transaction
                await _transactionRepository.UnitOfWork.SaveEntitiesAsync(cancellationToken);
                _logger.LogInformation("Successfully processed {EventType} for payment ID {PaymentId}", eventType, data.PaymentId);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing {EventType} for payment ID {PaymentId}", eventType, data.PaymentId);
            throw;
        }
    }

    private async Task<Transaction?> GetOrCreateTransactionAsync(TransactionEvent transactionEvent)
    {
        var data = transactionEvent.Data!;
        
        // Try to find existing transaction
        var transaction = await _transactionRepository.GetByPaymentIdAsync(data.PaymentId);
        
        if (transaction != null)
        {
            _logger.LogDebug("Found existing transaction for payment ID {PaymentId}", data.PaymentId);
            return transaction;
        }

        // Create new transaction if it doesn't exist
        _logger.LogInformation("Creating new transaction for payment ID {PaymentId}", data.PaymentId);
        
        transaction = new Transaction(
            transactionReference: transactionEvent.Uuid ?? Guid.NewGuid().ToString(),
            merchantReference: data.MerchantReference ?? "",
            paymentReference: data.PaymentReference ?? "",
            paymentId: data.PaymentId,
            amount: data.Amount,
            remittanceAmount: data.RemittanceAmount,
            responseCode: data.ResponseCode ?? "",
            responseDescription: data.ResponseDescription ?? "",
            cardNumber: data.CardNumber ?? "",
            retrievalReferenceNumber: data.RetrievalReferenceNumber ?? "",
            transactionDate: DateTimeOffset.FromUnixTimeMilliseconds(data.TransactionDate).UtcDateTime,
            accountNumber: data.AccountNumber,
            bankCode: data.BankCode ?? "",
            token: data.Token,
            currencyCode: data.CurrencyCode ?? "",
            channel: data.Channel ?? "",
            merchantCustomerId: data.MerchantCustomerId,
            merchantCustomerName: data.MerchantCustomerName,
            escrow: data.Escrow,
            nonCardProviderId: data.NonCardProviderId,
            payableCode: data.PayableCode ?? "");

        _transactionRepository.Add(transaction);
        return transaction;
    }

    private async Task<bool> ProcessTransactionCreatedAsync(Transaction transaction, TransactionEvent transactionEvent)
    {
        // Only process creation if not already processed
        if (transaction.Status != TransactionStatus.Created)
        {
            transaction.ProcessTransactionCreated();
            await AssociateTransactionWithOrderAsync(transaction);
        }
        else
        {
            _logger.LogDebug("Transaction {PaymentId} already in Created status, skipping creation processing", transaction.PaymentId);
        }

        return true;
    }

    private async Task<bool> ProcessTransactionUpdatedAsync(Transaction transaction, TransactionEvent transactionEvent)
    {
        var data = transactionEvent.Data!;
        
        // Update response code and description if provided and different
        if (!string.IsNullOrEmpty(data.ResponseCode) && 
            (transaction.ResponseCode != data.ResponseCode || transaction.ResponseDescription != data.ResponseDescription))
        {
            transaction.ProcessTransactionUpdated(data.ResponseCode, data.ResponseDescription ?? "");
        }

        return true;
    }

    private async Task<bool> ProcessTransactionCompletedAsync(Transaction transaction, TransactionEvent transactionEvent)
    {
        var data = transactionEvent.Data!;

        // Update response code if provided and different
        if (!string.IsNullOrEmpty(data.ResponseCode) && 
            (transaction.ResponseCode != data.ResponseCode || transaction.ResponseDescription != data.ResponseDescription))
        {
            transaction.ProcessTransactionUpdated(data.ResponseCode, data.ResponseDescription ?? "");
        }

        // Complete transaction only if not already completed
        if (transaction.Status != TransactionStatus.Completed)
        {
            transaction.ProcessTransactionCompleted();
        }
        else
        {
            _logger.LogDebug("Transaction {PaymentId} already completed, skipping completion processing", transaction.PaymentId);
        }

        // Ensure transaction is associated with order
        if (!transaction.OrderId.HasValue)
        {
            await AssociateTransactionWithOrderAsync(transaction);
        }

        return true;
    }

    private async Task AssociateTransactionWithOrderAsync(Transaction transaction)
    {
        if (transaction.OrderId.HasValue)
        {
            _logger.LogDebug("Transaction {PaymentId} already associated with order {OrderId}",
                transaction.PaymentId, transaction.OrderId);
            return;
        }

        // Find order by merchant reference with retry logic for race conditions
        var order = await GetOrderWithRetryAsync(transaction.MerchantReference);
        if (order != null)
        {
            transaction.AssociateWithOrder(order.Id);
            order.SetPaymentReference(transaction.PaymentReference);

            _logger.LogInformation("Associated transaction {PaymentId} with order {OrderId}",
                transaction.PaymentId, order.Id);

            // Create or verify buyer and payment method
            await CreateOrVerifyBuyerAndPaymentMethodAsync(transaction, order);
        }
        else
        {
            _logger.LogWarning("Could not find order with merchant reference {MerchantReference} after retries",
                transaction.MerchantReference);
        }
    }

    private async Task<Order?> GetOrderWithRetryAsync(string merchantReference)
    {
        const int maxRetries = 3;
        const int delayMs = 500;

        for (int attempt = 0; attempt < maxRetries; attempt++)
        {
            var order = await _orderRepository.GetByMerchantReferenceAsync(merchantReference);
            if (order is not null)
            {
                return order;
            }

            // If this is not the last attempt, wait before retrying
            if (attempt < maxRetries - 1)
            {
                _logger.LogInformation("Order not found for merchant reference {MerchantReference}, attempt {Attempt}/{MaxRetries}. Retrying in {DelayMs}ms",
                    merchantReference, attempt + 1, maxRetries, delayMs);
                await Task.Delay(delayMs);
            }
        }

        return null;
    }

    private async Task CreateOrVerifyBuyerAndPaymentMethodAsync(Transaction transaction, Order order)
    {
        // Determine buyer information - prefer webhook data, fallback to order context
        string buyerId;
        string buyerName;

        if (!string.IsNullOrEmpty(transaction.MerchantCustomerId) && 
            !string.IsNullOrEmpty(transaction.MerchantCustomerName))
        {
            buyerId = transaction.MerchantCustomerId;
            buyerName = transaction.MerchantCustomerName;
        }
        else
        {
            // Fallback to order context
            buyerId = $"order-{order.Id}";
            buyerName = $"Customer for Order {order.Id}";
            
            _logger.LogInformation("No customer information in webhook for transaction {PaymentId}. Using order context for buyer creation.",
                transaction.PaymentId);
        }

        // Get or create buyer
        var buyer = await _buyerRepository.FindAsync(buyerId);
        if (buyer == null)
        {
            buyer = new Buyer(buyerId, buyerName);
            _buyerRepository.Add(buyer);
            
            _logger.LogInformation("Created new buyer {BuyerId} for transaction {PaymentId}", 
                buyerId, transaction.PaymentId);
        }

        // Create or verify payment method
        var paymentMethod = buyer.VerifyOrAddInterSwitchPaymentMethod(
            alias: $"Interswitch-{transaction.PaymentId}",
            paymentReference: transaction.PaymentReference,
            cardHolderName: buyerName,
            orderId: order.Id);

        _logger.LogInformation("Created/verified payment method {PaymentMethodId} for buyer {BuyerId} and transaction {PaymentId}",
            paymentMethod.Id, buyer.Id, transaction.PaymentId);
    }
}
