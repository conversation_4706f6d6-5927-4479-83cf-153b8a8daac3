using Order = Ordering.Domain.AggregatesModel.OrderAggregate.Order;

namespace Ordering.API.Application.Models;

public class OrderDraftDto
{
    public IEnumerable<OrderItemDto> OrderItems { get; set; } = [];
    public decimal Total { get; set; }

    public static OrderDraftDto FromOrder(Order order)
    {
        return new OrderDraftDto()
        {
            OrderItems = order.OrderItems.Select(oi => new OrderItemDto
            {
                Discount = oi.Discount,
                ProductId = oi.ProductId,
                UnitPrice = oi.UnitPrice,
                PictureUrl = oi.PictureUrl,
                Units = oi.Units,
                ProductName = oi.ProductName
            }),
            Total = order.GetTotal()
        };
    }
}

public class OrderItemDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public decimal UnitPrice { get; set; }
    public decimal Discount { get; set; }
    public int Units { get; set; }
    public string? PictureUrl { get; set; }
}
