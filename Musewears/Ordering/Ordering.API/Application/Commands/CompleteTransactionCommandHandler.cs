using MediatR;
using Musewears.Shared.Models;
using Ordering.API.Application.Services;
using Ordering.Domain.AggregatesModel.TransactionAggregate;

namespace Ordering.API.Application.Commands;

/// <summary>
/// Command handler for completing transactions from TRANSACTION.COMPLETED webhooks
/// Now uses the consolidated TransactionProcessingService to prevent duplicate processing
/// </summary>
public class CompleteTransactionCommandHandler(
    ITransactionProcessingService transactionProcessingService,
    ILogger<CompleteTransactionCommandHandler> logger)
    : IRequestHandler<CompleteTransactionCommand, bool>
{
    private readonly ITransactionProcessingService _transactionProcessingService = transactionProcessingService ?? throw new ArgumentNullException(nameof(transactionProcessingService));
    private readonly ILogger<CompleteTransactionCommandHandler> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    public async Task<bool> Handle(CompleteTransactionCommand command, CancellationToken cancellationToken)
    {
        var transactionEvent = command.TransactionEvent;
        var data = transactionEvent.Data!;

        _logger.LogInformation("Completing transaction for payment ID {PaymentId}", data.PaymentId);

        try
        {
            // Use the consolidated transaction processing service
            var result = await _transactionProcessingService.ProcessTransactionWebhookAsync(transactionEvent, cancellationToken);

            if (result)
            {
                _logger.LogInformation("Successfully completed transaction for payment ID {PaymentId}", data.PaymentId);
            }
            else
            {
                _logger.LogError("Failed to complete transaction for payment ID {PaymentId}", data.PaymentId);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing transaction for payment ID {PaymentId}", data.PaymentId);
            throw;
        }
    }

}
