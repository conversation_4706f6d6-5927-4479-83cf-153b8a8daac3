using MediatR;
using Ordering.API.Application.Services;
using Ordering.Domain.AggregatesModel.TransactionAggregate;

namespace Ordering.API.Application.Commands;

/// <summary>
/// Command handler for creating transactions from TRANSACTION.CREATED webhooks
/// Now uses the consolidated TransactionProcessingService to prevent duplicate processing
/// </summary>
public class CreateTransactionCommandHandler(
    ITransactionProcessingService transactionProcessingService,
    ILogger<CreateTransactionCommandHandler> logger)
    : IRequestHandler<CreateTransactionCommand, bool>
{
    private readonly ITransactionProcessingService _transactionProcessingService = transactionProcessingService ?? throw new ArgumentNullException(nameof(transactionProcessingService));
    private readonly ILogger<CreateTransactionCommandHandler> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    public async Task<bool> Handle(CreateTransactionCommand command, CancellationToken cancellationToken)
    {
        var transactionEvent = command.TransactionEvent;
        var data = transactionEvent.Data!;

        _logger.LogInformation("Creating transaction for payment ID {PaymentId}, event type {EventType}",
            data.PaymentId, transactionEvent.Event);

        try
        {
            // Use the consolidated transaction processing service
            var result = await _transactionProcessingService.ProcessTransactionWebhookAsync(transactionEvent, cancellationToken);

            if (result)
            {
                _logger.LogInformation("Successfully created transaction for payment ID {PaymentId}", data.PaymentId);
            }
            else
            {
                _logger.LogError("Failed to create transaction for payment ID {PaymentId}", data.PaymentId);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating transaction for payment ID {PaymentId}", data.PaymentId);
            throw;
        }
    }
}
