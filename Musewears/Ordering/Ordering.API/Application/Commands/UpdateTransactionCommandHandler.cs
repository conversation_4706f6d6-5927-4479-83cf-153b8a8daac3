using MediatR;
using Musewears.Shared.Models;
using Ordering.API.Application.Services;
using Ordering.Domain.AggregatesModel.TransactionAggregate;

namespace Ordering.API.Application.Commands;

/// <summary>
/// Command handler for updating transactions from TRANSACTION.UPDATED webhooks
/// Now uses the consolidated TransactionProcessingService to prevent duplicate processing
/// </summary>
public class UpdateTransactionCommandHandler(
    ITransactionProcessingService transactionProcessingService,
    ILogger<UpdateTransactionCommandHandler> logger)
    : IRequestHandler<UpdateTransactionCommand, bool>
{
    private readonly ITransactionProcessingService _transactionProcessingService = transactionProcessingService ?? throw new ArgumentNullException(nameof(transactionProcessingService));
    private readonly ILogger<UpdateTransactionCommandHandler> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    public async Task<bool> Handle(UpdateTransactionCommand command, CancellationToken cancellationToken)
    {
        var transactionEvent = command.TransactionEvent;
        var data = transactionEvent.Data!;

        _logger.LogInformation("Updating transaction for payment ID {PaymentId}, event type {EventType}",
            data.PaymentId, transactionEvent.Event);

        try
        {
            // Use the consolidated transaction processing service
            var result = await _transactionProcessingService.ProcessTransactionWebhookAsync(transactionEvent, cancellationToken);

            if (result)
            {
                _logger.LogInformation("Successfully updated transaction for payment ID {PaymentId}", data.PaymentId);
            }
            else
            {
                _logger.LogError("Failed to update transaction for payment ID {PaymentId}", data.PaymentId);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating transaction for payment ID {PaymentId}", data.PaymentId);
            throw;
        }
    }

}
