using Ordering.Domain.AggregatesModel.OrderAggregate;
using Ordering.Infrastructure.Idempotency;

namespace Ordering.API.Application.Commands;

// Regular CommandHandler
public class SetPaidOrderStatusCommandHandler(IOrderRepository orderRepository, IMediator mediator)
    : IRequestHandler<SetPaidOrderStatusCommand, bool>
{
    /// <summary>
    /// Handler which processes the command when
    /// Shipment service confirms the payment
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    public async Task<bool> Handle(SetPaidOrderStatusCommand command, CancellationToken cancellationToken)
    {
        // Note: Removed artificial delay for Interswitch payments - validation happens via webhook

        var orderToUpdate = await orderRepository.GetAsync(command.OrderNumber);
        if (orderToUpdate is null)
        {
            return false;
        }

        // Use SetInterswitchPaidStatus for orders coming from Interswitch payments
        // This allows transition from Submitted status to Paid
        if (orderToUpdate.OrderStatus == OrderStatus.Submitted)
        {
            orderToUpdate.SetInterswitchPaidStatus(orderToUpdate.PaymentReference ?? "");
        }
        else
        {
            // Use regular SetPaidStatus for traditional payment flow (StockConfirmed -> Paid)
            orderToUpdate.SetPaidStatus();
        }

        return await orderRepository.UnitOfWork.SaveEntitiesAsync(cancellationToken);
    }
}


// Use for Idempotency in Command process
public class SetPaidIdentifiedOrderStatusCommandHandler(
    IMediator mediator,
    IRequestManager requestManager,
    ILogger<IdentifiedCommandHandler<SetPaidOrderStatusCommand, bool>> logger)
    : IdentifiedCommandHandler<SetPaidOrderStatusCommand, bool>(mediator, requestManager, logger)
{
    protected override bool CreateResultForDuplicateRequest()
    {
        return true; // Ignore duplicate requests for processing order.
    }
}
