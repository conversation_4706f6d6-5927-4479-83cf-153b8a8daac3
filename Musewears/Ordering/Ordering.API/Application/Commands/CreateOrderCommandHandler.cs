using MediatR;
using Microsoft.Extensions.Logging;
using Ordering.API.Application.Services;
using Ordering.Domain.AggregatesModel.OrderAggregate;
using Ordering.Infrastructure.Idempotency;
using Order = Ordering.Domain.AggregatesModel.OrderAggregate.Order;

namespace Ordering.API.Application.Commands;

// Regular CommandHandler
public class CreateOrderCommandHandler(
    IMediator mediator,
    IOrderRepository orderRepository,
    IIdentityService identityService,
    ILogger<CreateOrderCommandHandler> logger)
    : IRequestHandler<CreateOrderCommand, bool>
{
    private readonly IOrderRepository _orderRepository = orderRepository ?? throw new ArgumentNullException(nameof(orderRepository));
    private readonly IIdentityService _identityService = identityService ?? throw new ArgumentNullException(nameof(identityService));
    private readonly IMediator _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
    private readonly ILogger<CreateOrderCommandHandler> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    // Using DI to inject infrastructure persistence Repositories

    public async Task<bool> Handle(CreateOrderCommand message, CancellationToken cancellationToken)
    {
        // In a monolith, the OrderStartedDomainEvent will be automatically published
        // when the order is created, which can handle any basket cleanup logic

        // Check if order with same merchant reference already exists to prevent duplicates
        if (!string.IsNullOrEmpty(message.MerchantReference))
        {
            var existingOrder = await _orderRepository.GetByMerchantReferenceAsync(message.MerchantReference);
            if (existingOrder != null)
            {
                _logger.LogWarning("Order with merchant reference {MerchantReference} already exists with ID {OrderId}",
                    message.MerchantReference, existingOrder.Id);
                return true; // Return true as the order already exists
            }
        }

        // Add/Update the Buyer AggregateRoot
        // DDD patterns comment: Add child entities and value-objects through the Order Aggregate-Root
        // methods and constructor so validations, invariants and business logic
        // make sure that consistency is preserved across the whole aggregate
        var address = new Address(message.Street, message.City, message.State, message.Country, message.ZipCode);
        var order = new Order(message.UserId, message.UserName, address, buyerId: null, paymentMethodId: null);

        // Set payment reference if provided (for Interswitch payments)
        if (!string.IsNullOrEmpty(message.PaymentReference))
        {
            order.SetPaymentReference(message.PaymentReference);
        }

        // Set merchant reference if provided (our generated txn_ref)
        if (!string.IsNullOrEmpty(message.MerchantReference))
        {
            order.SetMerchantReference(message.MerchantReference);
        }

        foreach (var item in message.OrderItems)
        {
            order.AddOrderItem(item.ProductId, item.ProductName, item.UnitPrice, item.Discount, item.PictureUrl, item.Units);
        }

        _logger.LogInformation("Creating Order - Order: {@Order}", order);

        _orderRepository.Add(order);

        try
        {
            return await _orderRepository.UnitOfWork.SaveEntitiesAsync(cancellationToken);
        }
        catch (Exception ex) when (ex.Message.Contains("duplicate") || ex.Message.Contains("unique"))
        {
            // Handle potential race condition where duplicate merchant reference is created
            _logger.LogWarning(ex, "Duplicate order creation attempt for merchant reference {MerchantReference}", message.MerchantReference);
            return true; // Return true as the order likely already exists
        }
    }
}


// Use for Idempotency in Command process
public class CreateOrderIdentifiedCommandHandler(
    IMediator mediator,
    IRequestManager requestManager,
    ILogger<IdentifiedCommandHandler<CreateOrderCommand, bool>> logger)
    : IdentifiedCommandHandler<CreateOrderCommand, bool>(mediator, requestManager, logger)
{
    protected override bool CreateResultForDuplicateRequest()
    {
        return true; // Ignore duplicate requests for creating order.
    }
}
