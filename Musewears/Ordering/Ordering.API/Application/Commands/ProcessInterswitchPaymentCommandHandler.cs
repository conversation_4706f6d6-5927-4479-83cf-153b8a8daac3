using Ordering.Domain.AggregatesModel.OrderAggregate;
using Ordering.Infrastructure.Idempotency;

namespace Ordering.API.Application.Commands;

// Regular CommandHandler
public class ProcessInterswitchPaymentCommandHandler(IOrderRepository orderRepository, ILogger<ProcessInterswitchPaymentCommandHandler> logger)
    : IRequestHandler<ProcessInterswitchPaymentCommand, bool>
{
    private readonly IOrderRepository _orderRepository = orderRepository ?? throw new ArgumentNullException(nameof(orderRepository));
    private readonly ILogger<ProcessInterswitchPaymentCommandHandler> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    public async Task<bool> Handle(ProcessInterswitchPaymentCommand command, CancellationToken cancellationToken)
    {
        // Find the order by merchant reference with retry logic for race conditions
        var order = await GetOrderWithRetryAsync(command.MerchantReference, cancellationToken);

        if (order is null)
        {
            // If order still doesn't exist after retries, we can't process the payment
            // This might happen if the webhook arrives before the order is created
            _logger.LogWarning("Order not found for merchant reference {MerchantReference} after retries", command.MerchantReference);
            return false;
        }

        // Process the Interswitch payment - this will raise domain events
        order.ProcessInterSwitchPayment(
            command.PaymentReference,
            command.MerchantReference,
            command.UserId,
            command.UserName,
            command.PaymentStatus,
            command.PaymentId,
            command.MerchantCustomerId,
            command.MerchantCustomerName,
            command.ResponseCode);

        return await _orderRepository.UnitOfWork.SaveEntitiesAsync(cancellationToken);
    }

    private async Task<Order?> GetOrderWithRetryAsync(string merchantReference, CancellationToken cancellationToken)
    {
        const int maxRetries = 3;
        const int delayMs = 500;

        for (int attempt = 0; attempt < maxRetries; attempt++)
        {
            var order = await _orderRepository.GetByMerchantReferenceAsync(merchantReference);
            if (order is not null)
            {
                return order;
            }

            // If this is not the last attempt, wait before retrying
            if (attempt < maxRetries - 1)
            {
                _logger.LogInformation("Order not found for merchant reference {MerchantReference}, attempt {Attempt}/{MaxRetries}. Retrying in {DelayMs}ms",
                    merchantReference, attempt + 1, maxRetries, delayMs);
                await Task.Delay(delayMs, cancellationToken);
            }
        }

        return null;
    }
}

// Use for Idempotency in Command process
public class ProcessInterswitchPaymentIdentifiedCommandHandler(
    IMediator mediator,
    IRequestManager requestManager,
    ILogger<IdentifiedCommandHandler<ProcessInterswitchPaymentCommand, bool>> logger)
    : IdentifiedCommandHandler<ProcessInterswitchPaymentCommand, bool>(mediator, requestManager, logger)
{
    protected override bool CreateResultForDuplicateRequest()
    {
        return true; // Ignore duplicate requests for processing Interswitch payments.
    }
}
