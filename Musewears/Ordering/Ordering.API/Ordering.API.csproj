<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Asp.Versioning.Http" Version="8.1.0" />
        <PackageReference Include="Aspire.Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.3.0" />
        <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.1" />
        <PackageReference Include="MediatR" Version="12.5.0" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.5">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.5" />
        <PackageReference Include="System.Runtime.Serialization.Primitives" Version="4.3.0" />
        <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    </ItemGroup>
    
    <ItemGroup>
        <ProjectReference Include="..\Ordering.Domain\Ordering.Domain.csproj" />
        <ProjectReference Include="..\Ordering.Infrastructure\Ordering.Infrastructure.csproj" />
        <ProjectReference Include="..\..\DomainEventBus\DomainEventBus.csproj" />
    </ItemGroup>


    <ItemGroup>
        <Compile Include="..\..\Shared\MigrateDbContextExtensions.cs" Link="Extensions\MigrateDbContextExtensions.cs" />
        <Compile Include="..\..\Shared\ActivityExtensions.cs" Link="Extensions\ActivityExtensions.cs" />
        <Compile Include="..\..\Shared\HostEnvironmentExtensions.cs" Link="Extensions\HostEnvironmentExtensions.cs" />
<!--        <Compile Include="..\..\Shared\Models\PaymentStatus.cs">-->
<!--          <Link>Models\PaymentStatus.cs</Link>-->
<!--        </Compile>-->
        <Compile Include="..\..\Shared\Models\TransactionEvent.cs" Link="Models\TransactionEvent.cs" />
        <Compile Remove="Application\Commands\ProcessInterswitchWebhookCommand.cs" />
        <Compile Remove="Application\Commands\ProcessInterswitchWebhookCommandHandler.cs" />
        <Compile Remove="Application\DomainEventHandlers\InterswitchPaymentWebhookProcessedDomainEventHandler.cs" />
        <Compile Include="..\..\Shared\Models\TransactionEventData.cs">
          <Link>Models\TransactionEventData.cs</Link>
        </Compile>
    </ItemGroup>

</Project>
