<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="MediatR" Version="12.5.0" />
        <PackageReference Include="System.Reflection.TypeExtensions" Version="4.7.0" />
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="AggregatesModel\OrderAggregate\InterswitchPaymentDetails.cs" />
      <Compile Remove="Events\InterswitchPaymentWebhookProcessedDomainEvent.cs" />
    </ItemGroup>
</Project>
