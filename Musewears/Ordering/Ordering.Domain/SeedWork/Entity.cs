using System.Reflection;

namespace Ordering.Domain.SeedWork;

public abstract class Entity
{
    int? _requestedHashCode;
    int _Id;
    public virtual int Id
    {
        get
        {
            return _Id;
        }
        protected set
        {
            _Id = value;
        }
    }

    private List<INotification> _domainEvents;
    public IReadOnlyCollection<INotification> DomainEvents => _domainEvents?.AsReadOnly();

    public void AddDomainEvent(INotification eventItem)
    {
        _domainEvents = _domainEvents ?? [];

        // Check for duplicate events before adding
        if (!HasDuplicateEvent(eventItem))
        {
            _domainEvents.Add(eventItem);
        }
    }

    public void RemoveDomainEvent(INotification eventItem)
    {
        _domainEvents?.Remove(eventItem);
    }

    public void ClearDomainEvents()
    {
        _domainEvents?.Clear();
    }

    /// <summary>
    /// Checks if a domain event of the same type with similar properties already exists
    /// </summary>
    protected virtual bool HasDuplicateEvent(INotification eventItem)
    {
        if (_domainEvents == null || !_domainEvents.Any())
            return false;

        return _domainEvents.Any(existingEvent =>
            existingEvent.GetType() == eventItem.GetType() &&
            AreEventsEqual(existingEvent, eventItem));
    }

    /// <summary>
    /// Compares two domain events for equality based on their properties
    /// Override this method in derived classes for custom comparison logic
    /// </summary>
    protected virtual bool AreEventsEqual(INotification event1, INotification event2)
    {
        if (event1.GetType() != event2.GetType())
            return false;

        // For simple record types or events with primitive properties,
        // we can use reflection to compare properties
        var properties = event1.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);

        foreach (var property in properties)
        {
            var value1 = property.GetValue(event1);
            var value2 = property.GetValue(event2);

            if (!Equals(value1, value2))
                return false;
        }

        return true;
    }

    /// <summary>
    /// Checks if there's a pending domain event of the specified type that matches the predicate
    /// </summary>
    protected bool HasPendingDomainEvent<T>(Func<T, bool> predicate) where T : INotification
    {
        return _domainEvents?.OfType<T>().Any(predicate) ?? false;
    }

    public bool IsTransient()
    {
        return this.Id == default;
    }

    public override bool Equals(object obj)
    {
        if (obj == null || !(obj is Entity))
            return false;

        if (Object.ReferenceEquals(this, obj))
            return true;

        if (this.GetType() != obj.GetType())
            return false;

        Entity item = (Entity)obj;

        if (item.IsTransient() || this.IsTransient())
            return false;
        else
            return item.Id == this.Id;
    }

    public override int GetHashCode()
    {
        if (!IsTransient())
        {
            if (!_requestedHashCode.HasValue)
                _requestedHashCode = this.Id.GetHashCode() ^ 31; // XOR for random distribution (http://blogs.msdn.com/b/ericlippert/archive/2011/02/28/guidelines-and-rules-for-gethashcode.aspx)

            return _requestedHashCode.Value;
        }
        else
            return base.GetHashCode();

    }
    public static bool operator ==(Entity? left, Entity right)
    {
        if (Object.Equals(left, null))
            return (Object.Equals(right, null)) ? true : false;
        else
            return left.Equals(right);
    }

    public static bool operator !=(Entity? left, Entity right)
    {
        return !(left == right);
    }
}
