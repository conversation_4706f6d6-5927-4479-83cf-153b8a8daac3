using System.ComponentModel.DataAnnotations;
using Ordering.Domain.Exceptions;

namespace Ordering.Domain.AggregatesModel.BuyerAggregate;

public class PaymentMethod : Entity
{
    [Required]
    private string _alias;
    [Required]
    private string _cardNumber;
    private string _securityNumber;
    [Required]
    private string _cardHolderName;
    private DateTime _expiration;

    private int _cardTypeId;

    // For Interswitch payments
    private string? _paymentReference;
    private string? _paymentProvider;

    public CardType? CardType { get; private set; }
    public string? PaymentReference => _paymentReference;
    public string? PaymentProvider => _paymentProvider;

    protected PaymentMethod() { }

    // Constructor for card-based payments
    public PaymentMethod(int cardTypeId, string alias, string cardNumber, string securityNumber, string cardHolderName, DateTime expiration)
    {
        _cardNumber = !string.IsNullOrWhiteSpace(cardNumber) ? cardNumber : throw new OrderingDomainException(nameof(cardNumber));
        _securityNumber = !string.IsNullOrWhiteSpace(securityNumber) ? securityNumber : throw new OrderingDomainException(nameof(securityNumber));
        _cardHolderName = !string.IsNullOrWhiteSpace(cardHolderName) ? cardHolderName : throw new OrderingDomainException(nameof(cardHolderName));

        if (expiration < DateTime.UtcNow)
        {
            throw new OrderingDomainException(nameof(expiration));
        }

        _alias = alias;
        _expiration = expiration;
        _cardTypeId = cardTypeId;
        _paymentProvider = "Card";
    }

    // Constructor for Interswitch payments
    public PaymentMethod(string alias, string paymentReference, string cardHolderName)
    {
        _alias = !string.IsNullOrWhiteSpace(alias) ? alias : throw new OrderingDomainException(nameof(alias));
        _paymentReference = !string.IsNullOrWhiteSpace(paymentReference) ? paymentReference : throw new OrderingDomainException(nameof(paymentReference));
        _cardHolderName = !string.IsNullOrWhiteSpace(cardHolderName) ? cardHolderName : throw new OrderingDomainException(nameof(cardHolderName));

        _paymentProvider = "Interswitch";
        _cardNumber = "****"; // Placeholder for Interswitch payments
        _securityNumber = "***"; // Placeholder for Interswitch payments
        _expiration = DateTime.UtcNow.AddYears(1); // Default expiration for non-card payments
        _cardTypeId = 0; // Default for non-card payments
    }

    public bool IsEqualTo(int cardTypeId, string cardNumber, DateTime expiration)
    {
        return _cardTypeId == cardTypeId
            && _cardNumber == cardNumber
            && _expiration == expiration;
    }

    public bool IsEqualToInterswitch(string paymentReference)
    {
        return _paymentProvider == "Interswitch"
            && _paymentReference == paymentReference;
    }
}
