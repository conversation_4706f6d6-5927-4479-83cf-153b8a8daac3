using Ordering.Domain.SeedWork;

namespace Ordering.Domain.AggregatesModel.TransactionAggregate;

/// <summary>
/// Repository interface for Transaction aggregate
/// </summary>
public interface ITransactionRepository : IRepository<Transaction>
{
    Transaction Add(Transaction transaction);
    void Update(Transaction transaction);
    Task<Transaction?> GetAsync(int transactionId);
    Task<Transaction?> GetByPaymentIdAsync(int paymentId);
    Task<Transaction?> GetByMerchantReferenceAsync(string merchantReference);
    Task<Transaction?> GetByPaymentReferenceAsync(string paymentReference);
    Task<IEnumerable<Transaction>> GetByOrderIdAsync(int orderId);
}
