using Ordering.Domain.SeedWork;
using Ordering.Domain.Events;

namespace Ordering.Domain.AggregatesModel.TransactionAggregate;

/// <summary>
/// Transaction aggregate representing Interswitch payment transactions
/// Stores comprehensive transaction data and handles transaction lifecycle
/// </summary>
public class Transaction : Entity, IAggregateRoot
{
    public string TransactionReference { get; private set; }
    public string MerchantReference { get; private set; }
    public string PaymentReference { get; private set; }
    public int PaymentId { get; private set; }
    public decimal Amount { get; private set; }
    public decimal RemittanceAmount { get; private set; }
    public string ResponseCode { get; private set; }
    public string ResponseDescription { get; private set; }
    public string CardNumber { get; private set; }
    public string RetrievalReferenceNumber { get; private set; }
    public DateTime TransactionDate { get; private set; }
    public string? AccountNumber { get; private set; }
    public string BankCode { get; private set; }
    public string? Token { get; private set; }
    public string CurrencyCode { get; private set; }
    public string Channel { get; private set; }
    public string? MerchantCustomerId { get; private set; }
    public string? MerchantCustomerName { get; private set; }
    public bool Escrow { get; private set; }
    public string? NonCardProviderId { get; private set; }
    public string PayableCode { get; private set; }
    public TransactionStatus Status { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime UpdatedAt { get; private set; }

    // Navigation property to related order
    public int? OrderId { get; private set; }

    protected Transaction() { }

    public Transaction(
        string transactionReference,
        string merchantReference,
        string paymentReference,
        int paymentId,
        decimal amount,
        decimal remittanceAmount,
        string responseCode,
        string responseDescription,
        string cardNumber,
        string retrievalReferenceNumber,
        DateTime transactionDate,
        string? accountNumber,
        string bankCode,
        string? token,
        string currencyCode,
        string channel,
        string? merchantCustomerId,
        string? merchantCustomerName,
        bool escrow,
        string? nonCardProviderId,
        string payableCode)
    {
        TransactionReference = transactionReference ?? throw new ArgumentNullException(nameof(transactionReference));
        MerchantReference = merchantReference ?? throw new ArgumentNullException(nameof(merchantReference));
        PaymentReference = paymentReference ?? throw new ArgumentNullException(nameof(paymentReference));
        PaymentId = paymentId;
        Amount = amount;
        RemittanceAmount = remittanceAmount;
        ResponseCode = responseCode ?? throw new ArgumentNullException(nameof(responseCode));
        ResponseDescription = responseDescription ?? throw new ArgumentNullException(nameof(responseDescription));
        CardNumber = cardNumber ?? string.Empty;
        RetrievalReferenceNumber = retrievalReferenceNumber ?? throw new ArgumentNullException(nameof(retrievalReferenceNumber));
        TransactionDate = transactionDate;
        AccountNumber = accountNumber;
        BankCode = bankCode ?? throw new ArgumentNullException(nameof(bankCode));
        Token = token;
        CurrencyCode = currencyCode ?? throw new ArgumentNullException(nameof(currencyCode));
        Channel = channel ?? throw new ArgumentNullException(nameof(channel));
        MerchantCustomerId = merchantCustomerId;
        MerchantCustomerName = merchantCustomerName;
        Escrow = escrow;
        NonCardProviderId = nonCardProviderId;
        PayableCode = payableCode ?? throw new ArgumentNullException(nameof(payableCode));
        Status = DetermineStatus(responseCode);
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }

    public void ProcessTransactionCreated()
    {
        Status = TransactionStatus.Created;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new TransactionCreatedDomainEvent(this));
    }

    public void ProcessTransactionUpdated(string newResponseCode, string newResponseDescription)
    {
        ResponseCode = newResponseCode;
        ResponseDescription = newResponseDescription;
        Status = DetermineStatus(newResponseCode);
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new TransactionUpdatedDomainEvent(this));
    }

    public void ProcessTransactionCompleted()
    {
        Status = TransactionStatus.Completed;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new TransactionCompletedDomainEvent(this));
    }

    public void AssociateWithOrder(int orderId)
    {
        OrderId = orderId;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new TransactionOrderAssociatedDomainEvent(this, orderId));
    }

    public bool IsSuccessful => ResponseCode == "00";

    public string GetPaymentStatus()
    {
        return ResponseCode switch
        {
            "00" => "completed",  // Approved
            "10" => "completed",  // Approved (partial)
            "11" => "completed",  // Approved (VIP)
            "09" => "pending",    // In Progress
            "01" => "failed",
            "02" => "failed",
            "03" => "failed",
            "04" => "failed",
            "05" => "failed",
            "06" => "failed",
            "07" => "failed",
            "08" => "failed",
            "12" => "failed",
            "13" => "failed",
            "14" => "failed",
            "15" => "failed",
            _ => "pending"
        };
    }

    private static TransactionStatus DetermineStatus(string responseCode)
    {
        return responseCode switch
        {
            "00" => TransactionStatus.Successful,  // Approved
            "10" => TransactionStatus.Successful,  // Approved (partial)
            "11" => TransactionStatus.Successful,  // Approved (VIP)
            "09" => TransactionStatus.Pending,     // In Progress
            _ when responseCode.StartsWith("0") && responseCode != "00" && responseCode != "09" && responseCode != "10" && responseCode != "11" => TransactionStatus.Failed,
            _ => TransactionStatus.Pending
        };
    }
}

public enum TransactionStatus
{
    Created = 1,
    Pending = 2,
    Successful = 3,
    Failed = 4,
    Updated = 5,
    Completed = 6
}
