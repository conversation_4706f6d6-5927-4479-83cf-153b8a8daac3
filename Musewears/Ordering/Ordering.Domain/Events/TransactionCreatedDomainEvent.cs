using MediatR;
using Ordering.Domain.AggregatesModel.TransactionAggregate;

namespace Ordering.Domain.Events;

/// <summary>
/// Domain event raised when a transaction is created (TRANSACTION.CREATED webhook)
/// </summary>
public class TransactionCreatedDomainEvent(Transaction transaction) : INotification
{
    public Transaction Transaction { get; } = transaction ?? throw new ArgumentNullException(nameof(transaction));
}
