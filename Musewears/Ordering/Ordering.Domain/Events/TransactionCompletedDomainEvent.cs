using MediatR;
using Ordering.Domain.AggregatesModel.TransactionAggregate;

namespace Ordering.Domain.Events;

/// <summary>
/// Domain event raised when a transaction is completed (TRANSACTION.COMPLETED webhook)
/// </summary>
public class TransactionCompletedDomainEvent(Transaction transaction) : INotification
{
    public Transaction Transaction { get; } = transaction ?? throw new ArgumentNullException(nameof(transaction));
}
