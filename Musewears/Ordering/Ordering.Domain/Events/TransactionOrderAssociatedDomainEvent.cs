using MediatR;
using Ordering.Domain.AggregatesModel.TransactionAggregate;

namespace Ordering.Domain.Events;

/// <summary>
/// Domain event raised when a transaction is associated with an order
/// </summary>
public class TransactionOrderAssociatedDomainEvent : INotification
{
    public Transaction Transaction { get; }
    public int OrderId { get; }

    public TransactionOrderAssociatedDomainEvent(Transaction transaction, int orderId)
    {
        Transaction = transaction ?? throw new ArgumentNullException(nameof(transaction));
        OrderId = orderId;
    }
}
