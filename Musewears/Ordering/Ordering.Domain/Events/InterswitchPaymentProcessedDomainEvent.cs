namespace Ordering.Domain.Events;

/// <summary>
/// Domain event raised when an Interswitch payment is processed
/// This event triggers buyer creation/verification and payment method association
/// </summary>
public record InterswitchPaymentProcessedDomainEvent(
    int OrderId,
    string PaymentReference,
    string UserId,
    string UserName,
    string PaymentStatus,
    int? PaymentId = null,
    string? MerchantCustomerId = null,
    string? MerchantCustomerName = null) : INotification;
