using MediatR;
using Ordering.Domain.AggregatesModel.TransactionAggregate;

namespace Ordering.Domain.Events;

/// <summary>
/// Domain event raised when a transaction is updated (TRANSACTION.UPDATED webhook)
/// </summary>
public class TransactionUpdatedDomainEvent(Transaction transaction) : INotification
{
    public Transaction Transaction { get; } = transaction ?? throw new ArgumentNullException(nameof(transaction));
}
