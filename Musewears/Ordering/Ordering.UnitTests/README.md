# Ordering Unit Tests

This project contains unit tests for the Ordering domain and application layers, specifically designed to verify the fixes for the issues encountered with order creation and domain event handling.

## Test Coverage

### 1. Domain Tests (`OrderAggregateTests.cs`)

Tests the Order aggregate root to ensure:
- ✅ **Description is always set**: Verifies that the Order constructor sets a default description to avoid null constraint violations
- ✅ **Order properties are correctly initialized**: Validates all required properties are set during order creation
- ✅ **Domain events are properly added**: Ensures OrderStartedDomainEvent is added when an order is created
- ✅ **Draft orders work correctly**: Tests the NewDraft factory method
- ✅ **Order items can be added**: Validates the AddOrderItem functionality
- ✅ **Status changes update description**: Tests that status changes properly update the description field

### 2. Infrastructure Tests (`DomainEventDispatchingTests.cs`)

Tests the domain event dispatching mechanism to ensure:
- ✅ **Scoped services can be resolved**: Verifies that the dependency injection fix allows scoped services like IBuyerRepository to be resolved during domain event handling
- ✅ **Buyer creation through domain events**: Tests that OrderStartedDomainEvent properly creates buyers
- ✅ **No duplicate buyers**: Ensures existing buyers are not duplicated
- ✅ **Multiple events are processed**: Validates that multiple domain events can be processed successfully

### 3. Application Tests (`CreateOrderCommandHandlerTests.cs`)

Tests the complete order creation flow to ensure:
- ✅ **End-to-end order creation**: Verifies the complete CreateOrderCommand handling process
- ✅ **Description is set in real scenarios**: Ensures the description fix works in the actual command handler
- ✅ **Buyer creation through domain events**: Tests that buyers are created as part of the order creation process
- ✅ **Multiple order items**: Validates handling of orders with multiple items
- ✅ **Correct property mapping**: Ensures all order properties are correctly set from the command

## Issues Fixed

### Issue 1: Database Constraint Violation
**Problem**: The Description column was null, violating the database NOT NULL constraint.

**Fix**: Modified the Order constructor and NewDraft method to always set a default description.

**Tests**: 
- `Order_Constructor_Should_Set_Default_Description`
- `Order_NewDraft_Should_Set_Default_Description`
- `Handle_Should_Create_Order_With_Description_Set`

### Issue 2: Dependency Injection Problem
**Problem**: Domain event handlers requiring scoped services (like IBuyerRepository) couldn't be resolved from the root provider.

**Fix**: Modified `MediatorExtension.DispatchDomainEventsAsync` to create a proper scope when publishing domain events.

**Tests**:
- `DispatchDomainEventsAsync_Should_Resolve_Scoped_Services_Successfully`
- `OrderStartedDomainEvent_Should_Create_Buyer_When_Not_Exists`
- `Handle_Should_Create_Buyer_Through_Domain_Event`

## Running the Tests

### Option 1: Using the provided scripts
```bash
# On Linux/macOS
./run-tests.sh

# On Windows
run-tests.bat
```

### Option 2: Using dotnet CLI directly
```bash
# Navigate to the test project
cd Musewears/Ordering/Ordering.UnitTests

# Restore packages
dotnet restore

# Run tests
dotnet test
```

### Option 3: Using Visual Studio
1. Open the solution in Visual Studio
2. Build the solution
3. Open Test Explorer (Test → Test Explorer)
4. Run all tests or specific test classes

## Test Dependencies

The tests use the following frameworks and libraries:
- **xUnit**: Testing framework
- **FluentAssertions**: Assertion library for more readable tests
- **Moq**: Mocking framework for dependencies
- **Entity Framework In-Memory**: For testing database operations without a real database
- **MediatR**: For testing domain event handling

## Expected Results

All tests should pass, confirming that:
1. Orders are always created with a non-null description
2. Domain events can be dispatched without dependency injection errors
3. The complete order creation flow works end-to-end
4. Buyers are properly created through domain events

If any tests fail, it indicates that the fixes may not be working correctly or there may be additional issues to address.
