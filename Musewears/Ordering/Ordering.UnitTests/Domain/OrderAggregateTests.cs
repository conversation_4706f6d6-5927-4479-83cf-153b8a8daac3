using FluentAssertions;
using Ordering.Domain.AggregatesModel.OrderAggregate;
using Ordering.Domain.Events;
using Xunit;

namespace Ordering.UnitTests.Domain;

public class OrderAggregateTests
{
    [Fact]
    public void Order_Constructor_Should_Set_Default_Description()
    {
        // Arrange
        var userId = "test-user-123";
        var userName = "Test User";
        var address = new Address("123 Main St", "Test City", "Test State", "Test Country", "12345");

        // Act
        var order = new Order(userId, userName, address);

        // Assert
        order.Description.Should().NotBeNullOrEmpty();
        order.Description.Should().Be("Order submitted");
    }

    [Fact]
    public void Order_Constructor_Should_Set_All_Required_Properties()
    {
        // Arrange
        var userId = "test-user-123";
        var userName = "Test User";
        var address = new Address("123 Main St", "Test City", "Test State", "Test Country", "12345");

        // Act
        var order = new Order(userId, userName, address);

        // Assert
        order.OrderStatus.Should().Be(OrderStatus.Submitted);
        order.OrderDate.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        order.Address.Should().Be(address);
        order.Description.Should().Be("Order submitted");
        order.BuyerId.Should().BeNull();
        order.PaymentId.Should().BeNull();
    }

    [Fact]
    public void Order_Constructor_Should_Add_OrderStartedDomainEvent()
    {
        // Arrange
        var userId = "test-user-123";
        var userName = "Test User";
        var address = new Address("123 Main St", "Test City", "Test State", "Test Country", "12345");

        // Act
        var order = new Order(userId, userName, address);

        // Assert
        order.DomainEvents.Should().HaveCount(1);
        order.DomainEvents.First().Should().BeOfType<OrderStartedDomainEvent>();
        
        var domainEvent = (OrderStartedDomainEvent)order.DomainEvents.First();
        domainEvent.Order.Should().Be(order);
        domainEvent.UserId.Should().Be(userId);
        domainEvent.UserName.Should().Be(userName);
    }

    [Fact]
    public void Order_NewDraft_Should_Set_Default_Description()
    {
        // Act
        var order = Order.NewDraft();

        // Assert
        order.Description.Should().NotBeNullOrEmpty();
        order.Description.Should().Be("Draft order");
    }

    [Fact]
    public void Order_NewDraft_Should_Not_Have_Domain_Events()
    {
        // Act
        var order = Order.NewDraft();

        // Assert
        order.DomainEvents.Should().BeNullOrEmpty();
    }

    [Fact]
    public void Order_AddOrderItem_Should_Add_Item_To_Collection()
    {
        // Arrange
        var order = Order.NewDraft();
        var productId = 1;
        var productName = "Test Product";
        var unitPrice = 10.50m;
        var discount = 0m;
        string? pictureUrl = "test.jpg";
        var units = 2;

        // Act
        order.AddOrderItem(productId, productName, unitPrice, discount, pictureUrl, units);

        // Assert
        order.OrderItems.Should().HaveCount(1);
        var orderItem = order.OrderItems.First();
        orderItem.ProductId.Should().Be(productId);
        orderItem.ProductName.Should().Be(productName);
        orderItem.UnitPrice.Should().Be(unitPrice);
        orderItem.Units.Should().Be(units);
    }

    [Fact]
    public void Order_SetShippedStatus_Should_Update_Description()
    {
        // Arrange
        var userId = "test-user-123";
        var userName = "Test User";
        var address = new Address("123 Main St", "Test City", "Test State", "Test Country", "12345");
        var order = new Order(userId, userName, address);

        // Set order through the proper status transitions (required for shipping)
        order.SetAwaitingValidationStatus();
        order.SetStockConfirmedStatus();
        order.SetPaidStatus();

        // Act
        order.SetShippedStatus();

        // Assert
        order.OrderStatus.Should().Be(OrderStatus.Shipped);
        order.Description.Should().Be("The order was shipped.");
    }

    [Fact]
    public void Order_SetCancelledStatus_Should_Update_Description()
    {
        // Arrange
        var userId = "test-user-123";
        var userName = "Test User";
        var address = new Address("123 Main St", "Test City", "Test State", "Test Country", "12345");
        var order = new Order(userId, userName, address);

        // Act
        order.SetCancelledStatus();

        // Assert
        order.OrderStatus.Should().Be(OrderStatus.Cancelled);
        order.Description.Should().Be("The order was cancelled.");
    }

    [Fact]
    public void Order_SetStockConfirmedStatus_Should_Update_Description()
    {
        // Arrange
        var userId = "test-user-123";
        var userName = "Test User";
        var address = new Address("123 Main St", "Test City", "Test State", "Test Country", "12345");
        var order = new Order(userId, userName, address);

        // Set to awaiting validation first (required transition)
        order.SetAwaitingValidationStatus();

        // Act
        order.SetStockConfirmedStatus();

        // Assert
        order.OrderStatus.Should().Be(OrderStatus.StockConfirmed);
        order.Description.Should().Be("All the items were confirmed with available stock.");
    }

    [Fact]
    public void Order_SetPaidStatus_Should_Update_Description()
    {
        // Arrange
        var userId = "test-user-123";
        var userName = "Test User";
        var address = new Address("123 Main St", "Test City", "Test State", "Test Country", "12345");
        var order = new Order(userId, userName, address);

        // Set to stock confirmed first (required transition)
        order.SetAwaitingValidationStatus();
        order.SetStockConfirmedStatus();

        // Act
        order.SetPaidStatus();

        // Assert
        order.OrderStatus.Should().Be(OrderStatus.Paid);
        order.Description.Should().Contain("payment was performed");
    }
}
