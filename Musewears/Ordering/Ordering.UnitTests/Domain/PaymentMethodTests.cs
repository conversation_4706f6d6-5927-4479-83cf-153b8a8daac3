using FluentAssertions;
using Ordering.Domain.AggregatesModel.BuyerAggregate;
using Ordering.Domain.Exceptions;
using Xunit;

namespace Ordering.UnitTests.Domain;

public class PaymentMethodTests
{
    [Fact]
    public void PaymentMethod_Constructor_With_Card_Details_Should_Set_Properties_Correctly()
    {
        // Arrange
        var cardTypeId = 1;
        var alias = "My Credit Card";
        var cardNumber = "1234567890123456";
        var securityNumber = "123";
        var cardHolderName = "John Doe";
        var expiration = DateTime.UtcNow.AddYears(2);

        // Act
        var paymentMethod = new PaymentMethod(cardTypeId, alias, cardNumber, securityNumber, cardHolderName, expiration);

        // Assert
        paymentMethod.PaymentProvider.Should().Be("Card");
        paymentMethod.PaymentReference.Should().BeNull();
    }

    [Fact]
    public void PaymentMethod_Constructor_With_Card_Details_Should_Throw_When_CardNumber_Empty()
    {
        // Arrange
        var cardTypeId = 1;
        var alias = "My Credit Card";
        var cardNumber = "";
        var securityNumber = "123";
        var cardHolderName = "John Doe";
        var expiration = DateTime.UtcNow.AddYears(2);

        // Act & Assert
        var action = () => new PaymentMethod(cardTypeId, alias, cardNumber, securityNumber, cardHolderName, expiration);
        action.Should().Throw<OrderingDomainException>();
    }

    [Fact]
    public void PaymentMethod_Constructor_With_Card_Details_Should_Throw_When_SecurityNumber_Empty()
    {
        // Arrange
        var cardTypeId = 1;
        var alias = "My Credit Card";
        var cardNumber = "1234567890123456";
        var securityNumber = "";
        var cardHolderName = "John Doe";
        var expiration = DateTime.UtcNow.AddYears(2);

        // Act & Assert
        var action = () => new PaymentMethod(cardTypeId, alias, cardNumber, securityNumber, cardHolderName, expiration);
        action.Should().Throw<OrderingDomainException>();
    }

    [Fact]
    public void PaymentMethod_Constructor_With_Card_Details_Should_Throw_When_CardHolderName_Empty()
    {
        // Arrange
        var cardTypeId = 1;
        var alias = "My Credit Card";
        var cardNumber = "1234567890123456";
        var securityNumber = "123";
        var cardHolderName = "";
        var expiration = DateTime.UtcNow.AddYears(2);

        // Act & Assert
        var action = () => new PaymentMethod(cardTypeId, alias, cardNumber, securityNumber, cardHolderName, expiration);
        action.Should().Throw<OrderingDomainException>();
    }

    [Fact]
    public void PaymentMethod_Constructor_With_Card_Details_Should_Throw_When_Expiration_In_Past()
    {
        // Arrange
        var cardTypeId = 1;
        var alias = "My Credit Card";
        var cardNumber = "1234567890123456";
        var securityNumber = "123";
        var cardHolderName = "John Doe";
        var expiration = DateTime.UtcNow.AddYears(-1);

        // Act & Assert
        var action = () => new PaymentMethod(cardTypeId, alias, cardNumber, securityNumber, cardHolderName, expiration);
        action.Should().Throw<OrderingDomainException>();
    }

    [Fact]
    public void PaymentMethod_Constructor_With_Interswitch_Details_Should_Set_Properties_Correctly()
    {
        // Arrange
        var alias = "Interswitch Payment";
        var paymentReference = "PAY123456789";
        var cardHolderName = "Jane Smith";

        // Act
        var paymentMethod = new PaymentMethod(alias, paymentReference, cardHolderName);

        // Assert
        paymentMethod.PaymentProvider.Should().Be("Interswitch");
        paymentMethod.PaymentReference.Should().Be(paymentReference);
    }

    [Fact]
    public void PaymentMethod_Constructor_With_Interswitch_Details_Should_Throw_When_Alias_Empty()
    {
        // Arrange
        var alias = "";
        var paymentReference = "PAY123456789";
        var cardHolderName = "Jane Smith";

        // Act & Assert
        var action = () => new PaymentMethod(alias, paymentReference, cardHolderName);
        action.Should().Throw<OrderingDomainException>();
    }

    [Fact]
    public void PaymentMethod_Constructor_With_Interswitch_Details_Should_Throw_When_PaymentReference_Empty()
    {
        // Arrange
        var alias = "Interswitch Payment";
        var paymentReference = "";
        var cardHolderName = "Jane Smith";

        // Act & Assert
        var action = () => new PaymentMethod(alias, paymentReference, cardHolderName);
        action.Should().Throw<OrderingDomainException>();
    }

    [Fact]
    public void PaymentMethod_Constructor_With_Interswitch_Details_Should_Throw_When_CardHolderName_Empty()
    {
        // Arrange
        var alias = "Interswitch Payment";
        var paymentReference = "PAY123456789";
        var cardHolderName = "";

        // Act & Assert
        var action = () => new PaymentMethod(alias, paymentReference, cardHolderName);
        action.Should().Throw<OrderingDomainException>();
    }

    [Fact]
    public void IsEqualTo_Should_Return_True_When_Card_Details_Match()
    {
        // Arrange
        var cardTypeId = 1;
        var alias = "My Credit Card";
        var cardNumber = "1234567890123456";
        var securityNumber = "123";
        var cardHolderName = "John Doe";
        var expiration = DateTime.UtcNow.AddYears(2);

        var paymentMethod = new PaymentMethod(cardTypeId, alias, cardNumber, securityNumber, cardHolderName, expiration);

        // Act
        var result = paymentMethod.IsEqualTo(cardTypeId, cardNumber, expiration);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void IsEqualTo_Should_Return_False_When_Card_Details_Dont_Match()
    {
        // Arrange
        var cardTypeId = 1;
        var alias = "My Credit Card";
        var cardNumber = "1234567890123456";
        var securityNumber = "123";
        var cardHolderName = "John Doe";
        var expiration = DateTime.UtcNow.AddYears(2);

        var paymentMethod = new PaymentMethod(cardTypeId, alias, cardNumber, securityNumber, cardHolderName, expiration);

        // Act
        var result = paymentMethod.IsEqualTo(2, cardNumber, expiration); // Different card type

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void IsEqualToInterswitch_Should_Return_True_When_PaymentReference_Matches()
    {
        // Arrange
        var alias = "Interswitch Payment";
        var paymentReference = "PAY123456789";
        var cardHolderName = "Jane Smith";

        var paymentMethod = new PaymentMethod(alias, paymentReference, cardHolderName);

        // Act
        var result = paymentMethod.IsEqualToInterswitch(paymentReference);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void IsEqualToInterswitch_Should_Return_False_When_PaymentReference_Doesnt_Match()
    {
        // Arrange
        var alias = "Interswitch Payment";
        var paymentReference = "PAY123456789";
        var cardHolderName = "Jane Smith";

        var paymentMethod = new PaymentMethod(alias, paymentReference, cardHolderName);

        // Act
        var result = paymentMethod.IsEqualToInterswitch("PAY987654321");

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void IsEqualToInterswitch_Should_Return_False_When_PaymentMethod_Is_Card_Based()
    {
        // Arrange
        var cardTypeId = 1;
        var alias = "My Credit Card";
        var cardNumber = "1234567890123456";
        var securityNumber = "123";
        var cardHolderName = "John Doe";
        var expiration = DateTime.UtcNow.AddYears(2);

        var paymentMethod = new PaymentMethod(cardTypeId, alias, cardNumber, securityNumber, cardHolderName, expiration);

        // Act
        var result = paymentMethod.IsEqualToInterswitch("PAY123456789");

        // Assert
        result.Should().BeFalse();
    }
}
