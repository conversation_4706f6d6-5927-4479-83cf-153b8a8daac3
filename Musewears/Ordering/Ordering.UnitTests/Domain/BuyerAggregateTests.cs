using FluentAssertions;
using Ordering.Domain.AggregatesModel.BuyerAggregate;
using Ordering.Domain.Events;
using Xunit;

namespace Ordering.UnitTests.Domain;

public class BuyerAggregateTests
{
    [Fact]
    public void Buyer_Constructor_Should_Set_Properties_Correctly()
    {
        // Arrange
        var identity = "user123";
        var name = "<PERSON>";

        // Act
        var buyer = new Buyer(identity, name);

        // Assert
        buyer.IdentityGuid.Should().Be(identity);
        buyer.Name.Should().Be(name);
        buyer.PaymentMethods.Should().BeEmpty();
    }

    [Fact]
    public void Buyer_Constructor_Should_Throw_When_Identity_Is_Null()
    {
        // Arrange
        string? identity = null;
        var name = "<PERSON>";

        // Act & Assert
        var action = () => new Buyer(identity!, name);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void Buyer_Constructor_Should_Throw_When_Name_Is_Null()
    {
        // Arrange
        var identity = "user123";
        string? name = null;

        // Act & Assert
        var action = () => new Buyer(identity, name!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void VerifyOrAddPaymentMethod_Should_Add_New_Card_Payment_Method()
    {
        // Arrange
        var buyer = new Buyer("user123", "John Doe");
        var cardTypeId = 1;
        var alias = "My Credit Card";
        var cardNumber = "1234567890123456";
        var securityNumber = "123";
        var cardHolderName = "John Doe";
        var expiration = DateTime.UtcNow.AddYears(2);
        var orderId = 42;

        // Act
        var paymentMethod = buyer.VerifyOrAddPaymentMethod(cardTypeId, alias, cardNumber, securityNumber, cardHolderName, expiration, orderId);

        // Assert
        paymentMethod.Should().NotBeNull();
        buyer.PaymentMethods.Should().HaveCount(1);
        buyer.PaymentMethods.Should().Contain(paymentMethod);
        
        // Verify domain event was added
        buyer.DomainEvents.Should().HaveCount(1);
        buyer.DomainEvents.First().Should().BeOfType<BuyerAndPaymentMethodVerifiedDomainEvent>();
        
        var domainEvent = (BuyerAndPaymentMethodVerifiedDomainEvent)buyer.DomainEvents.First();
        domainEvent.Buyer.Should().Be(buyer);
        domainEvent.Payment.Should().Be(paymentMethod);
        domainEvent.OrderId.Should().Be(orderId);
    }

    [Fact]
    public void VerifyOrAddPaymentMethod_Should_Return_Existing_Card_Payment_Method()
    {
        // Arrange
        var buyer = new Buyer("user123", "John Doe");
        var cardTypeId = 1;
        var alias = "My Credit Card";
        var cardNumber = "1234567890123456";
        var securityNumber = "123";
        var cardHolderName = "John Doe";
        var expiration = DateTime.UtcNow.AddYears(2);
        var orderId = 42;

        // Add payment method first time
        var firstPaymentMethod = buyer.VerifyOrAddPaymentMethod(cardTypeId, alias, cardNumber, securityNumber, cardHolderName, expiration, orderId);
        buyer.ClearDomainEvents(); // Clear events from first addition

        // Act - Try to add same payment method again
        var secondPaymentMethod = buyer.VerifyOrAddPaymentMethod(cardTypeId, alias, cardNumber, securityNumber, cardHolderName, expiration, orderId);

        // Assert
        secondPaymentMethod.Should().Be(firstPaymentMethod);
        buyer.PaymentMethods.Should().HaveCount(1); // Should still be only one
        
        // Verify domain event was still added for verification
        buyer.DomainEvents.Should().HaveCount(1);
        buyer.DomainEvents.First().Should().BeOfType<BuyerAndPaymentMethodVerifiedDomainEvent>();
    }

    [Fact]
    public void VerifyOrAddInterswitchPaymentMethod_Should_Add_New_Interswitch_Payment_Method()
    {
        // Arrange
        var buyer = new Buyer("user123", "Jane Smith");
        var alias = "Interswitch Payment";
        var paymentReference = "PAY123456789";
        var cardHolderName = "Jane Smith";
        var orderId = 123;

        // Act
        var paymentMethod = buyer.VerifyOrAddInterSwitchPaymentMethod(alias, paymentReference, cardHolderName, orderId);

        // Assert
        paymentMethod.Should().NotBeNull();
        paymentMethod.PaymentProvider.Should().Be("Interswitch");
        paymentMethod.PaymentReference.Should().Be(paymentReference);
        
        buyer.PaymentMethods.Should().HaveCount(1);
        buyer.PaymentMethods.Should().Contain(paymentMethod);
        
        // Verify domain event was added
        buyer.DomainEvents.Should().HaveCount(1);
        buyer.DomainEvents.First().Should().BeOfType<BuyerAndPaymentMethodVerifiedDomainEvent>();
        
        var domainEvent = (BuyerAndPaymentMethodVerifiedDomainEvent)buyer.DomainEvents.First();
        domainEvent.Buyer.Should().Be(buyer);
        domainEvent.Payment.Should().Be(paymentMethod);
        domainEvent.OrderId.Should().Be(orderId);
    }

    [Fact]
    public void VerifyOrAddInterswitchPaymentMethod_Should_Return_Existing_Interswitch_Payment_Method()
    {
        // Arrange
        var buyer = new Buyer("user123", "Jane Smith");
        var alias = "Interswitch Payment";
        var paymentReference = "PAY123456789";
        var cardHolderName = "Jane Smith";
        var orderId = 123;

        // Add payment method first time
        var firstPaymentMethod = buyer.VerifyOrAddInterSwitchPaymentMethod(alias, paymentReference, cardHolderName, orderId);
        buyer.ClearDomainEvents(); // Clear events from first addition

        // Act - Try to add same payment method again
        var secondPaymentMethod = buyer.VerifyOrAddInterSwitchPaymentMethod(alias, paymentReference, cardHolderName, orderId);

        // Assert
        secondPaymentMethod.Should().Be(firstPaymentMethod);
        buyer.PaymentMethods.Should().HaveCount(1); // Should still be only one
        
        // Verify domain event was still added for verification
        buyer.DomainEvents.Should().HaveCount(1);
        buyer.DomainEvents.First().Should().BeOfType<BuyerAndPaymentMethodVerifiedDomainEvent>();
    }

    [Fact]
    public void Buyer_Should_Support_Both_Card_And_Interswitch_Payment_Methods()
    {
        // Arrange
        var buyer = new Buyer("user123", "John Doe");
        
        // Card payment method
        var cardTypeId = 1;
        var cardAlias = "My Credit Card";
        var cardNumber = "1234567890123456";
        var securityNumber = "123";
        var cardHolderName = "John Doe";
        var expiration = DateTime.UtcNow.AddYears(2);
        
        // Interswitch payment method
        var interswitchAlias = "Interswitch Payment";
        var paymentReference = "PAY123456789";
        var orderId = 42;

        // Act
        var cardPaymentMethod = buyer.VerifyOrAddPaymentMethod(cardTypeId, cardAlias, cardNumber, securityNumber, cardHolderName, expiration, orderId);
        var interswitchPaymentMethod = buyer.VerifyOrAddInterSwitchPaymentMethod(interswitchAlias, paymentReference, cardHolderName, orderId);

        // Assert
        buyer.PaymentMethods.Should().HaveCount(2);
        buyer.PaymentMethods.Should().Contain(cardPaymentMethod);
        buyer.PaymentMethods.Should().Contain(interswitchPaymentMethod);
        
        cardPaymentMethod.PaymentProvider.Should().Be("Card");
        interswitchPaymentMethod.PaymentProvider.Should().Be("Interswitch");
        
        // Verify both domain events were added
        buyer.DomainEvents.Should().HaveCount(2);
        buyer.DomainEvents.Should().AllBeOfType<BuyerAndPaymentMethodVerifiedDomainEvent>();
    }

    [Fact]
    public void VerifyOrAddInterswitchPaymentMethod_Should_Not_Conflict_With_Card_Payment_Methods()
    {
        // Arrange
        var buyer = new Buyer("user123", "John Doe");
        
        // Add a card payment method first
        var cardTypeId = 1;
        var cardAlias = "My Credit Card";
        var cardNumber = "1234567890123456";
        var securityNumber = "123";
        var cardHolderName = "John Doe";
        var expiration = DateTime.UtcNow.AddYears(2);
        var orderId = 42;
        
        buyer.VerifyOrAddPaymentMethod(cardTypeId, cardAlias, cardNumber, securityNumber, cardHolderName, expiration, orderId);
        
        // Now add an Interswitch payment method
        var interswitchAlias = "Interswitch Payment";
        var paymentReference = "PAY123456789";

        // Act
        var interswitchPaymentMethod = buyer.VerifyOrAddInterSwitchPaymentMethod(interswitchAlias, paymentReference, cardHolderName, orderId);

        // Assert
        interswitchPaymentMethod.Should().NotBeNull();
        buyer.PaymentMethods.Should().HaveCount(2);
        
        // Verify the Interswitch payment method doesn't match the card payment method
        var cardPaymentMethod = buyer.PaymentMethods.First(pm => pm.PaymentProvider == "Card");
        cardPaymentMethod.IsEqualToInterswitch(paymentReference).Should().BeFalse();
        interswitchPaymentMethod.IsEqualTo(cardTypeId, cardNumber, expiration).Should().BeFalse();
    }
}
