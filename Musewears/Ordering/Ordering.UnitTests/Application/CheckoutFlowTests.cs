using MediatR;
using Microsoft.Extensions.Logging;
using Moq;
using Ordering.API.Application.Commands;
using Ordering.API.Application.Models;
using Ordering.API.Application.Services;
using Ordering.Domain.AggregatesModel.OrderAggregate;
using Ordering.Domain.SeedWork;
using Xunit;

namespace Ordering.UnitTests.Application;

/// <summary>
/// Tests for the new checkout flow that handles race conditions
/// </summary>
public class CheckoutFlowTests
{
    private readonly Mock<IOrderRepository> _orderRepositoryMock;
    private readonly Mock<ILogger<CreateOrderCommandHandler>> _loggerMock;
    private readonly Mock<ILogger<ProcessInterswitchPaymentCommandHandler>> _paymentLoggerMock;
    private readonly Mock<IUnitOfWork> _unitOfWorkMock;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly Mock<IIdentityService> _identityServiceMock;

    public CheckoutFlowTests()
    {
        _orderRepositoryMock = new Mock<IOrderRepository>();
        _loggerMock = new Mock<ILogger<CreateOrderCommandHandler>>();
        _paymentLoggerMock = new Mock<ILogger<ProcessInterswitchPaymentCommandHandler>>();
        _unitOfWorkMock = new Mock<IUnitOfWork>();
        _mediatorMock = new Mock<IMediator>();
        _identityServiceMock = new Mock<IIdentityService>();

        _orderRepositoryMock.Setup(x => x.UnitOfWork).Returns(_unitOfWorkMock.Object);
    }

    [Fact]
    public async Task CreateOrderCommand_WithNewMerchantReference_ShouldCreateOrder()
    {
        // Arrange
        var merchantReference = Guid.NewGuid().ToString();
        var basketItems = new List<BasketItem>
        {
            new BasketItem { Id = "1", ProductId = 1, ProductName = "Test Product", UnitPrice = 10.00m, OldUnitPrice = 0, Quantity = 1, PictureUrl = "test.jpg" }
        };

        var command = new CreateOrderCommand(
            basketItems: basketItems,
            userId: "user123",
            userName: "Test User",
            city: "Test City",
            street: "Test Street",
            state: "Test State",
            country: "Test Country",
            zipcode: "12345",
            paymentReference: null,
            merchantReference: merchantReference);

        _orderRepositoryMock.Setup(x => x.GetByMerchantReferenceAsync(merchantReference))
            .ReturnsAsync((Order?)null);
        _unitOfWorkMock.Setup(x => x.SaveEntitiesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var handler = new CreateOrderCommandHandler(_mediatorMock.Object, _orderRepositoryMock.Object, _identityServiceMock.Object, _loggerMock.Object);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result);
        _orderRepositoryMock.Verify(x => x.Add(It.IsAny<Order>()), Times.Once);
        _unitOfWorkMock.Verify(x => x.SaveEntitiesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task CreateOrderCommand_WithExistingMerchantReference_ShouldReturnTrueWithoutCreating()
    {
        // Arrange
        var merchantReference = Guid.NewGuid().ToString();
        var existingOrder = CreateTestOrder();
        var basketItems = new List<BasketItem>
        {
            new BasketItem { Id = "1", ProductId = 1, ProductName = "Test Product", UnitPrice = 10.00m, OldUnitPrice = 0, Quantity = 1, PictureUrl = "test.jpg" }
        };

        var command = new CreateOrderCommand(
            basketItems: basketItems,
            userId: "user123",
            userName: "Test User",
            city: "Test City",
            street: "Test Street",
            state: "Test State",
            country: "Test Country",
            zipcode: "12345",
            paymentReference: null,
            merchantReference: merchantReference);

        _orderRepositoryMock.Setup(x => x.GetByMerchantReferenceAsync(merchantReference))
            .ReturnsAsync(existingOrder);

        var handler = new CreateOrderCommandHandler(_mediatorMock.Object, _orderRepositoryMock.Object, _identityServiceMock.Object, _loggerMock.Object);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result);
        _orderRepositoryMock.Verify(x => x.Add(It.IsAny<Order>()), Times.Never);
        _unitOfWorkMock.Verify(x => x.SaveEntitiesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ProcessInterswitchPaymentCommand_WithRetryLogic_ShouldEventuallyFindOrder()
    {
        // Arrange
        var paymentReference = "PAY123";
        var order = CreateTestOrder();
        
        _orderRepositoryMock.SetupSequence(x => x.GetByMerchantReferenceAsync(paymentReference))
            .ReturnsAsync((Order?)null) // First attempt fails
            .ReturnsAsync((Order?)null) // Second attempt fails
            .ReturnsAsync(order);        // Third attempt succeeds

        _unitOfWorkMock.Setup(x => x.SaveEntitiesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var command = new ProcessInterswitchPaymentCommand(
            MerchantReference: paymentReference, // Use paymentReference as MerchantReference for test
            UserId: "user123",
            UserName: "Test User",
            PaymentStatus: "completed",
            PaymentId: 123,
            MerchantCustomerId: "cust123",
            MerchantCustomerName: "Customer Name",
            ResponseCode: "00",
            PaymentReference: paymentReference);

        var handler = new ProcessInterswitchPaymentCommandHandler(_orderRepositoryMock.Object, _paymentLoggerMock.Object);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result);
        _orderRepositoryMock.Verify(x => x.GetByMerchantReferenceAsync(paymentReference), Times.Exactly(3));
        _unitOfWorkMock.Verify(x => x.SaveEntitiesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    private static Order CreateTestOrder()
    {
        var address = new Address("Test Street", "Test City", "Test State", "Test Country", "12345");
        var order = new Order("user123", "Test User", address);
        return order;
    }
}
