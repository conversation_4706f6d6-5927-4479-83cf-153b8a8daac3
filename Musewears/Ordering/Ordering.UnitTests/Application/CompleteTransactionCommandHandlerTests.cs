using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Musewears.Shared.Models;
using Ordering.API.Application.Commands;
using Ordering.API.Application.Services;
using Ordering.Domain.AggregatesModel.TransactionAggregate;
using Ordering.Domain.SeedWork;
using Xunit;

namespace Ordering.UnitTests.Application;

public class CompleteTransactionCommandHandlerTests
{
    private readonly Mock<ITransactionProcessingService> _mockTransactionProcessingService;
    private readonly Mock<ILogger<CompleteTransactionCommandHandler>> _mockLogger;
    private readonly CompleteTransactionCommandHandler _handler;

    public CompleteTransactionCommandHandlerTests()
    {
        _mockTransactionProcessingService = new Mock<ITransactionProcessingService>();
        _mockLogger = new Mock<ILogger<CompleteTransactionCommandHandler>>();

        _handler = new CompleteTransactionCommandHandler(_mockTransactionProcessingService.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task Handle_Should_Complete_Existing_Transaction_Successfully()
    {
        // Arrange
        var paymentId = 123456;
        var transactionEvent = CreateTransactionEvent(paymentId, "TRANSACTION.COMPLETED");
        var command = new CompleteTransactionCommand(transactionEvent);

        _mockTransactionProcessingService.Setup(x => x.ProcessTransactionWebhookAsync(transactionEvent, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeTrue();
        _mockTransactionProcessingService.Verify(x => x.ProcessTransactionWebhookAsync(transactionEvent, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Create_And_Complete_Transaction_When_Not_Found()
    {
        // Arrange
        var paymentId = 123456;
        var transactionEvent = CreateTransactionEvent(paymentId, "TRANSACTION.COMPLETED");
        var command = new CompleteTransactionCommand(transactionEvent);

        _mockTransactionProcessingService.Setup(x => x.ProcessTransactionWebhookAsync(transactionEvent, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeTrue();
        _mockTransactionProcessingService.Verify(x => x.ProcessTransactionWebhookAsync(transactionEvent, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Return_False_When_Save_Fails()
    {
        // Arrange
        var paymentId = 123456;
        var transactionEvent = CreateTransactionEvent(paymentId, "TRANSACTION.COMPLETED");
        var command = new CompleteTransactionCommand(transactionEvent);

        _mockTransactionProcessingService.Setup(x => x.ProcessTransactionWebhookAsync(transactionEvent, It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeFalse();
        _mockTransactionProcessingService.Verify(x => x.ProcessTransactionWebhookAsync(transactionEvent, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Update_Response_Code_Before_Completion()
    {
        // Arrange
        var paymentId = 123456;
        var transactionEvent = CreateTransactionEvent(paymentId, "TRANSACTION.COMPLETED", "00", "Successful");
        var command = new CompleteTransactionCommand(transactionEvent);

        _mockTransactionProcessingService.Setup(x => x.ProcessTransactionWebhookAsync(transactionEvent, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeTrue();
        _mockTransactionProcessingService.Verify(x => x.ProcessTransactionWebhookAsync(transactionEvent, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Create_Transaction_With_Null_Customer_Info()
    {
        // Arrange
        var paymentId = 123456;
        var transactionEvent = CreateTransactionEventWithNullCustomerInfo(paymentId, "TRANSACTION.COMPLETED");
        var command = new CompleteTransactionCommand(transactionEvent);

        _mockTransactionProcessingService.Setup(x => x.ProcessTransactionWebhookAsync(transactionEvent, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeTrue();
        _mockTransactionProcessingService.Verify(x => x.ProcessTransactionWebhookAsync(transactionEvent, It.IsAny<CancellationToken>()), Times.Once);
    }

    private static TransactionEvent CreateTransactionEvent(int paymentId, string eventType, string? responseCode = null, string? responseDescription = null)
    {
        return new TransactionEvent
        {
            Event = eventType,
            Uuid = Guid.NewGuid().ToString(),
            Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
            Data = new TransactionEventData
            {
                PaymentId = paymentId,
                PaymentReference = $"PAY_{paymentId}",
                MerchantReference = $"MERCH_{paymentId}",
                Amount = 10000,
                RemittanceAmount = 9500,
                ResponseCode = responseCode ?? "00",
                ResponseDescription = responseDescription ?? "Pending",
                CardNumber = "1234****5678",
                RetrievalReferenceNumber = "REF123456",
                TransactionDate = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                AccountNumber = "**********",
                BankCode = "011",
                Token = "token123",
                CurrencyCode = "NGN",
                Channel = "WEB",
                MerchantCustomerId = "customer123",
                MerchantCustomerName = "Test Customer",
                Escrow = false,
                NonCardProviderId = null,
                PayableCode = "PAY001",
                UserId = "user123",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };
    }

    private static TransactionEvent CreateTransactionEventWithNullCustomerInfo(int paymentId, string eventType, string? responseCode = null, string? responseDescription = null)
    {
        return new TransactionEvent
        {
            Event = eventType,
            Uuid = Guid.NewGuid().ToString(),
            Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
            Data = new TransactionEventData
            {
                PaymentId = paymentId,
                PaymentReference = $"PAY_{paymentId}",
                MerchantReference = $"MERCH_{paymentId}",
                Amount = 10000,
                RemittanceAmount = 9500,
                ResponseCode = responseCode ?? "00",
                ResponseDescription = responseDescription ?? "Pending",
                CardNumber = "1234****5678",
                RetrievalReferenceNumber = "REF123456",
                TransactionDate = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                AccountNumber = "**********",
                BankCode = "011",
                Token = "token123",
                CurrencyCode = "NGN",
                Channel = "WEB",
                MerchantCustomerId = null, // Null customer info
                MerchantCustomerName = null, // Null customer info
                Escrow = false,
                NonCardProviderId = null,
                PayableCode = "PAY001",
                UserId = null, // Null user info
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };
    }

    private static Transaction CreateTransaction(int paymentId)
    {
        return new Transaction(
            transactionReference: Guid.NewGuid().ToString(),
            merchantReference: $"MERCH_{paymentId}",
            paymentReference: $"PAY_{paymentId}",
            paymentId: paymentId,
            amount: 10000,
            remittanceAmount: 9500,
            responseCode: "00",
            responseDescription: "Pending",
            cardNumber: "1234****5678",
            retrievalReferenceNumber: "REF123456",
            transactionDate: DateTime.UtcNow,
            accountNumber: "**********",
            bankCode: "011",
            token: "token123",
            currencyCode: "NGN",
            channel: "WEB",
            merchantCustomerId: "customer123",
            merchantCustomerName: "Test Customer",
            escrow: false,
            nonCardProviderId: null,
            payableCode: "PAY001");
    }
}
