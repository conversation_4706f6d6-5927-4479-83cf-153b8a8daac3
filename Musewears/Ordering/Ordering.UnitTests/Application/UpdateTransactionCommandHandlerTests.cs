using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Musewears.Shared.Models;
using Ordering.API.Application.Commands;
using Ordering.API.Application.Services;
using Ordering.Domain.AggregatesModel.TransactionAggregate;
using Ordering.Domain.SeedWork;
using Xunit;

namespace Ordering.UnitTests.Application;

public class UpdateTransactionCommandHandlerTests
{
    private readonly Mock<ITransactionProcessingService> _mockTransactionProcessingService;
    private readonly Mock<ILogger<UpdateTransactionCommandHandler>> _mockLogger;
    private readonly UpdateTransactionCommandHandler _handler;

    public UpdateTransactionCommandHandlerTests()
    {
        _mockTransactionProcessingService = new Mock<ITransactionProcessingService>();
        _mockLogger = new Mock<ILogger<UpdateTransactionCommandHandler>>();

        _handler = new UpdateTransactionCommandHandler(_mockTransactionProcessingService.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task Handle_Should_Update_Existing_Transaction_Successfully()
    {
        // Arrange
        var paymentId = 123456;
        var transactionEvent = CreateTransactionEvent(paymentId, "TRANSACTION.UPDATED", "01", "Processing");
        var command = new UpdateTransactionCommand(transactionEvent);

        _mockTransactionProcessingService.Setup(x => x.ProcessTransactionWebhookAsync(transactionEvent, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeTrue();
        _mockTransactionProcessingService.Verify(x => x.ProcessTransactionWebhookAsync(transactionEvent, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Create_And_Update_Transaction_When_Not_Found()
    {
        // Arrange
        var paymentId = 123456;
        var transactionEvent = CreateTransactionEvent(paymentId, "TRANSACTION.UPDATED", "01", "Processing");
        var command = new UpdateTransactionCommand(transactionEvent);

        _mockTransactionProcessingService.Setup(x => x.ProcessTransactionWebhookAsync(transactionEvent, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeTrue();
        _mockTransactionProcessingService.Verify(x => x.ProcessTransactionWebhookAsync(transactionEvent, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Return_False_When_Save_Fails()
    {
        // Arrange
        var paymentId = 123456;
        var transactionEvent = CreateTransactionEvent(paymentId, "TRANSACTION.UPDATED");
        var command = new UpdateTransactionCommand(transactionEvent);

        _mockTransactionProcessingService.Setup(x => x.ProcessTransactionWebhookAsync(transactionEvent, It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeFalse();
        _mockTransactionProcessingService.Verify(x => x.ProcessTransactionWebhookAsync(transactionEvent, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Handle_Empty_Response_Code_And_Description()
    {
        // Arrange
        var paymentId = 123456;
        var transactionEvent = CreateTransactionEvent(paymentId, "TRANSACTION.UPDATED", "", "");
        var command = new UpdateTransactionCommand(transactionEvent);

        _mockTransactionProcessingService.Setup(x => x.ProcessTransactionWebhookAsync(transactionEvent, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeTrue();
        _mockTransactionProcessingService.Verify(x => x.ProcessTransactionWebhookAsync(transactionEvent, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Throw_Exception_When_Repository_Throws()
    {
        // Arrange
        var paymentId = 123456;
        var transactionEvent = CreateTransactionEvent(paymentId, "TRANSACTION.UPDATED");
        var command = new UpdateTransactionCommand(transactionEvent);

        _mockTransactionProcessingService.Setup(x => x.ProcessTransactionWebhookAsync(transactionEvent, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database error"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _handler.Handle(command, CancellationToken.None));
    }

    private static TransactionEvent CreateTransactionEvent(int paymentId, string eventType, string? responseCode = null, string? responseDescription = null)
    {
        return new TransactionEvent
        {
            Event = eventType,
            Uuid = Guid.NewGuid().ToString(),
            Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
            Data = new TransactionEventData
            {
                PaymentId = paymentId,
                PaymentReference = $"PAY_{paymentId}",
                MerchantReference = $"MERCH_{paymentId}",
                Amount = 10000,
                RemittanceAmount = 9500,
                ResponseCode = responseCode ?? "00",
                ResponseDescription = responseDescription ?? "Pending",
                CardNumber = "1234****5678",
                RetrievalReferenceNumber = "REF123456",
                TransactionDate = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                AccountNumber = "**********",
                BankCode = "011",
                Token = "token123",
                CurrencyCode = "NGN",
                Channel = "WEB",
                MerchantCustomerId = "customer123",
                MerchantCustomerName = "Test Customer",
                Escrow = false,
                NonCardProviderId = null,
                PayableCode = "PAY001",
                UserId = "user123",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };
    }

    private static Transaction CreateTransaction(int paymentId)
    {
        return new Transaction(
            transactionReference: Guid.NewGuid().ToString(),
            merchantReference: $"MERCH_{paymentId}",
            paymentReference: $"PAY_{paymentId}",
            paymentId: paymentId,
            amount: 10000,
            remittanceAmount: 9500,
            responseCode: "00",
            responseDescription: "Pending",
            cardNumber: "1234****5678",
            retrievalReferenceNumber: "REF123456",
            transactionDate: DateTime.UtcNow,
            accountNumber: "**********",
            bankCode: "011",
            token: "token123",
            currencyCode: "NGN",
            channel: "WEB",
            merchantCustomerId: "customer123",
            merchantCustomerName: "Test Customer",
            escrow: false,
            nonCardProviderId: null,
            payableCode: "PAY001");
    }
}
