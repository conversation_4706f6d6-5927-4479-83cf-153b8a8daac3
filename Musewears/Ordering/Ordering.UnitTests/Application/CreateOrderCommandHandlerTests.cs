using FluentAssertions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Ordering.API.Application.Commands;
using Ordering.API.Application.DomainEventHandlers;
using Ordering.API.Application.Models;
using Ordering.API.Application.Services;
using Ordering.Domain.AggregatesModel.BuyerAggregate;
using Ordering.Domain.AggregatesModel.OrderAggregate;
using Ordering.Domain.Events;
using Ordering.Infrastructure;
using Ordering.Infrastructure.Repositories;
using Xunit;

namespace Ordering.UnitTests.Application;

public class CreateOrderCommandHandlerTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly OrderingContext _context;

    public CreateOrderCommandHandlerTests()
    {
        var services = new ServiceCollection();
        
        // Add DbContext with in-memory database
        services.AddDbContext<OrderingContext>(options =>
            options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()));

        // Add MediatR
        services.AddMediatR(cfg =>
        {
            cfg.RegisterServicesFromAssemblyContaining<CreateOrderCommandHandler>();
        });

        // Add repositories as scoped services
        services.AddScoped<IBuyerRepository, BuyerRepository>();
        services.AddScoped<IOrderRepository, OrderRepository>();

        // Add logging
        services.AddLogging(builder => builder.AddConsole());

        // Add domain event handlers
        services.AddScoped<INotificationHandler<OrderStartedDomainEvent>, 
            ValidateOrAddBuyerAggregateWhenOrderStartedDomainEventHandler>();

        // Mock identity service
        var mockIdentityService = new Mock<IIdentityService>();
        services.AddSingleton(mockIdentityService.Object);

        _serviceProvider = services.BuildServiceProvider();
        _context = _serviceProvider.GetRequiredService<OrderingContext>();
    }

    [Fact]
    public async Task Handle_Should_Create_Order_With_Description_Set()
    {
        // Arrange
        using var scope = _serviceProvider.CreateScope();
        var handler = scope.ServiceProvider.GetRequiredService<IRequestHandler<CreateOrderCommand, bool>>();
        var context = scope.ServiceProvider.GetRequiredService<OrderingContext>();

        var command = new CreateOrderCommand(
            basketItems: new List<BasketItem>
            {
                new() { ProductId = 1, ProductName = "Test Product", UnitPrice = 10.50m, Quantity = 2, PictureUrl = "test.jpg" }
            },
            userId: "test-user-123",
            userName: "Test User",
            city: "Test City",
            street: "123 Main St",
            state: "Test State",
            country: "Test Country",
            zipcode: "12345"
        );

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeTrue();

        var order = await context.Orders.FirstOrDefaultAsync();
        order.Should().NotBeNull();
        order!.Description.Should().NotBeNullOrEmpty();
        order.Description.Should().Be("Order submitted");
        order.OrderItems.Should().HaveCount(1);
    }

    [Fact]
    public async Task Handle_Should_Create_Buyer_Through_Domain_Event()
    {
        // Arrange
        using var scope = _serviceProvider.CreateScope();
        var handler = scope.ServiceProvider.GetRequiredService<IRequestHandler<CreateOrderCommand, bool>>();
        var context = scope.ServiceProvider.GetRequiredService<OrderingContext>();

        var userId = "new-buyer-123";
        var userName = "New Buyer";

        var command = new CreateOrderCommand(
            basketItems: new List<BasketItem>
            {
                new() { ProductId = 1, ProductName = "Test Product", UnitPrice = 10.50m, Quantity = 1, PictureUrl = "test.jpg" }
            },
            userId: userId,
            userName: userName,
            city: "Test City",
            street: "123 Main St",
            state: "Test State",
            country: "Test Country",
            zipcode: "12345"
        );

        // Verify buyer doesn't exist initially
        var existingBuyer = await context.Buyers.FirstOrDefaultAsync(b => b.IdentityGuid == userId);
        existingBuyer.Should().BeNull();

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeTrue();

        // Verify buyer was created through domain event
        var createdBuyer = await context.Buyers.FirstOrDefaultAsync(b => b.IdentityGuid == userId);
        createdBuyer.Should().NotBeNull();
        createdBuyer!.Name.Should().Be(userName);
    }

    [Fact]
    public async Task Handle_Should_Process_Multiple_Order_Items()
    {
        // Arrange
        using var scope = _serviceProvider.CreateScope();
        var handler = scope.ServiceProvider.GetRequiredService<IRequestHandler<CreateOrderCommand, bool>>();
        var context = scope.ServiceProvider.GetRequiredService<OrderingContext>();

        var command = new CreateOrderCommand(
            basketItems: new List<BasketItem>
            {
                new() { ProductId = 1, ProductName = "Product 1", UnitPrice = 10.50m, Quantity = 2, PictureUrl = "test1.jpg" },
                new() { ProductId = 2, ProductName = "Product 2", UnitPrice = 25.00m, Quantity = 1, PictureUrl = "test2.jpg" },
                new() { ProductId = 3, ProductName = "Product 3", UnitPrice = 15.75m, Quantity = 3, PictureUrl = "test3.jpg" }
            },
            userId: "test-user-456",
            userName: "Test User",
            city: "Test City",
            street: "123 Main St",
            state: "Test State",
            country: "Test Country",
            zipcode: "12345"
        );

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeTrue();

        var order = await context.Orders
            .Include(o => o.OrderItems)
            .FirstOrDefaultAsync();
        
        order.Should().NotBeNull();
        order!.OrderItems.Should().HaveCount(3);
        order.Description.Should().Be("Order submitted");

        var orderItem1 = order.OrderItems.FirstOrDefault(oi => oi.ProductId == 1);
        orderItem1.Should().NotBeNull();
        orderItem1!.ProductName.Should().Be("Product 1");
        orderItem1.Units.Should().Be(2);

        var orderItem2 = order.OrderItems.FirstOrDefault(oi => oi.ProductId == 2);
        orderItem2.Should().NotBeNull();
        orderItem2!.ProductName.Should().Be("Product 2");
        orderItem2.Units.Should().Be(1);

        var orderItem3 = order.OrderItems.FirstOrDefault(oi => oi.ProductId == 3);
        orderItem3.Should().NotBeNull();
        orderItem3!.ProductName.Should().Be("Product 3");
        orderItem3.Units.Should().Be(3);
    }

    [Fact]
    public async Task Handle_Should_Set_Correct_Order_Properties()
    {
        // Arrange
        using var scope = _serviceProvider.CreateScope();
        var handler = scope.ServiceProvider.GetRequiredService<IRequestHandler<CreateOrderCommand, bool>>();
        var context = scope.ServiceProvider.GetRequiredService<OrderingContext>();

        var userId = "property-test-user";
        var userName = "Property Test User";
        var city = "Property City";
        var street = "456 Property St";
        var state = "Property State";
        var country = "Property Country";
        var zipCode = "54321";

        var command = new CreateOrderCommand(
            basketItems: new List<BasketItem>
            {
                new() { ProductId = 1, ProductName = "Test Product", UnitPrice = 10.50m, Quantity = 1, PictureUrl = "test.jpg" }
            },
            userId: userId,
            userName: userName,
            city: city,
            street: street,
            state: state,
            country: country,
            zipcode: zipCode
        );

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeTrue();

        var order = await context.Orders.FirstOrDefaultAsync();
        order.Should().NotBeNull();
        order!.OrderStatus.Should().Be(OrderStatus.Submitted);
        order.OrderDate.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
        order.Description.Should().Be("Order submitted");
        order.BuyerId.Should().BeNull(); // Initially null, set later by domain event handler
        order.PaymentId.Should().BeNull(); // Initially null, set later by domain event handler
        
        order.Address.Should().NotBeNull();
        order.Address.Street.Should().Be(street);
        order.Address.City.Should().Be(city);
        order.Address.State.Should().Be(state);
        order.Address.Country.Should().Be(country);
        order.Address.ZipCode.Should().Be(zipCode);
    }

    public void Dispose()
    {
        _context?.Dispose();
        _serviceProvider?.Dispose();
    }
}
