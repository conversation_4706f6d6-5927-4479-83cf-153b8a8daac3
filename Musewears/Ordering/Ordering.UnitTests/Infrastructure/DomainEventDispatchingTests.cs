using FluentAssertions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Ordering.API.Application.DomainEventHandlers;
using Ordering.Domain.AggregatesModel.BuyerAggregate;
using Ordering.Domain.AggregatesModel.OrderAggregate;
using Ordering.Domain.Events;
using Ordering.Infrastructure;
using Ordering.Infrastructure.Repositories;
using Xunit;
using Microsoft.EntityFrameworkCore.Infrastructure;

namespace Ordering.UnitTests.Infrastructure;

public class DomainEventDispatchingTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly OrderingContext _context;

    public DomainEventDispatchingTests()
    {
        var services = new ServiceCollection();
        
        // Add DbContext with in-memory database
        services.AddDbContext<OrderingContext>(options =>
            options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()));

        // Add MediatR
        services.AddMediatR(cfg =>
        {
            cfg.RegisterServicesFromAssemblyContaining<ValidateOrAddBuyerAggregateWhenOrderStartedDomainEventHandler>();
        });

        // Add repositories as scoped services (this is the key to testing the fix)
        services.AddScoped<IBuyerRepository, BuyerRepository>();
        services.AddScoped<IOrderRepository, OrderRepository>();

        // Add logging
        services.AddLogging(builder => builder.AddConsole());

        // Add domain event handler
        services.AddScoped<INotificationHandler<OrderStartedDomainEvent>, 
            ValidateOrAddBuyerAggregateWhenOrderStartedDomainEventHandler>();

        _serviceProvider = services.BuildServiceProvider();
        _context = _serviceProvider.GetRequiredService<OrderingContext>();
    }

    [Fact]
    public async Task OrderStartedDomainEvent_Should_Be_Handled_Without_DI_Issues()
    {
        // Arrange
        using var scope = _serviceProvider.CreateScope();
        var scopedContext = scope.ServiceProvider.GetRequiredService<OrderingContext>();
        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

        var userId = "test-user-123";
        var userName = "Test User";
        var address = new Address("123 Main St", "Test City", "Test State", "Test Country", "12345");
        var order = new Order(userId, userName, address);

        // Get the domain event that was added
        var domainEvent = order.DomainEvents.First() as OrderStartedDomainEvent;
        domainEvent.Should().NotBeNull();

        // Act & Assert - This should not throw an exception about resolving scoped services
        var action = async () => await mediator.Publish(domainEvent!);
        await action.Should().NotThrowAsync();
    }

    [Fact]
    public async Task OrderStartedDomainEvent_Should_Create_Buyer_When_Not_Exists()
    {
        // Arrange
        using var scope = _serviceProvider.CreateScope();
        var scopedContext = scope.ServiceProvider.GetRequiredService<OrderingContext>();
        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
        var buyerRepository = scope.ServiceProvider.GetRequiredService<IBuyerRepository>();

        var userId = "new-user-123";
        var userName = "New User";
        var address = new Address("123 Main St", "Test City", "Test State", "Test Country", "12345");
        var order = new Order(userId, userName, address);

        // Verify buyer doesn't exist initially
        var existingBuyer = await buyerRepository.FindAsync(userId);
        existingBuyer.Should().BeNull();

        // Get the domain event and publish it directly
        var domainEvent = order.DomainEvents.First() as OrderStartedDomainEvent;
        domainEvent.Should().NotBeNull();

        // Act
        await mediator.Publish(domainEvent!);

        // Assert
        var createdBuyer = await buyerRepository.FindAsync(userId);
        createdBuyer.Should().NotBeNull();
        createdBuyer!.IdentityGuid.Should().Be(userId);
        createdBuyer.Name.Should().Be(userName);
    }

    [Fact]
    public async Task OrderStartedDomainEvent_Should_Not_Duplicate_Existing_Buyer()
    {
        // Arrange
        using var scope = _serviceProvider.CreateScope();
        var scopedContext = scope.ServiceProvider.GetRequiredService<OrderingContext>();
        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
        var buyerRepository = scope.ServiceProvider.GetRequiredService<IBuyerRepository>();

        var userId = "existing-user-123";
        var userName = "Existing User";

        // Create existing buyer
        var existingBuyer = new Buyer(userId, userName);
        buyerRepository.Add(existingBuyer);
        await buyerRepository.UnitOfWork.SaveEntitiesAsync();

        var address = new Address("123 Main St", "Test City", "Test State", "Test Country", "12345");
        var order = new Order(userId, userName, address);

        // Get initial buyer count
        var initialBuyerCount = await scopedContext.Buyers.CountAsync();

        // Get the domain event and publish it directly
        var domainEvent = order.DomainEvents.First() as OrderStartedDomainEvent;
        domainEvent.Should().NotBeNull();

        // Act
        await mediator.Publish(domainEvent!);

        // Assert - Should not create a duplicate buyer
        var finalBuyerCount = await scopedContext.Buyers.CountAsync();
        finalBuyerCount.Should().Be(initialBuyerCount);

        var buyer = await buyerRepository.FindAsync(userId);
        buyer.Should().NotBeNull();
        buyer!.IdentityGuid.Should().Be(userId);
        buyer.Name.Should().Be(userName);
    }

    [Fact]
    public async Task Multiple_Domain_Events_Should_Be_Processed_Successfully()
    {
        // Arrange
        using var scope = _serviceProvider.CreateScope();
        var scopedContext = scope.ServiceProvider.GetRequiredService<OrderingContext>();
        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

        // Create multiple orders with different users
        var orders = new[]
        {
            new Order("user1", "User One", new Address("123 Main St", "City1", "State1", "Country1", "12345")),
            new Order("user2", "User Two", new Address("456 Oak St", "City2", "State2", "Country2", "67890")),
            new Order("user3", "User Three", new Address("789 Pine St", "City3", "State3", "Country3", "54321"))
        };

        // Collect all domain events
        var domainEvents = orders.SelectMany(o => o.DomainEvents).Cast<OrderStartedDomainEvent>().ToList();

        // Act - Publish each domain event
        foreach (var domainEvent in domainEvents)
        {
            await mediator.Publish(domainEvent);
        }

        // Assert - All buyers should be created
        var buyerCount = await scopedContext.Buyers.CountAsync();
        buyerCount.Should().Be(3);

        var buyer1 = await scopedContext.Buyers.FirstOrDefaultAsync(b => b.IdentityGuid == "user1");
        buyer1.Should().NotBeNull();
        buyer1!.Name.Should().Be("User One");

        var buyer2 = await scopedContext.Buyers.FirstOrDefaultAsync(b => b.IdentityGuid == "user2");
        buyer2.Should().NotBeNull();
        buyer2!.Name.Should().Be("User Two");

        var buyer3 = await scopedContext.Buyers.FirstOrDefaultAsync(b => b.IdentityGuid == "user3");
        buyer3.Should().NotBeNull();
        buyer3!.Name.Should().Be("User Three");
    }

    public void Dispose()
    {
        _context?.Dispose();
        _serviceProvider?.Dispose();
    }
}
