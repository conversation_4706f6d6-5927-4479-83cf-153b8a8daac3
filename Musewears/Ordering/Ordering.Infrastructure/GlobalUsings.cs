global using System.Data;
global using MediatR;
global using Microsoft.EntityFrameworkCore;
global using Microsoft.EntityFrameworkCore.Design;
global using Microsoft.EntityFrameworkCore.Metadata.Builders;
global using Microsoft.EntityFrameworkCore.Storage;
global using Ordering.Domain.AggregatesModel.BuyerAggregate;
global using Ordering.Domain.AggregatesModel.OrderAggregate;
global using Ordering.Domain.Exceptions;
global using Ordering.Domain.SeedWork;
global using Ordering.Infrastructure.EntityConfigurations;
global using Ordering.Infrastructure.Idempotency;
