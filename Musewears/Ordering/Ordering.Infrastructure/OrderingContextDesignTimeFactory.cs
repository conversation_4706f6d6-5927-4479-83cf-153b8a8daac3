using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace Ordering.Infrastructure;

public class OrderingContextDesignTimeFactory : IDesignTimeDbContextFactory<OrderingContext>
{
    public OrderingContext CreateDbContext(string[] args)
    {
        var optionsBuilder = new DbContextOptionsBuilder<OrderingContext>();
        
        // Use a default connection string for design time
        optionsBuilder.UseNpgsql("Host=localhost;port=5432;Database=ordering;Username=root;Password=********************************");
        
        // Use the constructor without IMediator for design time
        return new OrderingContext(optionsBuilder.Options);
    }
}
