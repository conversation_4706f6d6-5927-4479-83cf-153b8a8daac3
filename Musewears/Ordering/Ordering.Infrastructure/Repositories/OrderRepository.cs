using Ordering.Domain.SeedWork;

namespace Ordering.Infrastructure.Repositories;

public class OrderRepository(OrderingContext context) : IOrderRepository
{
    private readonly OrderingContext _context = context ?? throw new ArgumentNullException(nameof(context));

    public IUnitOfWork UnitOfWork => _context;

    public Order Add(Order order)
    {
        return _context.Orders.Add(order).Entity;

    }

    public async Task<Order?> GetAsync(int orderId)
    {
        var order = await _context.Orders.FindAsync(orderId);

        if (order is not null)
        {
            await _context.Entry(order)
                .Collection(i => i.OrderItems).LoadAsync();
        }

        return order;
    }

    public async Task<Order?> GetByPaymentReferenceAsync(string paymentReference)
    {
        var order = await _context.Orders
            .Where(o => o.PaymentReference == paymentReference)
            .FirstOrDefaultAsync();

        if (order is not null)
        {
            await _context.Entry(order)
                .Collection(i => i.OrderItems).LoadAsync();
        }

        return order;
    }

    public async Task<Order?> GetByMerchantReferenceAsync(string merchantReference)
    {
        var order = await _context.Orders
            .Where(o => o.MerchantReference == merchantReference)
            .FirstOrDefaultAsync();

        if (order is not null)
        {
            await _context.Entry(order)
                .Collection(i => i.OrderItems).LoadAsync();
        }

        return order;
    }

    public void Update(Order order)
    {
        _context.Entry(order).State = EntityState.Modified;
    }
}
