using Microsoft.EntityFrameworkCore;
using Ordering.Domain.AggregatesModel.TransactionAggregate;
using Ordering.Domain.SeedWork;

namespace Ordering.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for Transaction aggregate
/// </summary>
public class TransactionRepository : ITransactionRepository
{
    private readonly OrderingContext _context;

    public TransactionRepository(OrderingContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    public IUnitOfWork UnitOfWork => _context;

    public Transaction Add(Transaction transaction)
    {
        return _context.Transactions.Add(transaction).Entity;
    }

    public void Update(Transaction transaction)
    {
        _context.Entry(transaction).State = EntityState.Modified;
    }

    public async Task<Transaction?> GetAsync(int transactionId)
    {
        return await _context.Transactions.FindAsync(transactionId);
    }

    public async Task<Transaction?> GetByPaymentIdAsync(int paymentId)
    {
        return await _context.Transactions
            .Where(t => t.PaymentId == paymentId)
            .FirstOrDefaultAsync();
    }

    public async Task<Transaction?> GetByMerchantReferenceAsync(string merchantReference)
    {
        return await _context.Transactions
            .Where(t => t.MerchantReference == merchantReference)
            .FirstOrDefaultAsync();
    }

    public async Task<Transaction?> GetByPaymentReferenceAsync(string paymentReference)
    {
        return await _context.Transactions
            .Where(t => t.PaymentReference == paymentReference)
            .FirstOrDefaultAsync();
    }

    public async Task<IEnumerable<Transaction>> GetByOrderIdAsync(int orderId)
    {
        return await _context.Transactions
            .Where(t => t.OrderId == orderId)
            .ToListAsync();
    }
}
