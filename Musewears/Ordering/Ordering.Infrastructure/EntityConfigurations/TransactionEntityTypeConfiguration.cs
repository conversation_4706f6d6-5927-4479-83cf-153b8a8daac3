using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Ordering.Domain.AggregatesModel.TransactionAggregate;

namespace Ordering.Infrastructure.EntityConfigurations;

/// <summary>
/// Entity Framework configuration for Transaction aggregate
/// </summary>
public class TransactionEntityTypeConfiguration : IEntityTypeConfiguration<Transaction>
{
    public void Configure(EntityTypeBuilder<Transaction> transactionConfiguration)
    {
        transactionConfiguration.ToTable("Transactions", "ordering");

        transactionConfiguration.HasKey(t => t.Id);

        transactionConfiguration.Ignore(t => t.DomainEvents);

        // Configure properties
        transactionConfiguration.Property(t => t.TransactionReference)
            .HasMaxLength(100)
            .IsRequired();

        transactionConfiguration.Property(t => t.MerchantReference)
            .HasMaxLength(100)
            .IsRequired();

        transactionConfiguration.Property(t => t.PaymentReference)
            .HasMaxLength(100)
            .IsRequired();

        transactionConfiguration.Property(t => t.PaymentId)
            .IsRequired();

        transactionConfiguration.Property(t => t.Amount)
            .HasPrecision(18, 2)
            .IsRequired();

        transactionConfiguration.Property(t => t.RemittanceAmount)
            .HasPrecision(18, 2)
            .IsRequired();

        transactionConfiguration.Property(t => t.ResponseCode)
            .HasMaxLength(10)
            .IsRequired();

        transactionConfiguration.Property(t => t.ResponseDescription)
            .HasMaxLength(500)
            .IsRequired();

        transactionConfiguration.Property(t => t.CardNumber)
            .HasMaxLength(50);

        transactionConfiguration.Property(t => t.RetrievalReferenceNumber)
            .HasMaxLength(50)
            .IsRequired();

        transactionConfiguration.Property(t => t.TransactionDate)
            .IsRequired();

        transactionConfiguration.Property(t => t.AccountNumber)
            .HasMaxLength(50);

        transactionConfiguration.Property(t => t.BankCode)
            .HasMaxLength(10)
            .IsRequired();

        transactionConfiguration.Property(t => t.Token)
            .HasMaxLength(200);

        transactionConfiguration.Property(t => t.CurrencyCode)
            .HasMaxLength(10)
            .IsRequired();

        transactionConfiguration.Property(t => t.Channel)
            .HasMaxLength(20)
            .IsRequired();

        transactionConfiguration.Property(t => t.MerchantCustomerId)
            .HasMaxLength(100);

        transactionConfiguration.Property(t => t.MerchantCustomerName)
            .HasMaxLength(200);

        transactionConfiguration.Property(t => t.Escrow)
            .IsRequired();

        transactionConfiguration.Property(t => t.NonCardProviderId)
            .HasMaxLength(50);

        transactionConfiguration.Property(t => t.PayableCode)
            .HasMaxLength(20)
            .IsRequired();

        transactionConfiguration.Property(t => t.Status)
            .IsRequired();

        transactionConfiguration.Property(t => t.CreatedAt)
            .IsRequired();

        transactionConfiguration.Property(t => t.UpdatedAt)
            .IsRequired();

        transactionConfiguration.Property(t => t.OrderId);

        // Configure indexes for performance
        transactionConfiguration.HasIndex(t => t.PaymentId)
            .IsUnique()
            .HasDatabaseName("IX_Transactions_PaymentId");

        transactionConfiguration.HasIndex(t => t.MerchantReference)
            .HasDatabaseName("IX_Transactions_MerchantReference");

        transactionConfiguration.HasIndex(t => t.PaymentReference)
            .HasDatabaseName("IX_Transactions_PaymentReference");

        transactionConfiguration.HasIndex(t => t.OrderId)
            .HasDatabaseName("IX_Transactions_OrderId");

        transactionConfiguration.HasIndex(t => t.TransactionReference)
            .HasDatabaseName("IX_Transactions_TransactionReference");

        transactionConfiguration.HasIndex(t => t.Status)
            .HasDatabaseName("IX_Transactions_Status");

        transactionConfiguration.HasIndex(t => t.CreatedAt)
            .HasDatabaseName("IX_Transactions_CreatedAt");

        transactionConfiguration.HasIndex(t => new { t.MerchantCustomerId, t.Status })
            .HasDatabaseName("IX_Transactions_MerchantCustomerId_Status");
    }
}
