namespace Ordering.Infrastructure.EntityConfigurations;

class OrderEntityTypeConfiguration : IEntityTypeConfiguration<Order>
{
    public void Configure(EntityTypeBuilder<Order> orderConfiguration)
    {
        orderConfiguration.ToTable("orders");

        orderConfiguration.Ignore(b => b.DomainEvents);

        orderConfiguration.Property(o => o.Id)
            .UseHiLo("orderseq");

        //Address value object persisted as owned entity type supported since EF Core 2.0
        orderConfiguration
            .OwnsOne(o => o.Address);

        orderConfiguration
            .Property(o => o.OrderStatus)
            .HasConversion<string>()
            .HasMaxLength(30);

        // PaymentId is nullable since we're using external payment processing (Interswitch)
        orderConfiguration
            .Property(o => o.PaymentId)
            .HasColumnName("PaymentMethodId")
            .IsRequired(false);

        // PaymentReference for Interswitch payments
        orderConfiguration
            .Property(o => o.PaymentReference)
            .HasMaxLength(100)
            .IsRequired(false);

        // MerchantReference for our generated transaction reference
        orderConfiguration
            .Property(o => o.MerchantReference)
            .HasMaxLength(100)
            .IsRequired(false);

        // Add unique constraint on MerchantReference to prevent duplicate orders
        orderConfiguration
            .HasIndex(o => o.MerchantReference)
            .IsUnique()
            .HasFilter("\"MerchantReference\" IS NOT NULL");

        orderConfiguration.HasOne(o => o.Buyer)
            .WithMany()
            .HasForeignKey(o => o.BuyerId);
    }
}
