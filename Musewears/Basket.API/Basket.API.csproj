<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Grpc.AspNetCore" Version="2.71.0" />
        <PackageReference Include="Grpc.AspNetCore.Web" Version="2.71.0" />
        <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.6" />
<!--        <PackageReference Include="StackExchange.Redis" Version="2.8.41" />-->
        <PackageReference Include="Aspire.StackExchange.Redis" Version="9.3.1" />
        <PackageReference Include="AspNetCore.HealthChecks.Redis" Version="9.0.0" />
    </ItemGroup>
    
    <ItemGroup>
        <Protobuf Include="Protos\basket.proto" GrpcServices="Server"/>
    </ItemGroup>
    
    <ItemGroup>
      <Folder Include="IntegrationEvents\Events\" />
    </ItemGroup>
    
    <ItemGroup>
      <Compile Remove="IntegrationEvents\Events\OrderStartedIntegrationEvent.cs" />
    </ItemGroup>
</Project>
