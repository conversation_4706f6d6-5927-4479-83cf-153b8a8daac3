using Grpc.Core.Interceptors;
using Microsoft.AspNetCore.Http;

namespace Basket.API.Interceptors;

public class AuthenticationInterceptor(IHttpContextAccessor httpContextAccessor) : Interceptor
{
    public override async Task<TResponse> UnaryServerHandler<TRequest, TResponse>(
        TRequest request,
        ServerCallContext context,
        UnaryServerMethod<TRequest, TResponse> continuation)
    {
        var httpContext = httpContextAccessor.HttpContext;
        if (httpContext?.User.Identity?.IsAuthenticated == true)
        {
            // Add user identity to gRPC context
            // var metadata = new Metadata
            // {
            //     { "user-id", httpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "" }
            // };
            context.RequestHeaders.Add("user-id", httpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "");
        }

        return await continuation(request, context);
    }
}