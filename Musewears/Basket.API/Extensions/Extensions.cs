using System.Text.Json.Serialization;
using Basket.API.Interceptors;
using Basket.API.Repositories;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace Basket.API.Extensions;

public static class Extensions
{
    public static void AddBasketServices(this IHostApplicationBuilder builder)
    {
        
        // Only use Aspire in Development when AppHost is running
        if (builder.Environment.IsDevelopment())
        {
            builder.AddRedisClient("redis");
        }
        else
        {
            var connectionString = builder.Configuration.GetConnectionString("redis");
            
            builder.Services.AddStackExchangeRedisCache(options =>
            {
                options.Configuration = connectionString;
                options.InstanceName  = "Basket:";     // optional prefix for all cache keys
            });
        
            builder.Services.AddSingleton<IConnectionMultiplexer>(sp =>
                ConnectionMultiplexer.Connect(
                    connectionString
                )
            );
        }
        
        builder.Services.AddSingleton<IBasketRepository, RedisBasketRepository>();
        
        
        // builder.Services.AddScoped<AuthenticationInterceptor>();
        
        // Add services to the container.
        builder.Services.AddGrpc(options =>
            {
                options.EnableDetailedErrors = true;
                
                // options.Interceptors.Add<AuthenticationInterceptor>(); // Add authentication interceptor
            })
            // .AddJsonTranscoding()
            ;
    }
    
    public static IApplicationBuilder UseBasketApi(this WebApplication app)
    {
        ArgumentNullException.ThrowIfNull(app);
        
        // Configure middleware
        app.UseGrpcWeb();

        // app.MapDefaultEndpoints();

        // Configure the HTTP request pipeline.
        app.MapGrpcService<BasketService>()
            .EnableGrpcWeb(); // ← Add this

        return app;
    }
}

// [JsonSerializable(typeof(OrderStartedIntegrationEvent))]
// partial class IntegrationEventContext : JsonSerializerContext
// {
//
// }
