using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Musewears.Server.Controllers;
using Musewears.Server.Models;
using Musewears.Server.Services;
using Musewears.Shared.Models;
using Newtonsoft.Json;
using System.Text;
using Xunit;

namespace Server.UnitTests.Controllers;

public class InterswitchWebhookControllerTests
{
    private readonly Mock<IWardrobe> _mockWardrobe;
    private readonly Mock<IOrderingIntegrationService> _mockOrderingIntegrationService;
    private readonly Mock<IWebhookMetricsService> _mockWebhookMetricsService;
    private readonly Mock<ILogger<InterswitchWebhookController>> _mockLogger;
    private readonly InterswitchWebhookController _controller;

    public InterswitchWebhookControllerTests()
    {
        _mockWardrobe = new Mock<IWardrobe>();
        _mockOrderingIntegrationService = new Mock<IOrderingIntegrationService>();
        _mockWebhookMetricsService = new Mock<IWebhookMetricsService>();
        _mockLogger = new Mock<ILogger<InterswitchWebhookController>>();

        _controller = new InterswitchWebhookController(
            _mockWardrobe.Object,
            _mockOrderingIntegrationService.Object,
            _mockWebhookMetricsService.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task HandleInterswitchNotification_Should_Return_BadRequest_When_Signature_Header_Missing()
    {
        // Arrange
        var httpContext = new DefaultHttpContext();
        var requestBody = "{}";
        httpContext.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes(requestBody));
        _controller.ControllerContext = new ControllerContext { HttpContext = httpContext };

        // Act
        var result = await _controller.HandleInterswitchNotification();

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.Should().Be("Missing X-Interswitch-Signature header.");
    }

    [Fact]
    public async Task HandleInterswitchNotification_Should_Return_Unauthorized_When_Signature_Invalid()
    {
        // Arrange
        Environment.SetEnvironmentVariable("INTERSWITCH_SECRET_KEY", "test-secret-key");
        
        var httpContext = new DefaultHttpContext();
        var requestBody = JsonConvert.SerializeObject(new { test = "data" });
        httpContext.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes(requestBody));
        httpContext.Request.Headers["X-Interswitch-Signature"] = "invalid-signature";
        _controller.ControllerContext = new ControllerContext { HttpContext = httpContext };

        // Act
        var result = await _controller.HandleInterswitchNotification();

        // Assert
        result.Should().BeOfType<UnauthorizedResult>();
    }

    [Fact]
    public async Task HandleInterswitchNotification_Should_Process_Transaction_Created_Event()
    {
        // Arrange
        var secretKey = "test-secret-key";
        Environment.SetEnvironmentVariable("INTERSWITCH_SECRET_KEY", secretKey);

        var transactionEvent = new TransactionEvent
        {
            Event = "TRANSACTION.CREATED",
            Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
            Data = new TransactionEventData
            {
                PaymentReference = "PAY123456",
                PaymentId = 789,
                Amount = 100.00m,
                ResponseCode = "00", // Approved
                MerchantCustomerId = "user123",
                MerchantCustomerName = "Test User",
                OrderId = "ORDER123",
                UserId = "user123",
                CreatedAt = DateTime.UtcNow
            }
        };

        var requestBody = JsonConvert.SerializeObject(transactionEvent);
        var signature = ComputeHmacSha512(requestBody, secretKey);

        var httpContext = new DefaultHttpContext();
        httpContext.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes(requestBody));
        httpContext.Request.Headers["X-Interswitch-Signature"] = signature;
        _controller.ControllerContext = new ControllerContext { HttpContext = httpContext };

        _mockOrderingIntegrationService.Setup(x => x.ProcessTransactionCreated(It.IsAny<TransactionEvent>()))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.HandleInterswitchNotification();

        // Assert
        result.Should().BeOfType<OkResult>();

        // Verify the transaction created method was called
        _mockOrderingIntegrationService.Verify(x => x.ProcessTransactionCreated(
            It.Is<TransactionEvent>(e => e.Data.PaymentReference == "PAY123456")), Times.Once);
    }

    [Fact]
    public async Task HandleInterswitchNotification_Should_Process_Transaction_Updated_Event()
    {
        // Arrange
        var secretKey = "test-secret-key";
        Environment.SetEnvironmentVariable("INTERSWITCH_SECRET_KEY", secretKey);

        var transactionEvent = new TransactionEvent
        {
            Event = "TRANSACTION.UPDATED",
            Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
            Data = new TransactionEventData
            {
                PaymentReference = "PAY123456",
                PaymentId = 789,
                Amount = 100.00m,
                ResponseCode = "09", // In Progress
                UserId = "user123",
                CreatedAt = DateTime.UtcNow
            }
        };

        var requestBody = JsonConvert.SerializeObject(transactionEvent);
        var signature = ComputeHmacSha512(requestBody, secretKey);

        var httpContext = new DefaultHttpContext();
        httpContext.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes(requestBody));
        httpContext.Request.Headers["X-Interswitch-Signature"] = signature;
        _controller.ControllerContext = new ControllerContext { HttpContext = httpContext };

        _mockOrderingIntegrationService.Setup(x => x.ProcessTransactionUpdated(It.IsAny<TransactionEvent>()))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.HandleInterswitchNotification();

        // Assert
        result.Should().BeOfType<OkResult>();

        _mockOrderingIntegrationService.Verify(x => x.ProcessTransactionUpdated(
            It.Is<TransactionEvent>(e => e.Data.PaymentReference == "PAY123456")), Times.Once);
    }

    [Fact]
    public async Task HandleInterswitchNotification_Should_Process_Payment_Link_Success_Event()
    {
        // Arrange
        var secretKey = "test-secret-key";
        Environment.SetEnvironmentVariable("INTERSWITCH_SECRET_KEY", secretKey);

        var paymentLinkEvent = new PaymentLinkEvent
        {
            Event = "LINK.TRANSACTION_SUCCESSFUL",
            Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
            Data = new PaymentLinkEventData
            {
                Reference = "LINK123456",
                CustomerEmail = "<EMAIL>",
                CustomerName = "Test Customer",
                Amount = 150.00m,
                ResponseCode = "00"
            }
        };

        var requestBody = JsonConvert.SerializeObject(paymentLinkEvent);
        var signature = ComputeHmacSha512(requestBody, secretKey);

        var httpContext = new DefaultHttpContext();
        httpContext.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes(requestBody));
        httpContext.Request.Headers["X-Interswitch-Signature"] = signature;
        _controller.ControllerContext = new ControllerContext { HttpContext = httpContext };

        _mockOrderingIntegrationService.Setup(x => x.UpdateOrderStatusFromPaymentAsync(
            It.IsAny<string>(), It.IsAny<PaymentStatus>()))
            .ReturnsAsync(true);
        _mockOrderingIntegrationService.Setup(x => x.AssociateBuyerWithOrderAsync(
            It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.HandleInterswitchNotification();

        // Assert
        result.Should().BeOfType<OkResult>();
        
        _mockOrderingIntegrationService.Verify(x => x.UpdateOrderStatusFromPaymentAsync(
            "LINK123456", PaymentStatus.completed), Times.Once);
        
        _mockOrderingIntegrationService.Verify(x => x.AssociateBuyerWithOrderAsync(
            "<EMAIL>", "Test Customer", "LINK123456", ""), Times.Once);
    }

    [Fact]
    public async Task HandleInterswitchNotification_Should_Handle_Test_Stop_Event()
    {
        // Arrange
        var secretKey = "test-secret-key";
        Environment.SetEnvironmentVariable("INTERSWITCH_SECRET_KEY", secretKey);

        var testEvent = new { @event = "TEST.STOP" };
        var requestBody = JsonConvert.SerializeObject(testEvent);
        var signature = ComputeHmacSha512(requestBody, secretKey);

        var httpContext = new DefaultHttpContext();
        httpContext.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes(requestBody));
        httpContext.Request.Headers["X-Interswitch-Signature"] = signature;
        _controller.ControllerContext = new ControllerContext { HttpContext = httpContext };

        // Act
        var result = await _controller.HandleInterswitchNotification();

        // Assert
        result.Should().BeOfType<OkResult>();
    }

    private static string ComputeHmacSha512(string data, string key)
    {
        using var hmac = new System.Security.Cryptography.HMACSHA512(Encoding.UTF8.GetBytes(key));
        var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
        return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
    }
}
