# Server Unit Tests - Interswitch Webhook Integration

This test project contains comprehensive unit tests for the Interswitch webhook integration functionality, including buyer association, payment method handling, and order status updates.

## Test Coverage

### 1. OrderingIntegrationService Tests (`Services/OrderingIntegrationServiceTests.cs`)

Tests the bridge service between the Server project and Ordering project:

- **Buyer Management**
  - `CreateOrFindBuyerAsync_Should_Return_Existing_Buyer_Id_When_Buyer_Exists`
  - `CreateOrFindBuyerAsync_Should_Create_New_Buyer_When_Not_Exists`
  - `CreateOrFindBuyerAsync_Should_Return_Null_When_Exception_Occurs`

- **Payment Status Mapping**
  - `MapPaymentStatusToOrderStatus_Should_Map_Correctly` (Theory test with multiple scenarios)

- **Order Association**
  - `AssociateBuyerWithOrderAsync_Should_Return_False_When_Order_Not_Found`
  - `AssociateBuyerWithOrderAsync_Should_Return_False_When_Buyer_Creation_Fails`

- **Order Status Updates**
  - `UpdateOrderStatusFromPaymentAsync_Should_Return_False_When_Order_Not_Found`
  - `UpdateOrderStatusFromPaymentAsync_Should_Update_Server_Order_Status`

- **Payment Method Verification**
  - `SetPaymentMethodVerifiedAsync_Should_Log_Operation`

### 2. InterswitchWebhookController Tests (`Controllers/InterswitchWebhookControllerTests.cs`)

Tests the webhook endpoint that processes Interswitch notifications:

- **Security & Validation**
  - `HandleInterswitchNotification_Should_Return_BadRequest_When_Signature_Header_Missing`
  - `HandleInterswitchNotification_Should_Return_Unauthorized_When_Signature_Invalid`

- **Transaction Events**
  - `HandleInterswitchNotification_Should_Process_Transaction_Created_Event`
  - `HandleInterswitchNotification_Should_Process_Transaction_Updated_Event`

- **Payment Link Events**
  - `HandleInterswitchNotification_Should_Process_Payment_Link_Success_Event`

- **Test Events**
  - `HandleInterswitchNotification_Should_Handle_Test_Stop_Event`

## Test Scenarios Covered

### Payment Status Mapping
- ✅ `completed` → `Paid` (Ordering) / `confirmed` (Server)
- ✅ `pending` → `AwaitingValidation` (Ordering) / `pending` (Server)
- ✅ `failed` → `Cancelled` (Ordering) / `cancelled` (Server)
- ✅ `refunded` → `Cancelled` (Ordering) / `cancelled` (Server)
- ✅ `incomplete` → `Submitted` (Ordering) / `incomplete` (Server)

### Webhook Event Types
- ✅ `TRANSACTION.CREATED` - Creates buyer association and payment method
- ✅ `TRANSACTION.UPDATED` - Updates order status
- ✅ `TRANSACTION.COMPLETED` - Finalizes payment and order status
- ✅ `LINK.TRANSACTION_SUCCESSFUL` - Handles payment link success
- ✅ `LINK.TRANSACTION_FAILURE` - Handles payment link failure
- ✅ `TEST.STOP` - Test event handling

### Security Features
- ✅ HMAC-SHA512 signature verification
- ✅ Missing signature header handling
- ✅ Invalid signature rejection

## Running the Tests

### Option 1: Using the provided scripts
```bash
# On Linux/macOS
./run-interswitch-tests.sh

# On Windows
run-interswitch-tests.bat
```

### Option 2: Using dotnet CLI directly
```bash
# Navigate to the test project
cd Musewears/Server.UnitTests

# Restore packages
dotnet restore

# Run tests
dotnet test

# Run with detailed output
dotnet test --verbosity normal --logger "console;verbosity=detailed"
```

### Option 3: Using Visual Studio
1. Open the solution in Visual Studio
2. Build the solution
3. Open Test Explorer (Test → Test Explorer)
4. Run all tests or specific test classes

## Test Dependencies

The tests use the following frameworks and libraries:
- **xUnit** - Test framework
- **FluentAssertions** - Assertion library for readable tests
- **Moq** - Mocking framework for dependencies
- **Microsoft.AspNetCore.Mvc.Testing** - ASP.NET Core testing utilities
- **Newtonsoft.Json** - JSON serialization for webhook payloads

## Mock Objects

The tests extensively use mocks for:
- `IBuyerRepository` - Buyer data access
- `IOrderRepository` - Order data access  
- `IWardrobe` - Server project data access
- `ILogger<T>` - Logging services
- `IUnitOfWork` - Database transaction handling

## Integration Points Tested

1. **Webhook → OrderService** - Payment status updates
2. **Webhook → OrderingIntegrationService** - Buyer association and order status mapping
3. **OrderingIntegrationService → Repositories** - Data persistence
4. **Server Orders ↔ Ordering Orders** - Status synchronization

## Expected Test Results

When all tests pass, you can be confident that:
- ✅ Webhook signature verification works correctly
- ✅ Buyer association is restored and functional
- ✅ Payment methods support both card and Interswitch payments
- ✅ Order statuses are properly mapped between systems
- ✅ Error handling is robust and logged appropriately
- ✅ The integration between Server and Ordering projects is working

## Troubleshooting

If tests fail, check:
1. **Environment Variables** - Ensure `INTERSWITCH_SECRET_KEY` is set for signature tests
2. **Project References** - Verify all project dependencies are correctly referenced
3. **Package Versions** - Ensure all NuGet packages are compatible
4. **Database Context** - In-memory database setup for repository tests

## Future Test Enhancements

Consider adding:
- Integration tests with real database
- Performance tests for webhook processing
- End-to-end tests with actual Interswitch payloads
- Load testing for concurrent webhook processing
