using FluentAssertions;
using MediatR;
using Microsoft.Extensions.Logging;
using Moq;
using Musewears.Server.Services;
using Musewears.Shared.Models;
using Ordering.API.Application.Commands;
using Xunit;

namespace Server.UnitTests.Services;

public class OrderingIntegrationServiceTests
{
    private readonly Mock<IMediator> _mockMediator;
    private readonly Mock<ILogger<OrderingIntegrationService>> _mockLogger;
    private readonly OrderingIntegrationService _service;

    public OrderingIntegrationServiceTests()
    {
        _mockMediator = new Mock<IMediator>();
        _mockLogger = new Mock<ILogger<OrderingIntegrationService>>();

        _service = new OrderingIntegrationService(
            _mockMediator.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task AssociateBuyerWithOrderAsync_Should_Send_ProcessInterswitchPaymentCommand()
    {
        // Arrange
        var userId = "test-user-123";
        var userName = "Test User";
        var paymentReference = "PAY123456";
        var orderId = "ORDER123";

        _mockMediator.Setup(x => x.Send(It.IsAny<ProcessInterswitchPaymentCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _service.AssociateBuyerWithOrderAsync(userId, userName, paymentReference, orderId);

        // Assert
        result.Should().BeTrue();
        _mockMediator.Verify(x => x.Send(
            It.Is<ProcessInterswitchPaymentCommand>(cmd => 
                cmd.PaymentReference == paymentReference &&
                cmd.UserId == userId &&
                cmd.UserName == userName &&
                cmd.PaymentStatus == "pending" &&
                cmd.MerchantCustomerId == userId &&
                cmd.MerchantCustomerName == userName), 
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task AssociateBuyerWithOrderAsync_Should_Return_False_When_Command_Fails()
    {
        // Arrange
        var userId = "test-user-456";
        var userName = "Test User";
        var paymentReference = "PAY123456";
        var orderId = "ORDER123";

        _mockMediator.Setup(x => x.Send(It.IsAny<ProcessInterswitchPaymentCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var result = await _service.AssociateBuyerWithOrderAsync(userId, userName, paymentReference, orderId);

        // Assert
        result.Should().BeFalse();
        _mockMediator.Verify(x => x.Send(It.IsAny<ProcessInterswitchPaymentCommand>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task UpdateOrderStatusFromPaymentAsync_Should_Send_ProcessInterswitchPaymentCommand()
    {
        // Arrange
        var paymentReference = "PAY123456";
        var paymentStatus = PaymentStatus.completed;

        _mockMediator.Setup(x => x.Send(It.IsAny<ProcessInterswitchPaymentCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _service.UpdateOrderStatusFromPaymentAsync(paymentReference, paymentStatus);

        // Assert
        result.Should().BeTrue();
        _mockMediator.Verify(x => x.Send(
            It.Is<ProcessInterswitchPaymentCommand>(cmd => 
                cmd.PaymentReference == paymentReference &&
                cmd.PaymentStatus == paymentStatus.ToString()), 
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task UpdateOrderStatusFromPaymentAsync_Should_Return_False_When_Command_Fails()
    {
        // Arrange
        var paymentReference = "PAY123456";
        var paymentStatus = PaymentStatus.failed;

        _mockMediator.Setup(x => x.Send(It.IsAny<ProcessInterswitchPaymentCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var result = await _service.UpdateOrderStatusFromPaymentAsync(paymentReference, paymentStatus);

        // Assert
        result.Should().BeFalse();
        _mockMediator.Verify(x => x.Send(It.IsAny<ProcessInterswitchPaymentCommand>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task SetPaymentMethodVerifiedAsync_Should_Send_ProcessInterswitchPaymentCommand()
    {
        // Arrange
        var paymentReference = "PAY123456";
        var buyerId = 42;
        var paymentMethodId = 789;

        _mockMediator.Setup(x => x.Send(It.IsAny<ProcessInterswitchPaymentCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _service.SetPaymentMethodVerifiedAsync(paymentReference, buyerId, paymentMethodId);

        // Assert
        result.Should().BeTrue();
        _mockMediator.Verify(x => x.Send(
            It.Is<ProcessInterswitchPaymentCommand>(cmd => 
                cmd.PaymentReference == paymentReference &&
                cmd.PaymentStatus == "completed" &&
                cmd.PaymentId == paymentMethodId), 
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task CreateOrFindBuyerAsync_Should_Return_Placeholder_Value()
    {
        // Arrange
        var userId = "test-user-123";
        var userName = "Test User";

        // Act
        var result = await _service.CreateOrFindBuyerAsync(userId, userName);

        // Assert
        result.Should().Be(1); // Placeholder value since actual buyer creation is handled by domain events
    }

    [Theory]
    [InlineData(PaymentStatus.completed, Ordering.Domain.AggregatesModel.OrderAggregate.OrderStatus.Paid)]
    [InlineData(PaymentStatus.pending, Ordering.Domain.AggregatesModel.OrderAggregate.OrderStatus.AwaitingValidation)]
    [InlineData(PaymentStatus.failed, Ordering.Domain.AggregatesModel.OrderAggregate.OrderStatus.Cancelled)]
    [InlineData(PaymentStatus.refunded, Ordering.Domain.AggregatesModel.OrderAggregate.OrderStatus.Cancelled)]
    [InlineData(PaymentStatus.incomplete, Ordering.Domain.AggregatesModel.OrderAggregate.OrderStatus.Submitted)]
    public void MapPaymentStatusToOrderStatus_Should_Map_Correctly(PaymentStatus paymentStatus, Ordering.Domain.AggregatesModel.OrderAggregate.OrderStatus expectedOrderStatus)
    {
        // Act
        var result = _service.MapPaymentStatusToOrderStatus(paymentStatus);

        // Assert
        result.Should().Be(expectedOrderStatus);
    }

    [Fact]
    public async Task AssociateBuyerWithOrderAsync_Should_Handle_Exception_Gracefully()
    {
        // Arrange
        var userId = "test-user-123";
        var userName = "Test User";
        var paymentReference = "PAY123456";
        var orderId = "ORDER123";

        _mockMediator.Setup(x => x.Send(It.IsAny<ProcessInterswitchPaymentCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test exception"));

        // Act
        var result = await _service.AssociateBuyerWithOrderAsync(userId, userName, paymentReference, orderId);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task UpdateOrderStatusFromPaymentAsync_Should_Handle_Exception_Gracefully()
    {
        // Arrange
        var paymentReference = "PAY123456";
        var paymentStatus = PaymentStatus.completed;

        _mockMediator.Setup(x => x.Send(It.IsAny<ProcessInterswitchPaymentCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test exception"));

        // Act
        var result = await _service.UpdateOrderStatusFromPaymentAsync(paymentReference, paymentStatus);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task SetPaymentMethodVerifiedAsync_Should_Handle_Exception_Gracefully()
    {
        // Arrange
        var paymentReference = "PAY123456";
        var buyerId = 42;
        var paymentMethodId = 789;

        _mockMediator.Setup(x => x.Send(It.IsAny<ProcessInterswitchPaymentCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test exception"));

        // Act
        var result = await _service.SetPaymentMethodVerifiedAsync(paymentReference, buyerId, paymentMethodId);

        // Assert
        result.Should().BeFalse();
    }
}
