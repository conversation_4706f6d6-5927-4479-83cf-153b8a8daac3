<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
        <PackageReference Include="xunit" Version="2.9.2" />
        <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="coverlet.collector" Version="6.0.2">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="FluentAssertions" Version="7.0.0" />
        <PackageReference Include="Moq" Version="4.20.72" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.6" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.6" />
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.6" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.5" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Server\Musewears.Server.csproj" />
        <ProjectReference Include="..\Ordering\Ordering.Domain\Ordering.Domain.csproj" />
        <ProjectReference Include="..\Ordering\Ordering.Infrastructure\Ordering.Infrastructure.csproj" />
    </ItemGroup>

</Project>
